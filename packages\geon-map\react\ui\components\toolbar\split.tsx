"use client";

import { cn } from "@geon-ui/react/lib/utils";
import { Popover, PopoverTrigger } from "@geon-ui/react/primitives/popover";
import {
  Columns2,
  Columns3,
  Grid2X2,
  SquareSplitHorizontal,
} from "lucide-react";
import * as React from "react";

import { Button } from "./base/button";
import {
  ToolbarContent,
  ToolbarItem,
  ToolbarTrigger,
} from "./base/toolbar-item";

export type SplitCount = 1 | 2 | 3 | 4;

export interface SplitMode {
  count: SplitCount;
  enableSync?: boolean;
}

// Context for ToolbarSplit
interface SplitContextValue {
  value?: SplitMode;
  onValueChange?: (splitMode: SplitMode) => void;
  currentValue: SplitMode;
  disabled?: boolean;
}

const SplitContext = React.createContext<SplitContextValue | null>(null);

const useSplitContext = () => {
  const context = React.useContext(SplitContext);
  if (!context) {
    throw new Error("ToolbarSplit components must be used within ToolbarSplit");
  }
  return context;
};

// Props for compound components
export interface ToolbarSplitProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, "defaultValue"> {
  /** 현재 분할 모드 */
  value?: SplitMode;
  /** 분할 모드 변경 콜백 */
  onValueChange?: (splitMode: SplitMode) => void;
  /** 기본 분할 모드 */
  defaultValue?: SplitMode;
  /** 비활성화 */
  disabled?: boolean;
}

export interface ToolbarSplitTriggerProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 툴팁 텍스트 */
  tooltip?: string;
  /** 버튼 크기 */
  size?: "sm" | "lg" | "default" | "icon";
}

export interface ToolbarSplitContentProps
  extends React.HTMLAttributes<HTMLDivElement> {
  /** 기본 헤더 표시 여부 */
}

export interface ToolbarSplitOptionProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 분할 개수 */
  count: SplitCount;
}

// 분할 옵션 정보
const SPLIT_OPTIONS = {
  1: {
    icon: SquareSplitHorizontal,
    label: "단일 화면",
    description: "하나의 지도 화면",
  },
  2: {
    icon: Columns2,
    label: "2분할 화면",
    description: "좌우 분할 지도",
  },
  3: {
    icon: Columns3,
    label: "3분할 화면",
    description: "좌우 분할 지도",
  },
  4: {
    icon: Grid2X2,
    label: "4분할 화면",
    description: "2x2 격자 지도",
  },
} as const;

// Main ToolbarSplit Container (Context Provider)
export const ToolbarSplit = React.forwardRef<HTMLDivElement, ToolbarSplitProps>(
  (
    {
      value,
      onValueChange,
      defaultValue = { count: 1, enableSync: true },
      disabled = false,
      className,
      children,
      ...props
    },
    ref,
  ) => {
    // 내부 상태 관리 (제어되지 않는 컴포넌트)
    const [internalValue, setInternalValue] =
      React.useState<SplitMode>(defaultValue);

    // 현재 값 (제어된 vs 제어되지 않은)
    const currentValue = value ?? internalValue;

    // 값 변경 핸들러
    const handleValueChange = React.useCallback(
      (newSplitMode: SplitMode) => {
        if (disabled) return;

        if (!value) {
          setInternalValue(newSplitMode);
        }
        onValueChange?.(newSplitMode);
      },
      [value, onValueChange, disabled],
    );

    const contextValue = React.useMemo(
      () => ({
        value,
        onValueChange: handleValueChange,
        currentValue,
        disabled,
      }),
      [value, handleValueChange, currentValue, disabled],
    );

    return (
      <SplitContext.Provider value={contextValue}>
        <ToolbarItem ref={ref} className={className} {...props}>
          <Popover>
            <PopoverTrigger asChild>
              {React.Children.toArray(children).find(
                (child) =>
                  React.isValidElement(child) &&
                  child.type === ToolbarSplitTrigger,
              )}
            </PopoverTrigger>
            {React.Children.toArray(children).find(
              (child) =>
                React.isValidElement(child) &&
                child.type === ToolbarSplitContent,
            )}
          </Popover>
        </ToolbarItem>
      </SplitContext.Provider>
    );
  },
);

ToolbarSplit.displayName = "ToolbarSplit";

// ToolbarSplitTrigger Component
export const ToolbarSplitTrigger = React.forwardRef<
  HTMLButtonElement,
  ToolbarSplitTriggerProps
>(
  (
    { tooltip = "지도 분할", size = "default", className, children, ...props },
    ref,
  ) => {
    const { currentValue, disabled } = useSplitContext();

    // 기본 아이콘 (children이 없을 때)
    const CurrentIcon = SPLIT_OPTIONS[currentValue.count].icon;

    return (
      <ToolbarTrigger
        ref={ref}
        tooltip={tooltip}
        size={size}
        disabled={disabled}
        active={currentValue.count > 1}
        className={className}
        {...props}
      >
        {children || <CurrentIcon className="h-4 w-4" />}
      </ToolbarTrigger>
    );
  },
);

ToolbarSplitTrigger.displayName = "ToolbarSplitTrigger";

// ToolbarSplitContent Component
export const ToolbarSplitContent = React.forwardRef<
  HTMLDivElement,
  ToolbarSplitContentProps
>(({ className, children, ...props }, ref) => {
  return (
    <ToolbarContent
      ref={ref}
      align="center"
      className={cn("w-fit flex flex-row justify-center gap-2 p-3", className)}
      {...props}
    >
      {children}
    </ToolbarContent>
  );
});

ToolbarSplitContent.displayName = "ToolbarSplitContent";

// ToolbarSplitOption Component
export const ToolbarSplitOption = React.forwardRef<
  HTMLButtonElement,
  ToolbarSplitOptionProps
>(({ count, className, children, ...props }, ref) => {
  const { currentValue, onValueChange, disabled } = useSplitContext();

  const option = SPLIT_OPTIONS[count];
  const Icon = option.icon;
  const isActive = currentValue.count === count;

  const handleClick = () => {
    const newSplitMode: SplitMode = {
      ...currentValue,
      count,
    };
    onValueChange?.(newSplitMode);
  };

  return (
    <Button
      ref={ref}
      variant="ghost"
      size="sm"
      active={isActive}
      disabled={disabled}
      onClick={handleClick}
      className={cn(
        "flex flex-col items-center justify-center h-16 w-16 p-2",
        className,
      )}
      title={option.description}
      {...props}
    >
      {children || (
        <>
          <Icon className="h-5 w-5 mb-1" />
          <span className="text-xs font-medium">{count}분할</span>
        </>
      )}
    </Button>
  );
});

ToolbarSplitOption.displayName = "ToolbarSplitOption";

// Hook for using split functionality outside toolbar
export function useSplitMode(
  initialValue: SplitMode = { count: 1, enableSync: true },
) {
  const [splitMode, setSplitMode] = React.useState<SplitMode>(initialValue);

  const updateSplitMode = React.useCallback((newMode: SplitMode) => {
    setSplitMode(newMode);
  }, []);

  const updateCount = React.useCallback((count: SplitCount) => {
    setSplitMode((prev) => ({ ...prev, count }));
  }, []);

  const updateSync = React.useCallback((enableSync: boolean) => {
    setSplitMode((prev) => ({ ...prev, enableSync }));
  }, []);

  return {
    splitMode,
    setSplitMode: updateSplitMode,
    setCount: updateCount,
    setSync: updateSync,
  };
}

// 완성형 위젯 제거 - 컴파운드 패턴으로 통일
