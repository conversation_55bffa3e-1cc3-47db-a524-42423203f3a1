"use client";

import { ScrollArea } from "@geon-ui/react/primitives/scroll-area";
import React from "react";

import type {
  ColumnInfo,
  DynamicTableProps,
  FormattedValue,
} from "../../_types/dynamic-table";

/**
 * 컬럼 타입에 따른 값 포맷팅
 */
function formatColumnValue(value: any, columnType: string): FormattedValue {
  if (value === null || value === undefined) {
    return { value: "-", rawValue: value };
  }

  const upperType = columnType.toUpperCase();

  // 날짜 타입
  if (upperType.includes("TIMESTAMP") || upperType.includes("DATE")) {
    try {
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        return {
          value: date.toLocaleDateString("ko-KR", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
          }),
          rawValue: value,
        };
      }
    } catch {
      // 날짜 파싱 실패시 원본 값 사용
    }
  }

  // 숫자 타입
  if (
    upperType.includes("INTEGER") ||
    upperType.includes("NUMERIC") ||
    upperType.includes("BIGINT")
  ) {
    const num = Number(value);
    if (!isNaN(num)) {
      return {
        value: num.toLocaleString("ko-KR"),
        rawValue: value,
      };
    }
  }

  // YN 플래그
  if (upperType === "CHARACTER" && (value === "Y" || value === "N")) {
    return {
      value: value === "Y" ? "예" : "아니오",
      rawValue: value,
    };
  }

  // 지오메트리 타입
  if (upperType === "USER_DEFINED" || upperType.includes("GEOM")) {
    return {
      value: "[지오메트리 데이터]",
      rawValue: value,
    };
  }

  // 기본 문자열
  return {
    value: String(value),
    rawValue: value,
  };
}

/**
 * 동적 테이블 컴포넌트
 */
export function DynamicTable({
  schema,
  data,
  loading = false,
  excludeColumns = [],
  columnRenderers = {},
  className = "",
}: DynamicTableProps) {
  if (loading) {
    return (
      <div className="flex h-[400px] items-center justify-center">
        <div className="text-gray-500">로딩 중...</div>
      </div>
    );
  }

  // ordinalPosition 순으로 정렬하고 제외할 컬럼 필터링
  const displayColumns = schema.columns
    .filter((col) => !excludeColumns.includes(col.columnName))
    .sort((a, b) => a.ordinalPosition - b.ordinalPosition);

  // 2열씩 배치하기 위해 페어 생성
  const columnPairs: Array<[ColumnInfo, ColumnInfo | undefined]> = [];
  for (let i = 0; i < displayColumns.length; i += 2) {
    const firstColumn = displayColumns[i]!; // i는 항상 유효한 인덱스
    const secondColumn =
      i + 1 < displayColumns.length ? displayColumns[i + 1]! : undefined;
    columnPairs.push([firstColumn, secondColumn]);
  }

  return (
    <ScrollArea className="h-[400px]">
      <div className="space-y-4 p-6">
        <table
          className={`w-full border-collapse border border-gray-300 ${className}`}
        >
          <tbody>
            {columnPairs.map((pair, index) => (
              <tr key={index}>
                {/* 첫 번째 컬럼 */}
                <td className="w-1/6 border border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700">
                  {pair[0].columnComment || pair[0].columnName}
                </td>
                {pair[1] ? (
                  // 두 번째 컬럼이 있는 경우 - 일반적인 2열 레이아웃
                  <>
                    <td className="w-1/6 border border-gray-300 px-3 py-2 text-sm text-gray-900">
                      {(() => {
                        const renderer = columnRenderers?.[pair[0].columnName];
                        return renderer
                          ? renderer(data[pair[0].columnName])
                          : formatColumnValue(
                              data[pair[0].columnName],
                              pair[0].columnType,
                            ).value;
                      })()}
                    </td>
                    <td className="w-1/6 border border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700">
                      {pair[1]!.columnComment || pair[1]!.columnName}
                    </td>
                    <td className="w-3/6 border border-gray-300 px-3 py-2 text-sm text-gray-900">
                      {(() => {
                        const renderer = columnRenderers?.[pair[1]!.columnName];
                        return renderer
                          ? renderer(data[pair[1]!.columnName])
                          : formatColumnValue(
                              data[pair[1]!.columnName],
                              pair[1]!.columnType,
                            ).value;
                      })()}
                    </td>
                  </>
                ) : (
                  // 홀수 개인 경우 마지막 컬럼 - 데이터가 나머지 3칸을 모두 차지
                  <td
                    className="border border-gray-300 px-3 py-2 text-sm text-gray-900"
                    colSpan={3}
                  >
                    {(() => {
                      const renderer = columnRenderers?.[pair[0].columnName];
                      return renderer
                        ? renderer(data[pair[0].columnName])
                        : formatColumnValue(
                            data[pair[0].columnName],
                            pair[0].columnType,
                          ).value;
                    })()}
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </ScrollArea>
  );
}

/**
 * 스키마 응답에서 테이블 컴포넌트 생성하는 헬퍼 함수
 */
export function createTableFromSchema(
  schemaResponse: any,
  data: Record<string, any>,
  options?: {
    excludeColumns?: string[];
    columnRenderers?: Record<string, (value: any) => React.ReactNode>;
    className?: string;
  },
) {
  if (!schemaResponse?.result) {
    return <div className="text-red-500">스키마 정보가 없습니다.</div>;
  }

  return (
    <DynamicTable
      schema={schemaResponse.result}
      data={data}
      excludeColumns={options?.excludeColumns}
      columnRenderers={options?.columnRenderers}
      className={options?.className}
    />
  );
}
