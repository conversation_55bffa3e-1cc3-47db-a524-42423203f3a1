import { useDraggable } from "@dnd-kit/core";
import { DialogHeader } from "@geon-ui/react/primitives/dialog";
import { ComponentProps } from "react";

export default function DraggableDialogHeader({
  children,
  id,
  ...props
}: ComponentProps<typeof DialogHeader>) {
  const { attributes, listeners, setNodeRef } = useDraggable({
    id: id || "draggable-dialog-header",
  });
  return (
    <DialogHeader
      className="cursor-move select-none"
      {...props}
      ref={setNodeRef}
      {...attributes}
      {...listeners}
    >
      {children}
    </DialogHeader>
  );
}
