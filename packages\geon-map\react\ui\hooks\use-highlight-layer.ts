"use client";
import {useFeature, useLayer} from "@geon-map/react-odf";
import { useCallback, useEffect, useState } from "react";

type HighlightOptions = {
  isFitToLayer?: boolean;
  srid?: string;
  clear?: boolean;
  style?: any;
};
type UseHighlightLayerProps = {
  style?: any;
};

export function useHighlightLayer(props: UseHighlightLayerProps = {}) {
  const { style } = props;
  const {
    addLayer,
    addFeature,
    clearFeatures,
    fitToLayer,
    setMaxZIndex,
    updateLayerStyle,
  } = useLayer();
  const { fromWKT } = useFeature();
  const [layerId, setLayerId] = useState<string | null>(null);

  useEffect(() => {
    addLayer({ type: "empty" }).then((id: string | void) => {
      if (id) setLayerId(id);
    });
  }, [addLayer]);

  useEffect(() => {
    if (layerId) {
      if(style){
        updateLayerStyle(layerId, style);
      } else{
        // 기본 스타일 적용
        updateLayerStyle(layerId, {
          "fill-color": [220, 60, 34, 0],
          "stroke-color": [255, 104, 68],
          "stroke-width": 5,
          'stroke-line-cap':'round',
          'stroke-line-join':'round'
        });
      }
    }
  }, [layerId]); // updateLayerStyle 제외

  const highlight = useCallback(
    (feature: any, options: HighlightOptions = {}) => {
      if (!feature || !layerId) return;

      const { isFitToLayer = true, srid, clear = true, style } = options;
      if (clear) clearFeatures(layerId);

      addFeature(layerId, feature, { srid: srid });
      if (isFitToLayer) fitToLayer(layerId, 1000);
      if (style) updateLayerStyle(layerId, style);
      setMaxZIndex(layerId);
    },
    [layerId, clearFeatures, addFeature, fitToLayer, setMaxZIndex],
  );
  const highlights = useCallback(
    (features: [], options: HighlightOptions = {}) => {
      features.map((feature) => {
        highlight(feature, options);
      });
    },
    [layerId, clearFeatures, addFeature, fitToLayer, setMaxZIndex],
  );

  const highlightWKT = useCallback(
    (wkt: string, options: HighlightOptions = {}) => {
      if (!wkt) return;
      const feature = fromWKT(wkt);
      highlight(feature, options);
    },
    [highlight],
  );

  const clearHighlight = useCallback(() => {
    if (!layerId) return;
    clearFeatures(layerId);
  }, [layerId, clearFeatures]);

  return {
    highlight,
    highlightWKT,
    clearHighlight,
    highlights,
    layerId,
    isReady: !!layerId
  };
}

