"use client";

import { TOCNode } from "@geon-map/react-ui/types";
import { Button } from "@geon-ui/react/primitives/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "@geon-ui/react/primitives/tooltip";
import { Layers } from "lucide-react";
import { useCallback, useState } from "react";
import { useFacilitySearch } from "@/app/service/_contexts/facility-search";
import TOCWidgetPackage from "@/components/widget/toc-widget-package";

interface TOCWidgetServiceProps {
  className?: string;
}

export default function TOCWidgetService({ className }: TOCWidgetServiceProps) {
  const { selectedServiceId } = useFacilitySearch();
  const [tocVisible, setTOCVisible] = useState(false);

  /**
   * 서비스별 TOC 데이터를 로드합니다.
   * 서비스별 TS 파일과 공통 TS 파일을 함께 로드하여 반환합니다.
   * @returns 서비스 데이터 + 공통 데이터 또는 공통 데이터만
   */
  const loadTOCData = useCallback(async (): Promise<TOCNode[]> => {
    if (!selectedServiceId) {
      return [];
    }

    // 공통 데이터는 항상 로드
    const commonModule = await import(`@/app/service/_data/toc/common`);
    const commonData = commonModule.TOC_DATA;

    try {
      // 서비스별 데이터 로드 시도
      const serviceModule = await import(`@/app/service/_data/toc/${selectedServiceId}`);

      // 서비스별로 export된 변수명이 다를 수 있으므로 처리
      let serviceData: TOCNode[] = serviceModule.TOC_DATA;

      // 서비스 데이터 + 공통 데이터 함께 반환
      return [...serviceData, ...commonData];
    } catch {
      // 서비스 데이터 로드 실패 시 공통 데이터만 반환
      return commonData;
    }
  }, [selectedServiceId]);

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            size="sm"
            onClick={() => setTOCVisible(!tocVisible)}
            className="absolute top-0 m-5 h-8 w-8 bg-white p-0 text-green-600 hover:bg-white hover:text-green-600 hover:scale-110 transition-transform"
          >
            <Layers className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>레이어 목록</TooltipContent>
      </Tooltip>

      <TOCWidgetPackage
        className={className}
        tocDataLoader={loadTOCData}
        visible={tocVisible}
      />
    </>
  );
}
