"use client";

import { OverviewMapPositionType } from "@geon-map/core";
import { useOverview } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Label } from "@geon-ui/react/primitives/label";
import { Popover, PopoverTrigger } from "@geon-ui/react/primitives/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@geon-ui/react/primitives/select";
import { Switch } from "@geon-ui/react/primitives/switch";
import { MapIcon } from "lucide-react";
import * as React from "react";

import {
  ToolbarContent,
  ToolbarItem,
  ToolbarTrigger,
} from "./base/toolbar-item";

// Props for compound components
export interface ToolbarOverviewProps
  extends React.HTMLAttributes<HTMLDivElement> {
  /** 개요도 위치 */
  position?: OverviewMapPositionType;
  /** 활성화 상태 변경 콜백 */
  onEnabledChange?: (enabled: boolean) => void;
  /** 위치 변경 콜백 */
  onPositionChange?: (position: OverviewMapPositionType) => void;
}

export interface ToolbarOverviewTriggerProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 툴팁 텍스트 */
  tooltip?: string;
  /** 버튼 크기 */
  size?: "sm" | "lg" | "default" | "icon";
}

export interface ToolbarOverviewContentProps
  extends React.HTMLAttributes<HTMLDivElement> {}

// Overview Context 생성
interface OverviewContextValue {
  enabled: boolean;
  setEnabled: (enabled: boolean) => void;
  position: OverviewMapPositionType;
  onEnabledChange?: (enabled: boolean) => void;
  onPositionChange?: (position: OverviewMapPositionType) => void;
}

const OverviewContext = React.createContext<OverviewContextValue | null>(null);

export const useOverviewContext = () => {
  const context = React.useContext(OverviewContext);
  if (!context) {
    throw new Error(
      "ToolbarOverview components must be used within ToolbarOverview",
    );
  }
  return context;
};

// 위치 옵션 정의
const POSITION_OPTIONS: { value: OverviewMapPositionType; label: string }[] = [
  { value: "left-up", label: "좌상단" },
  { value: "right-up", label: "우상단" },
  { value: "left-down", label: "좌하단" },
  { value: "right-down", label: "우하단" },
];

// Main ToolbarOverview Container
export const ToolbarOverview = React.forwardRef<
  HTMLDivElement,
  ToolbarOverviewProps
>(
  (
    {
      position = "left-down",
      onEnabledChange,
      onPositionChange,
      className,
      children,
      ...props
    },
    ref,
  ) => {
    const { enabled, setEnabled } = useOverview(position);

    const handleEnabledChange = React.useCallback(
      (newEnabled: boolean) => {
        setEnabled(newEnabled);
        onEnabledChange?.(newEnabled);
      },
      [setEnabled, onEnabledChange],
    );

    const contextValue = React.useMemo(
      () => ({
        enabled,
        setEnabled: handleEnabledChange,
        position,
        onEnabledChange,
        onPositionChange,
      }),
      [
        enabled,
        handleEnabledChange,
        position,
        onEnabledChange,
        onPositionChange,
      ],
    );

    return (
      <OverviewContext.Provider value={contextValue}>
        <ToolbarItem ref={ref} className={className} {...props}>
          <Popover>
            <PopoverTrigger asChild>
              {React.Children.toArray(children).find(
                (child) =>
                  React.isValidElement(child) &&
                  child.type === ToolbarOverviewTrigger,
              )}
            </PopoverTrigger>
            {React.Children.toArray(children).find(
              (child) =>
                React.isValidElement(child) &&
                child.type === ToolbarOverviewContent,
            )}
          </Popover>
        </ToolbarItem>
      </OverviewContext.Provider>
    );
  },
);

ToolbarOverview.displayName = "ToolbarOverview";

// ToolbarOverviewTrigger Component
export const ToolbarOverviewTrigger = React.forwardRef<
  HTMLButtonElement,
  ToolbarOverviewTriggerProps
>(
  (
    { tooltip = "인덱스맵", size = "default", className, children, ...props },
    ref,
  ) => {
    const { enabled } = useOverviewContext();

    return (
      <ToolbarTrigger
        ref={ref}
        tooltip={tooltip}
        size={size}
        active={enabled}
        className={className}
        {...props}
      >
        {children || <MapIcon className="h-4 w-4" />}
      </ToolbarTrigger>
    );
  },
);

ToolbarOverviewTrigger.displayName = "ToolbarOverviewTrigger";

// ToolbarOverviewContent Component
export const ToolbarOverviewContent = React.forwardRef<
  HTMLDivElement,
  ToolbarOverviewContentProps
>(({ className, children, ...props }, ref) => {
  const { enabled, setEnabled, position, onPositionChange } =
    useOverviewContext();

  const [currentPosition, setCurrentPosition] =
    React.useState<OverviewMapPositionType>(position);

  const handlePositionChange = React.useCallback(
    (newPosition: OverviewMapPositionType) => {
      setCurrentPosition(newPosition);
      onPositionChange?.(newPosition);
    },
    [onPositionChange],
  );

  return (
    <ToolbarContent
      ref={ref}
      align="center"
      sideOffset={16}
      className={cn("w-64 flex flex-col gap-4 p-4", className)}
      {...props}
    >
      {/* 개요도 활성화 토글 */}
      <div className="flex items-center justify-between">
        <Label htmlFor="overview-enabled" className="text-sm font-medium">
          인덱스 맵
        </Label>
        <Switch
          id="overview-enabled"
          checked={enabled}
          onCheckedChange={setEnabled}
        />
      </div>

      {/* 개요도 위치 설정 */}
      {enabled && (
        <div className="space-y-2">
          <Label className="text-sm font-medium">위치</Label>
          <Select value={currentPosition} onValueChange={handlePositionChange}>
            <SelectTrigger>
              <SelectValue placeholder="위치 선택" />
            </SelectTrigger>
            <SelectContent>
              {POSITION_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {children}
    </ToolbarContent>
  );
});

ToolbarOverviewContent.displayName = "ToolbarOverviewContent";
