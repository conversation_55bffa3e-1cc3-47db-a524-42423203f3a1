"use client";

import {
  useDraw,
  useFeature,
  useLayer,
  useMap,
  useProjection,
} from "@geon-map/react-odf";
import {
  BASE_URL,
  createGeonMagpClient,
  crtfckey,
  ResultItem,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { DialogContent } from "@geon-ui/react/compounds/draggable-dialog/dialog-content";
import { DialogFooter } from "@geon-ui/react/compounds/draggable-dialog/dialog-footer";
import { DialogHeader } from "@geon-ui/react/compounds/draggable-dialog/dialog-header";
import { DraggableDialog } from "@geon-ui/react/compounds/draggable-dialog/index";
import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import {
  Area,
  AreaChart,
  CartesianGrid,
  ChartContainer,
  ChartTooltipContent,
  Customized,
  ReferenceDot,
  Tooltip,
  <PERSON><PERSON><PERSON>s,
  <PERSON><PERSON><PERSON>s,
} from "@geon-ui/react/primitives/chart";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@geon-ui/react/primitives/hover-card";
import { Dot, DownloadIcon, Minus, PrinterIcon, SlashIcon } from "lucide-react";
import { RefObject, SetStateAction, useEffect, useRef, useState } from "react";

import { useDomCapture } from "../../hooks/use-dom-capture";

export type ElevationType = "lineString" | "point";

export interface ElevationWidgetProps extends ElevationProps {}
export interface ElevationProps extends React.HTMLAttributes<HTMLDivElement> {
  targetElementSelector?: string;
  exceptionElementSelectors?: string[];
}
export interface ElevationTriggerProps
  extends React.HTMLAttributes<HTMLButtonElement> {}
export interface ElevationContentProps
  extends React.HTMLAttributes<HTMLDivElement> {}
export interface ElevationItemProps
  extends React.HTMLAttributes<HTMLButtonElement> {
  elevationType: ElevationType;
  setDrawing: React.Dispatch<SetStateAction<{ type: string; wkt: string }>>;
}
export interface ElevationChartProps
  extends React.HTMLAttributes<HTMLDivElement> {
  data: ResultItem[];
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
}
export interface ElevationResultProps
  extends React.HTMLAttributes<HTMLDivElement> {
  data: ResultItem;
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
}

const ElevationTrigger = ({
  className,
  children,
  ...props
}: ElevationTriggerProps) => {
  return (
    <HoverCardTrigger>
      <Button
        className={cn(
          "cursor-pointer bg-white text-gray-600 hover:bg-white hover:text-black opacity-80",
          className,
        )}
        {...props}
      >
        {children}
      </Button>
    </HoverCardTrigger>
  );
};

const ElevationContent = ({
  className,
  children,
  ...props
}: ElevationContentProps) => {
  return (
    <HoverCardContent
      className={cn(
        "flex flex-row p-1 gap-3 bg-background/90 backdrop-blur-md border shadow-lg rounded-md w-fit h-auto items-center justify-center",
        className,
      )}
      align="center"
      side="left"
      alignOffset={-40}
      sideOffset={8}
      {...props}
    >
      {children}
    </HoverCardContent>
  );
};

const ElevationItem = ({
  className,
  children,
  elevationType,
  setDrawing,
  ...props
}: ElevationItemProps) => {
  const { map, odf } = useMap();
  const { startDrawing, clearAll } = useDraw();
  const { drawLayer } = useLayer();
  const f = useFeature();
  // Vertex 지도 Point 스타일
  const style = odf.StyleFactory.produce({
    image: {
      circle: {
        radius: 5,
        stroke: { color: "black", width: 2 },
      },
    },
  });

  const handleToDrawing = (mode: ElevationType) => {
    const { drawend } = startDrawing(mode);
    drawend((feature) => {
      if (feature.getArea() > 100000) {
        alert("100km 이하만 측정 가능합니다.");
        clearAll();
        return;
      }
      const projection = map.getProjection?.();
      const clone = feature.clone();
      const transformedFeature = projection
        ? projection.unprojectGeom(clone, "5186")
        : clone;
      // Vertex 좌표로 피쳐 생성 및 스타일 적용
      const flatCoords = feature.getGeometry().flatCoordinates;
      for (let i = 0; i < flatCoords.length; i += 2) {
        const point = f?.createPointFromCoordinates([
          flatCoords[i],
          flatCoords[i + 1],
        ]);
        point.setStyle(style);
        drawLayer?.odfLayer.addFeature(point);
      }
      setDrawing({ type: mode, wkt: transformedFeature.featureToWKT().wkt });
    });
  };

  return (
    <button
      className={cn(
        "flex flex-col items-center justify-center gap-1 px-3 py-2 min-w-12 h-auto text-xs font-medium transition-all duration-200 text-gray-600 hover:text-black hover:bg-white rounded-md cursor-pointer",
        className,
      )}
      {...props}
      onClick={() => handleToDrawing(elevationType)}
    >
      {children}
    </button>
  );
};

const Elevation = ({ className, children, ...props }: ElevationProps) => {
  return (
    <div
      className={cn("absolute right-4 top-80 flex flex-col gap-2", className)}
      {...props}
    >
      <HoverCard>{children}</HoverCard>
    </div>
  );
};

// 경사도 분석 팝업 공통 레이어
const ElevationDialog = ({
  isOpen,
  setIsOpen,
  layer,
  children,
}: {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  layer?: any;
  children: React.ReactNode;
}) => {
  const { clearAll } = useDraw();
  const onClose = () => {
    layer.clearFeatures();
    clearAll();
    setIsOpen(false);
  };

  return (
    <DraggableDialog
      className="w-[800px] h-[600px]"
      defaultPosition={{ x: -500, y: -100 }}
      isOpen={isOpen}
      onClose={onClose}
    >
      <DialogHeader>
        <p>경사도 분석</p>
      </DialogHeader>
      <DialogContent>{children}</DialogContent>
      <DialogFooter />
    </DraggableDialog>
  );
};

const ElevationButtons = ({
  targetRef,
}: {
  targetRef: RefObject<HTMLDivElement | null>;
}) => {
  const { download, print } = useDomCapture(targetRef);

  return (
    <div className="flex gap-2">
      <Button variant="outline" onClick={() => download("평균경사도.png")}>
        차트 다운로드
        <DownloadIcon />
      </Button>
      <Button variant="outline" onClick={() => print()}>
        차트 인쇄하기
        <PrinterIcon />
      </Button>
    </div>
  );
};

// LineString 차트 팝업
const ElevationChart = ({ data, isOpen, setIsOpen }: ElevationChartProps) => {
  const { map, odf } = useMap();
  const { projectGeom } = useProjection();
  const f = useFeature();
  // 선택지점 지도 Point 스타일
  const setColor = (type: "start" | "end") => {
    const style = odf.StyleFactory.produce({
      image: {
        circle: {
          radius: 5,
          stroke: { color: `${type === "start" ? "blue" : "red"}`, width: 2 },
          fill: { color: `${type === "start" ? "blue" : "red"}` },
        },
      },
    });
    return style;
  };

  const [vertex, setVertex] = useState<ResultItem[]>([]);
  const [pointLayer, setPointLayer] = useState<any | null>(null);
  const [point, setPoint] = useState<{
    start?: ResultItem;
    end?: ResultItem;
  }>({});
  const [slope, setSlope] = useState("차트에서 시작지점을 선택해주세요.");

  // 차트 Ref 객체
  const ref = useRef<HTMLDivElement | null>(null);

  // 차트 설정
  const chartConfig = {
    elevation: { label: "Elevation", color: "#A4D4EA" },
    distance: { label: "Distance", color: "#000000" },
    tooltip: { label: "Tooltip", color: "#ffffff" },
  };

  // 차트 축 포맷 설정
  const tickFormat = (value: number) => {
    const format = value !== 0 ? `${value} m` : `${value}`;
    return format;
  };

  // 차트 X축 설정 (구간 : 10개)
  const maxDistance = Math.max(
    ...data.map((d: ResultItem) => d.cumulativeDistanceM),
  );
  const desiredTicks = 10;
  const step = 5;
  const rawInterval = maxDistance / (desiredTicks - 1);
  const interval = Math.ceil(rawInterval / step) * step;
  const ticks: number[] = [];
  for (let i = 0; i <= maxDistance; i += interval) {
    ticks.push(Math.round(i * 100) / 100);
  }

  // Vertex 상태값 업데이트
  useEffect(() => {
    if (data) setVertex(data.filter((p: ResultItem) => p.vertexIndex));
  }, [data]);

  // 차트 지점 선택 포인트 레이어 생성
  useEffect(() => {
    const pointLayer = odf.LayerFactory.produce("empty");
    pointLayer.setMap(map);
    setPointLayer(pointLayer);
  }, [map, odf]);

  // 차트 지점 선택
  const pickChartPoint = (e: any) => {
    if (!e || !e.activePayload) return;
    const pick = e.activePayload[0].payload; // 선택 지점
    setPoint((prev) => {
      // 선택지점 지도 표시
      const mapPoint = projectGeom(f.fromWKT(pick.targetPoint), "5186");
      mapPoint.setStyle(prev.start ? setColor("end") : setColor("start"));
      pointLayer?.addFeature(mapPoint);
      if (!prev.start) {
        setSlope("차트에서 종료지점을 선택해주세요.");
        return { start: pick };
      } else if (!prev.end) {
        const slopeResult = getSlope(prev.start, pick);
        setSlope(slopeResult);
        return { ...prev, end: pick };
      } else {
        // 초기화 후 시작지점 선택
        pointLayer.clearFeatures();
        const mapPoint = projectGeom(f.fromWKT(pick.targetPoint), "5186");
        mapPoint.setStyle(setColor("start"));
        pointLayer?.addFeature(mapPoint);
        return { start: pick };
      }
    });
  };

  // 평균 경사도 계산
  const getSlope = (start: ResultItem, end: ResultItem) => {
    const rad = Math.atan2(
      end.elevationZ - start.elevationZ, // 종료지점 고도 - 시작지점 고도
      end.cumulativeDistanceM - start.cumulativeDistanceM, // 종료지점 누적거리 - 시작지점 누적거리
    );
    return ((rad * 180) / Math.PI).toFixed(2);
  };

  // 차트 지점 선택 연결선 커스터마이징 컴포넌트
  const ConnectingLine = ({
    startX,
    startY,
    endX,
    endY,
    xAxisMap,
    yAxisMap,
  }: {
    startX: number;
    startY: number;
    endX: number;
    endY: number;
    xAxisMap: any;
    yAxisMap: any;
  }) => {
    if (!xAxisMap || !yAxisMap) return null;
    const xAxis = Object.values(xAxisMap)[0] as any;
    const yAxis = Object.values(yAxisMap)[0] as any;
    const xScale = xAxis.scale;
    const yScale = yAxis.scale;

    const x1 = xScale(startX);
    const y1 = yScale(startY);
    const x2 = xScale(endX);
    const y2 = yScale(endY);

    const midX = (x1 + x2) / 2;
    const midY = (y1 + y2) / 2;

    return (
      <>
        <line
          x1={x1}
          y1={y1}
          x2={x2}
          y2={y2}
          stroke="#a268aaff"
          strokeWidth={1}
        />

        <text
          x={midX}
          y={midY + 30}
          textAnchor="middle"
          fontSize={16}
          fontWeight="bold"
        >
          평균 경사도 : {slope}º
        </text>
      </>
    );
  };

  return (
    <ElevationDialog isOpen={isOpen} setIsOpen={setIsOpen} layer={pointLayer}>
      <div className="flex items-center justify-between">
        <p>평균 경사도 : {slope}º</p>
        <ElevationButtons targetRef={ref} />
      </div>
      <div ref={ref} className="overflow-visible" style={{ width: "100%" }}>
        <ChartContainer
          config={chartConfig}
          className="min-h-[430px] mt-7 pr-10 pt-12 pb-10"
        >
          <AreaChart data={data} onClick={pickChartPoint} accessibilityLayer>
            <CartesianGrid stroke="#bbb" strokeDasharray="3 3" />
            <XAxis
              dataKey="cumulativeDistanceM"
              ticks={ticks}
              tickFormatter={tickFormat}
            />
            <YAxis dataKey="elevationZ" tickFormatter={tickFormat} />
            <Tooltip content={<ChartTooltipContent />} />
            <Area
              dataKey="elevationZ"
              name="고도(m)"
              stroke="var(--color-elevation)"
              fill="var(--color-elevation)"
              strokeWidth={2}
            />
            <Area
              dataKey="cumulativeDistanceM"
              name="거리(m)"
              stroke="var(--color-distance)"
              fill="var(--color-distance)"
              strokeWidth={0}
              fillOpacity={0}
              activeDot={false}
            />

            {/* Vertex */}
            {vertex.map((v) => (
              <ReferenceDot
                key={v.cumulativeDistanceM}
                x={v.cumulativeDistanceM}
                y={v.elevationZ}
                r={4}
                fill="none"
                stroke="black"
              />
            ))}
            {/* 선택 시작지점 */}
            {point.start && (
              <ReferenceDot
                x={point.start.cumulativeDistanceM}
                y={point.start.elevationZ}
                r={4} // 점 크기
                fill="blue"
                stroke="none"
              />
            )}
            {/* 선택 종료지점 */}
            {point.end && (
              <ReferenceDot
                x={point.end.cumulativeDistanceM}
                y={point.end.elevationZ}
                r={4}
                fill="red"
                stroke="none"
              />
            )}
            {/* 선택지점 연결선 */}
            {point.start && point.end && (
              <Customized
                component={
                  <ConnectingLine
                    startX={point.start.cumulativeDistanceM}
                    startY={point.start.elevationZ}
                    endX={point.end.cumulativeDistanceM}
                    endY={point.end.elevationZ}
                    xAxisMap
                    yAxisMap
                  />
                }
              />
            )}
          </AreaChart>
        </ChartContainer>
      </div>
    </ElevationDialog>
  );
};

// Point 결과 팝업
const ElevationResult = ({ data, isOpen, setIsOpen }: ElevationResultProps) => {
  return (
    <ElevationDialog isOpen={isOpen} setIsOpen={setIsOpen}>
      <p>좌표 : {data.targetPoint}</p>
      <p>고도 : {data.elevationZ}</p>
    </ElevationDialog>
  );
};

export const ElevationWidget = (options: ElevationWidgetProps) => {
  const [drawing, setDrawing] = useState({ type: "", wkt: "" });
  const [isOpen, setIsOpen] = useState(false);

  const client = createGeonMagpClient({ baseUrl: BASE_URL, crtfckey });
  const { data } = useAppQuery({
    queryKey: ["elevation", drawing.wkt],
    queryFn: () => {
      const options = {
        targetSrid: 5186,
        stepMeters: 5,
      };

      return drawing.type === "lineString"
        ? client.elevation.line({ linestringWkt: drawing.wkt, ...options })
        : drawing.type === "point"
          ? client.elevation.point({ pointWkt: drawing.wkt, ...options })
          : null;
    },
    enabled: !!drawing,
    select: (res) => res.result,
  });

  useEffect(() => {
    if (data) setIsOpen(true);
  }, [data]);

  return (
    <>
      <Elevation {...options}>
        <ElevationTrigger>
          <SlashIcon className="w-5 h-5" />
        </ElevationTrigger>
        <ElevationContent>
          <ElevationItem elevationType="lineString" setDrawing={setDrawing}>
            <Minus />
            <span>선</span>
          </ElevationItem>
          <ElevationItem elevationType="point" setDrawing={setDrawing}>
            <Dot />
            <span>점</span>
          </ElevationItem>
        </ElevationContent>

        {/* LineString 차트 */}
        {data && isOpen && drawing.type === "lineString" && (
          <ElevationChart data={data} isOpen={isOpen} setIsOpen={setIsOpen} />
        )}
        {/* Point 결과 */}
        {data && isOpen && drawing.type === "point" && (
          <ElevationResult data={data} isOpen={isOpen} setIsOpen={setIsOpen} />
        )}
      </Elevation>
    </>
  );
};
