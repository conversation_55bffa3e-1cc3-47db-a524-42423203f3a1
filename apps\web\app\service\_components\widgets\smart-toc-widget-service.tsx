"use client";

import { <PERSON><PERSON> } from "@geon-ui/react/primitives/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@geon-ui/react/primitives/tooltip";
import { Layers } from "lucide-react";
import { useState } from "react";

import { SmartTOCWidget } from "@/components/widget/smart-toc-widget";

interface SmartTOCWidgetServiceProps {
  className?: string;
}

/**
 * SmartTOCWidget을 사용하는 서비스 TOC 위젯
 *
 * 특징:
 * - useLayer 상태를 직접 구독하여 레이어 표시
 * - 복잡한 TOC 데이터 로딩 로직 불필요
 * - Layer 컴포넌트로 마운트된 레이어들을 자동으로 표시
 */
export default function SmartTOCWidgetService({
  className,
}: SmartTOCWidgetServiceProps) {
  const [tocVisible, setTOCVisible] = useState(false);

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            size="sm"
            onClick={() => setTOCVisible(!tocVisible)}
            className="absolute top-0 m-5 h-8 w-8 bg-white p-0 text-green-600 transition-transform hover:scale-110 hover:bg-white hover:text-green-600"
          >
            <Layers className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>레이어 목록</TooltipContent>
      </Tooltip>

      {tocVisible && (
        <SmartTOCWidget
          className={className}
          excludeLayerTypes={["draw", "measure", "clear"]}
        />
      )}
    </>
  );
}
