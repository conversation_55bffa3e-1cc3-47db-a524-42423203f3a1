/**
 * shadcn/ui 색상 변수를 상속
 */

@theme {
  --color-geon-background: var(--color-background);
  --color-geon-foreground: var(--color-foreground);
  --color-geon-card: var(--color-card);
  --color-geon-card-foreground: var(--color-card-foreground);
  --color-geon-popover: var(--color-popover);
  --color-geon-popover-foreground: var(--color-popover-foreground);
  --color-geon-primary: var(--color-primary);
  --color-geon-primary-foreground: var(--color-primary-foreground);
  --color-geon-secondary: var(--color-secondary);
  --color-geon-secondary-foreground: var(--color-secondary-foreground);
  --color-geon-muted: var(--color-muted);
  --color-geon-muted-foreground: var(--color-muted-foreground);
  --color-geon-accent: var(--color-accent);
  --color-geon-accent-foreground: var(--color-accent-foreground);
  --color-geon-destructive: var(--color-destructive);
  --color-geon-destructive-foreground: var(--color-destructive-foreground);
  --color-geon-border: var(--color-border);
  --color-geon-input: var(--color-input);
  --color-geon-ring: var(--color-ring);

  --color-geon-hover: var(--color-accent);
  --color-geon-hover-foreground: var(--color-accent-foreground);
  --color-geon-active: var(--color-muted);
  --color-geon-active-foreground: var(--color-muted-foreground);
  --color-geon-pressed: var(--color-secondary);
  --color-geon-pressed-foreground: var(--color-secondary-foreground);

  --color-geon-toolbar: var(--color-popover);
  --color-geon-toolbar-border: var(--color-border);
  --color-geon-toolbar-shadow: var(--color-ring);
}

.dark {
  --color-geon-background: var(--color-background);
  --color-geon-foreground: var(--color-foreground);
  --color-geon-card: var(--color-card);
  --color-geon-card-foreground: var(--color-card-foreground);
  --color-geon-popover: var(--color-popover);
  --color-geon-popover-foreground: var(--color-popover-foreground);
  --color-geon-primary: var(--color-primary);
  --color-geon-primary-foreground: var(--color-primary-foreground);
  --color-geon-secondary: var(--color-secondary);
  --color-geon-secondary-foreground: var(--color-secondary-foreground);
  --color-geon-muted: var(--color-muted);
  --color-geon-muted-foreground: var(--color-muted-foreground);
  --color-geon-accent: var(--color-accent);
  --color-geon-accent-foreground: var(--color-accent-foreground);
  --color-geon-destructive: var(--color-destructive);
  --color-geon-destructive-foreground: var(--color-destructive-foreground);
  --color-geon-border: var(--color-border);
  --color-geon-input: var(--color-input);
  --color-geon-ring: var(--color-ring);

  --color-geon-hover: var(--color-accent);
  --color-geon-hover-foreground: var(--color-accent-foreground);
  --color-geon-active: var(--color-muted);
  --color-geon-active-foreground: var(--color-muted-foreground);
  --color-geon-pressed: var(--color-secondary);
  --color-geon-pressed-foreground: var(--color-secondary-foreground);

  --color-geon-toolbar: var(--color-popover);
  --color-geon-toolbar-border: var(--color-border);
  --color-geon-toolbar-shadow: var(--color-ring);
}

@layer utilities {
  .bg-geon-toolbar {
    background-color: var(--color-geon-toolbar);
  }

  .border-geon-toolbar-border {
    border-color: var(--color-geon-toolbar-border);
  }

  .shadow-geon-toolbar {
    box-shadow: 0 4px 20px var(--color-geon-toolbar-shadow);
  }

  .text-geon-hover-foreground {
    color: var(--color-geon-hover-foreground);
  }

  .bg-geon-hover {
    background-color: var(--color-geon-hover);
  }

  .bg-geon-active {
    background-color: var(--color-geon-active);
  }

  .text-geon-active-foreground {
    color: var(--color-geon-active-foreground);
  }

  .bg-geon-pressed {
    background-color: var(--color-geon-pressed);
  }

  .text-geon-pressed-foreground {
    color: var(--color-geon-pressed-foreground);
  }
}
