import { QueryProvider } from "@geon-query/react-query";
import {
  SidebarInset,
  SidebarProvider,
} from "@geon-ui/react/primitives/sidebar";
import React from "react";

import AdminSidebar from "./_components/admin-sidebar";

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <SidebarProvider>
      <AdminSidebar />
      <QueryProvider>
        <SidebarInset className="flex-1 overflow-auto p-4">
          {children}
        </SidebarInset>
      </QueryProvider>
    </SidebarProvider>
  );
}
