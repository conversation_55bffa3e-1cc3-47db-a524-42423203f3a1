"use client";

import { DndContext } from "@dnd-kit/core";
import { restrictToWindowEdges } from "@dnd-kit/modifiers";
import { cn } from "@geon-ui/react/lib/utils";
import { DialogContent } from "@geon-ui/react/primitives/dialog";
import React, { ComponentProps } from "react";

export default function DraggableDialogContent({
  children,
  className,
  interactive,
  ...props
}: ComponentProps<typeof DialogContent> & {
  /** Dialog 바깥 상호작용 가능 여부 */
  interactive?: boolean;
}) {
  // drag target element ref
  const ref = React.useRef<HTMLDivElement>(null);
  // element position ref
  const position = React.useRef({ x: 0, y: 0 });
  // request animation frame id ref
  const rafId = React.useRef<number>(null);

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
    };
  }, []);

  return (
    <DndContext
      onDragMove={(e) => {
        if (!ref.current) return;

        if (rafId.current) {
          cancelAnimationFrame(rafId.current);
        }

        rafId.current = requestAnimationFrame(() => {
          if (!ref.current) return;
          ref.current.style.transform = `translate3d(${position.current.x + e.delta.x}px, ${position.current.y + e.delta.y}px, 0)`;
        });
      }}
      onDragEnd={(e) => {
        if (rafId.current) {
          cancelAnimationFrame(rafId.current);
          rafId.current = null;
        }

        position.current = {
          x: position.current.x + e.delta.x,
          y: position.current.y + e.delta.y,
        };

        if (ref.current)
          ref.current.style.transform = `translate3d(${position.current.x}px, ${position.current.y}px, 0)`;
      }}
      modifiers={[restrictToWindowEdges]}
    >
      <DialogContent
        {...props}
        className={cn(className, "transition-none")}
        ref={ref}
        onInteractOutside={interactive ? (e) => e.preventDefault() : undefined}
        style={{
          transform: `translate3d(0px, 0px, 0)`,
        }}
      >
        {children}
      </DialogContent>
    </DndContext>
  );
}
