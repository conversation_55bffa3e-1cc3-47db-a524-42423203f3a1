"use client";

import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@geon-ui/react/primitives/select";
import { Table } from "@tanstack/react-table";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { HTMLAttributes } from "react";

/** 한 페이지에 표시할 행 개수 선택 옵션 */
const NUM_OF_ROWS = [10, 20, 25, 30, 40, 50];

/** 서버가 내려주는 두 가지 pageInfo 포맷 지원 */
export type PageInfoLegacy = {
  /** 페이지당 행 수 */
  numOfRows: number;
  /** 현재 페이지 (1-base) */
  pageNo: number;
  /** 전체 건수 (문자열) */
  totalCount: string;
};

export type PageInfoNew = {
  /** 전체 건수 (숫자) */
  totalCount: number;
  /** 현재 페이지 (1-base) */
  currentPage: number;
  /** 페이지당 행 수 */
  pageSize: number;
  /** 전체 페이지 수 */
  totalPages: number;
};

export type PageInfo = PageInfoLegacy | PageInfoNew;

interface BasePaginationProps extends HTMLAttributes<HTMLDivElement> {
  isLoading?: boolean;
}

interface ServerPaginationProps extends BasePaginationProps {
  type: "server";
  table?: never;
  pageInfo: PageInfo;
  onPageNoChange: (pageNo: number) => void;
  onNumOfRowsChange: (numOfRows: number) => void;
}

interface ClientPaginationProps<TData> extends BasePaginationProps {
  type: "client";
  table: Table<TData>;
  pageInfo?: never;
  onPageNoChange?: never;
  onNumOfRowsChange?: never;
}

export type PaginationProps<TData> =
  | ServerPaginationProps
  | ClientPaginationProps<TData>;

/** 서로 다른 pageInfo를 내부 공통 형태로 정규화 */
function normalizePageInfo(pageInfo: PageInfo) {
  if ("pageNo" in pageInfo) {
    // Legacy 타입
    const totalCount = Number.parseInt(pageInfo.totalCount, 10) || 0;
    const numOfRows = pageInfo.numOfRows;
    const totalPages = Math.max(
      1,
      Math.ceil(totalCount / Math.max(numOfRows, 1)),
    );
    const currentPage = Math.min(Math.max(1, pageInfo.pageNo), totalPages);

    return {
      totalCount,
      currentPage,
      numOfRows,
      totalPages,
    };
  }

  // New 타입
  const totalCount = pageInfo.totalCount ?? 0;
  const numOfRows = pageInfo.pageSize ?? 0;
  // 전달된 totalPages가 0이거나 음수일 수 있는 경우를 방어
  const safeTotalPages =
    pageInfo.totalPages && pageInfo.totalPages > 0
      ? pageInfo.totalPages
      : Math.max(1, Math.ceil(totalCount / Math.max(numOfRows, 1)));
  const currentPage = Math.min(
    Math.max(1, pageInfo.currentPage ?? 1),
    safeTotalPages,
  );

  return {
    totalCount,
    currentPage,
    numOfRows,
    totalPages: safeTotalPages,
  };
}

/**
 * Server 에서 처리된 Pagination 에 따라 `numOfRows` 와 `pageNo` 요청을 처리합니다.
 * - 기존 포맷: { numOfRows, pageNo, totalCount: string }
 * - 신규 포맷: { totalCount: number, currentPage, pageSize, totalPages }
 */
export default function Pagination<TData = never>({
  pageInfo,
  onPageNoChange,
  onNumOfRowsChange,
  isLoading = false,
  className,
  type = "server",
  table,
  ...rest
}: PaginationProps<TData>) {
  // message handler
  const t = useTranslations("pagination");

  const setPageSize =
    type === "server" ? onNumOfRowsChange! : table!.setPageSize;
  const setPageIndex =
    type === "server"
      ? onPageNoChange!
      : (pageIndex: number) => table!.setPageIndex(pageIndex - 1);

  const { totalCount, currentPage, numOfRows, totalPages } =
    type === "server"
      ? normalizePageInfo(pageInfo!)
      : {
          totalCount: table!.getFilteredRowModel().rows.length,
          currentPage: table!.getState().pagination.pageIndex + 1,
          numOfRows: table!.getState().pagination.pageSize,
          totalPages: table!.getPageCount(),
        };

  const canPreviousPage = currentPage > 1;
  const canNextPage = currentPage < totalPages;

  return (
    <div
      className={cn("flex items-center justify-between px-2", className)}
      {...rest}
    >
      {/* Showing status */}
      <div className="text-muted-foreground flex-1 text-sm">
        {t("resultStatus", {
          totalCount,
          start: (currentPage - 1) * numOfRows + 1,
          end: Math.min(currentPage * numOfRows, totalCount),
        })}
      </div>
      <div className="flex items-center space-x-6 lg:space-x-8">
        {/* Rows per page selector */}
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium">{t("rowsPerPage")}</p>
          <Select
            value={`${numOfRows}`}
            onValueChange={(value) => {
              setPageSize(Number(value));
            }}
            disabled={isLoading}
          >
            <SelectTrigger size="sm" className="w-[70px]">
              <SelectValue placeholder={numOfRows} />
            </SelectTrigger>
            <SelectContent side="top">
              {NUM_OF_ROWS.map((rows) => (
                <SelectItem key={rows} value={`${rows}`}>
                  {rows}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        {/* Page indicator */}
        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
          {t("pageStatus", { currentPage, totalPages })}
        </div>
        {/* Navigation buttons */}
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="icon"
            className="hidden size-8 lg:flex"
            title="처음"
            onClick={() => setPageIndex(1)}
            disabled={!canPreviousPage || isLoading}
          >
            {/* << */}
            <ChevronsLeft />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="size-8"
            title="이전"
            onClick={() => setPageIndex(currentPage - 1)}
            disabled={!canPreviousPage || isLoading}
          >
            {/* < */}
            <ChevronLeft />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="size-8"
            title="다음"
            onClick={() => setPageIndex(currentPage + 1)}
            disabled={!canNextPage || isLoading}
          >
            {/* > */}
            <ChevronRight />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="hidden size-8 lg:flex"
            title="마지막"
            onClick={() => setPageIndex(totalPages)}
            disabled={!canNextPage || isLoading}
          >
            {/* >> */}
            <ChevronsRight />
          </Button>
        </div>
      </div>
    </div>
  );
}
