import {
  SidebarInset,
  SidebarProvider,
} from "@geon-ui/react/primitives/sidebar";
import React from "react";

import MapCreateSidebar from "./_components/map-create-sidebar";

/**
 * 맞춤형 지도 생성 페이지 레이아웃, Map Provider 가 Sidebar를 감싸도록 설정해야
 * 사이드바에서 useMap 으로 컨트롤 가능
 */
export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <SidebarProvider>
      <MapCreateSidebar />
      <SidebarInset>{children}</SidebarInset>
    </SidebarProvider>
  );
}
