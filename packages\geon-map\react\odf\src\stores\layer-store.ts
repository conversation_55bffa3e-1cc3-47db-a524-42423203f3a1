import type { LayerFactory } from "@geon-map/core";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

import type { BaseLayerInfo, Layer } from "../types/layer-types";
import { logger } from "./middleware/logger";

// 레이어 상태 인터페이스
export interface LayerState {
  // 레이어 목록
  layers: Layer[];
  selectedLayerId?: string;
  expandedGroups: Set<string>;

  // LayerFactory 인스턴스
  layerFactory: LayerFactory | null;

  // 🆕 배경 레이어 전용 상태
  currentBaseLayerId: string | null;
  availableBaseLayers: BaseLayerInfo[];
  baseLayersByCategory: Map<string, BaseLayerInfo[]>;

  // 🆕 오버레이/하이브리드 레이어 전용 상태
  activeOverlayLayerIds: string[]; // 현재 활성된 오버레이 레이어 ID들
  availableOverlayLayers: BaseLayerInfo[]; // 사용 가능한 오버레이 레이어들
  overlayLayersByCategory: Map<string, BaseLayerInfo[]>; // 카테고리별 오버레이 레이어

  // 액션들
  addLayer: (layer: Layer) => void;
  removeLayer: (layerId: string) => void;
  updateLayer: (id: string, updates: Partial<Layer>) => void;
  setLayers: (layers: Layer[]) => void;
  toggleLayerVisibility: (layerId: string) => void;
  setLayerFilter: (id: string, filter: string) => void;
  setSelectedLayer: (layerId: string) => void;
  clearLayers: () => void;

  // LayerFactory 관리
  setLayerFactory: (factory: LayerFactory | null) => void;

  // 🆕 배경 레이어 액션들
  setCurrentBaseLayer: (baseLayerId: string | null) => void;
  addBaseLayerOption: (baseLayer: BaseLayerInfo) => void;
  removeBaseLayerOption: (baseLayerId: string) => void;
  setAvailableBaseLayers: (baseLayers: BaseLayerInfo[]) => void;
  getBaseLayerById: (baseLayerId: string) => BaseLayerInfo | null;
  getBaseLayersByCategory: (category: string) => BaseLayerInfo[];

  // 🆕 오버레이/하이브리드 레이어 액션들
  addActiveOverlayLayer: (overlayLayerId: string) => void;
  removeActiveOverlayLayer: (overlayLayerId: string) => void;
  toggleOverlayLayer: (overlayLayerId: string) => void;
  setAvailableOverlayLayers: (overlayLayers: BaseLayerInfo[]) => void;
  getOverlayLayerById: (overlayLayerId: string) => BaseLayerInfo | null;
  getOverlayLayersByCategory: (category: string) => BaseLayerInfo[];
  isOverlayLayerActive: (overlayLayerId: string) => boolean;

  // 🆕 Draw/Measure/Clear 레이어 전용 헬퍼 메서드
  getLayerByType: (type: "draw" | "measure" | "clear") => Layer | null;
  getLayerById: (layerId: string) => Layer | null;
  getLayerTypeById: (layerId: string) => "draw" | "measure" | "clear" | "other";
  addDrawLayer: (
    drawLayer: any,
    options?: { name?: string; visible?: boolean },
  ) => void;
  addMeasureLayer: (
    measureLayer: any,
    options?: { name?: string; visible?: boolean },
  ) => void;
  addClearLayer: (
    clearLayer: any,
    options?: { name?: string; visible?: boolean },
  ) => void;
}

// 초기 상태
const initialState: LayerState = {
  layers: [],
  selectedLayerId: undefined,
  expandedGroups: new Set<string>(),
  layerFactory: null,

  // 🆕 배경 레이어 초기 상태
  currentBaseLayerId: null,
  availableBaseLayers: [],
  baseLayersByCategory: new Map(),

  // 🆕 오버레이/하이브리드 레이어 초기 상태
  activeOverlayLayerIds: [],
  availableOverlayLayers: [],
  overlayLayersByCategory: new Map(),

  // 액션들 (팩토리에서 오버라이드됨)
  addLayer: () => {},
  removeLayer: () => {},
  updateLayer: () => {},
  setLayers: () => {},
  toggleLayerVisibility: () => {},
  setLayerFilter: () => {},
  setSelectedLayer: () => {},
  clearLayers: () => {},
  setLayerFactory: () => {},

  // 🆕 배경 레이어 액션들 (팩토리에서 오버라이드됨)
  setCurrentBaseLayer: () => {},
  addBaseLayerOption: () => {},
  removeBaseLayerOption: () => {},
  setAvailableBaseLayers: () => {},
  getBaseLayerById: () => null,
  getBaseLayersByCategory: () => [],

  // 🆕 오버레이/하이브리드 레이어 액션들 (팩토리에서 오버라이드됨)
  addActiveOverlayLayer: () => {},
  removeActiveOverlayLayer: () => {},
  toggleOverlayLayer: () => {},
  setAvailableOverlayLayers: () => {},
  getOverlayLayerById: () => null,
  getOverlayLayersByCategory: () => [],
  isOverlayLayerActive: () => false,

  getLayerByType: () => null,
  getLayerById: () => null,
  getLayerTypeById: () => "other",
  addDrawLayer: () => {},
  addMeasureLayer: () => {},
  addClearLayer: () => {},
};

/**
 * 🎯 레이어 스토어 팩토리 함수 (다중 지도 인스턴스 지원)
 */
export const createLayerStore = (initialOptions: Partial<LayerState> = {}) => {
  const mergedInitialState = {
    ...initialState,
    ...initialOptions,
  };

  return create<LayerState>()(
    devtools(
      logger(
        (set, get) => ({
          ...mergedInitialState,

          // 액션 구현
          addLayer: (layer: Layer) =>
            set(
              (state) => ({
                layers: [...state.layers, layer],
              }),
              false,
              "addLayer",
            ),

          removeLayer: (layerId: string) =>
            set(
              (state) => ({
                layers: state.layers.filter((layer) => layer.id !== layerId),
                selectedLayerId:
                  state.selectedLayerId === layerId
                    ? undefined
                    : state.selectedLayerId,
              }),
              false,
              "removeLayer",
            ),

          updateLayer: (id: string, updates: Partial<Layer>) => {
            // 🔧 ODF 레이어의 실제 가시성도 함께 변경
            if ("visible" in updates) {
              const layer = get().layers.find((l) => l.id === id);
              if (layer?.odfLayer) {
                try {
                  console.log(
                    `🔄 Setting ODF layer visibility: ${id} -> ${updates.visible}`,
                  );
                  layer.odfLayer.setVisible(!!updates.visible);
                } catch (error) {
                  console.warn(
                    `⚠️ Failed to set ODF layer visibility for ${id}:`,
                    error,
                  );
                }
              }
            }

            set(
              (state) => ({
                layers: state.layers.map((layer) =>
                  layer.id === id ? { ...layer, ...updates } : layer,
                ),
              }),
              false,
              "updateLayer",
            );
          },

          setLayers: (layers: Layer[]) =>
            set(
              () => ({
                layers,
              }),
              false,
              "setLayers",
            ),
          toggleLayerVisibility: (layerId: string) => {
            const layer = get().layers.find((l) => l.id === layerId);
            if (layer) {
              const newVisible = !layer.visible;

              // 🔧 ODF 레이어의 실제 가시성도 함께 변경
              if (layer.odfLayer) {
                try {
                  console.log(
                    `🔄 Toggling ODF layer visibility: ${layerId} -> ${newVisible}`,
                  );
                  layer.odfLayer.setVisible(newVisible);
                } catch (error) {
                  console.warn(
                    `⚠️ Failed to toggle ODF layer visibility for ${layerId}:`,
                    error,
                  );
                }
              }

              set(
                (state) => ({
                  layers: state.layers.map((l) =>
                    l.id === layerId ? { ...l, visible: newVisible } : l,
                  ),
                }),
                false,
                "toggleLayerVisibility",
              );
            }
          },

          setLayerFilter: (id: string, filter: string) =>
            set(
              (state) => ({
                layers: state.layers.map((layer) =>
                  layer.id === id ? { ...layer, filter } : layer,
                ),
              }),
              false,
              "setLayerFilter",
            ),

          setSelectedLayer: (layerId: string) =>
            set(
              () => ({
                selectedLayerId: layerId,
              }),
              false,
              "setSelectedLayer",
            ),

          clearLayers: () =>
            set(
              () => ({
                layers: [],
                selectedLayerId: undefined,
                expandedGroups: new Set(),
              }),
              false,
              "clearLayers",
            ),

          // 🆕 Draw/Measure/Clear 레이어 전용 헬퍼 메서드 구현
          getLayerByType: (type: "draw" | "measure" | "clear") => {
            const state = get();
            return state.layers.find((layer) => layer.type === type) || null;
          },

          getLayerById: (layerId: string) => {
            const state = get();
            return state.layers.find((layer) => layer.id === layerId) || null;
          },

          getLayerTypeById: (layerId: string) => {
            const state = get();
            const layer = state.layers.find((layer) => layer.id === layerId);
            if (!layer) return "other";

            if (["draw", "measure", "clear"].includes(layer.type)) {
              return layer.type as "draw" | "measure" | "clear";
            }
            return "other";
          },

          addDrawLayer: (
            drawLayer: any,
            options: { name?: string; visible?: boolean } = {},
          ) => {
            const { name = "Draw Layer", visible = true } = options;
            const layer: Layer = {
              id: `draw_${Date.now()}`,
              name,
              type: "draw",
              visible,
              zIndex: 1000, // Draw 레이어는 높은 우선순위
              odfLayer: drawLayer,
              params: {
                drawOptions: {}, // 필요시 그리기 옵션 저장
              },
            };

            set(
              (state) => ({
                layers: [
                  ...state.layers.filter((l) => l.type !== "draw"),
                  layer,
                ], // 기존 draw 레이어 교체
              }),
              false,
              "addDrawLayer",
            );
          },

          addMeasureLayer: (
            measureLayer: any,
            options: { name?: string; visible?: boolean } = {},
          ) => {
            const { name = "Measure Layer", visible = true } = options;
            const layer: Layer = {
              id: `measure_${Date.now()}`,
              name,
              type: "measure",
              visible,
              zIndex: 1001, // Measure 레이어는 Draw보다 높은 우선순위
              odfLayer: measureLayer,
              params: {
                measureOptions: {}, // 필요시 측정 옵션 저장
              },
            };

            set(
              (state) => ({
                layers: [
                  ...state.layers.filter((l) => l.type !== "measure"),
                  layer,
                ], // 기존 measure 레이어 교체
              }),
              false,
              "addMeasureLayer",
            );
          },

          addClearLayer: (
            clearLayer: any,
            options: { name?: string; visible?: boolean } = {},
          ) => {
            const { name = "Clear Layer", visible = true } = options;
            const layer: Layer = {
              id: `clear_${Date.now()}`,
              name,
              type: "clear",
              visible,
              zIndex: 1002, // Clear 레이어는 가장 높은 우선순위
              odfLayer: clearLayer,
              params: {
                clearOptions: {}, // 필요시 정리 옵션 저장
              },
            };

            set(
              (state) => ({
                layers: [
                  ...state.layers.filter((l) => l.type !== "clear"),
                  layer,
                ], // 기존 clear 레이어 교체
              }),
              false,
              "addClearLayer",
            );
          },

          // 🆕 배경 레이어 액션 구현
          setCurrentBaseLayer: (baseLayerId: string | null) => {
            console.log("🔄 setCurrentBaseLayer called:", {
              from: get().currentBaseLayerId,
              to: baseLayerId,
            });

            // 기존 배경 레이어 숨기기
            const currentId = get().currentBaseLayerId;
            if (currentId) {
              console.log("👁️ Hiding current base layer:", currentId);
              get().updateLayer(currentId, { visible: false });
            }

            // 새 배경 레이어 활성화
            if (baseLayerId) {
              console.log("👁️ Showing new base layer:", baseLayerId);
              get().updateLayer(baseLayerId, { visible: true });
            }

            set(
              { currentBaseLayerId: baseLayerId },
              false,
              "setCurrentBaseLayer",
            );
            console.log("✅ setCurrentBaseLayer completed:", baseLayerId);
          },

          addBaseLayerOption: (baseLayer: BaseLayerInfo) => {
            set(
              (state) => ({
                availableBaseLayers: [...state.availableBaseLayers, baseLayer],
              }),
              false,
              "addBaseLayerOption",
            );

            // 카테고리별 분류 업데이트
            const categoryMap = new Map(get().baseLayersByCategory);
            const category = baseLayer.category || "기타";
            const categoryLayers = categoryMap.get(category) || [];
            categoryMap.set(category, [...categoryLayers, baseLayer]);

            set(
              { baseLayersByCategory: categoryMap },
              false,
              "updateBaseLayersByCategory",
            );
          },

          removeBaseLayerOption: (baseLayerId: string) => {
            const state = get();
            const baseLayer = state.availableBaseLayers.find(
              (layer) => layer.id === baseLayerId,
            );

            set(
              (state) => ({
                availableBaseLayers: state.availableBaseLayers.filter(
                  (layer) => layer.id !== baseLayerId,
                ),
              }),
              false,
              "removeBaseLayerOption",
            );

            // 카테고리별 분류에서도 제거
            if (baseLayer) {
              const categoryMap = new Map(get().baseLayersByCategory);
              const category = baseLayer.category || "기타";
              const categoryLayers = categoryMap.get(category) || [];
              categoryMap.set(
                category,
                categoryLayers.filter((layer) => layer.id !== baseLayerId),
              );

              set(
                { baseLayersByCategory: categoryMap },
                false,
                "updateBaseLayersByCategory",
              );
            }

            // 현재 활성 배경 레이어가 제거된 경우 초기화
            if (state.currentBaseLayerId === baseLayerId) {
              get().setCurrentBaseLayer(null);
            }
          },

          setAvailableBaseLayers: (baseLayers: BaseLayerInfo[]) => {
            set(
              { availableBaseLayers: baseLayers },
              false,
              "setAvailableBaseLayers",
            );

            // 카테고리별 분류 재구성
            const categoryMap = new Map<string, BaseLayerInfo[]>();
            baseLayers.forEach((baseLayer) => {
              const category = baseLayer.category || "기타";
              const categoryLayers = categoryMap.get(category) || [];
              categoryMap.set(category, [...categoryLayers, baseLayer]);
            });

            set(
              { baseLayersByCategory: categoryMap },
              false,
              "updateBaseLayersByCategory",
            );
          },

          getBaseLayerById: (baseLayerId: string) => {
            const state = get();
            return (
              state.availableBaseLayers.find(
                (layer) => layer.id === baseLayerId,
              ) || null
            );
          },

          getBaseLayersByCategory: (category: string) => {
            const state = get();
            return state.baseLayersByCategory.get(category) || [];
          },

          // 🆕 오버레이/하이브리드 레이어 액션 구현
          addActiveOverlayLayer: (overlayLayerId: string) => {
            set(
              (state) => ({
                activeOverlayLayerIds: [
                  ...state.activeOverlayLayerIds,
                  overlayLayerId,
                ],
              }),
              false,
              "addActiveOverlayLayer",
            );
          },

          removeActiveOverlayLayer: (overlayLayerId: string) => {
            set(
              (state) => ({
                activeOverlayLayerIds: state.activeOverlayLayerIds.filter(
                  (id) => id !== overlayLayerId,
                ),
              }),
              false,
              "removeActiveOverlayLayer",
            );
          },

          toggleOverlayLayer: (overlayLayerId: string) => {
            const state = get();
            const isActive =
              state.activeOverlayLayerIds.includes(overlayLayerId);

            if (isActive) {
              get().removeActiveOverlayLayer(overlayLayerId);
              // 레이어 숨기기
              get().updateLayer(overlayLayerId, { visible: false });
            } else {
              get().addActiveOverlayLayer(overlayLayerId);
              // 레이어 보이기
              get().updateLayer(overlayLayerId, { visible: true });
            }
          },

          setAvailableOverlayLayers: (overlayLayers: BaseLayerInfo[]) => {
            set(
              { availableOverlayLayers: overlayLayers },
              false,
              "setAvailableOverlayLayers",
            );

            // 카테고리별 분류 재구성
            const categoryMap = new Map<string, BaseLayerInfo[]>();
            overlayLayers.forEach((overlayLayer) => {
              const category = overlayLayer.category || "기타";
              const categoryLayers = categoryMap.get(category) || [];
              categoryMap.set(category, [...categoryLayers, overlayLayer]);
            });

            set(
              { overlayLayersByCategory: categoryMap },
              false,
              "updateOverlayLayersByCategory",
            );
          },

          getOverlayLayerById: (overlayLayerId: string) => {
            const state = get();
            return (
              state.availableOverlayLayers.find(
                (layer) => layer.id === overlayLayerId,
              ) || null
            );
          },

          getOverlayLayersByCategory: (category: string) => {
            const state = get();
            return state.overlayLayersByCategory.get(category) || [];
          },

          isOverlayLayerActive: (overlayLayerId: string) => {
            const state = get();
            return state.activeOverlayLayerIds.includes(overlayLayerId);
          },

          // LayerFactory 설정
          setLayerFactory: (factory: LayerFactory | null) =>
            set({ layerFactory: factory }, false, "setLayerFactory"),
        }),
        "layer-store",
      ),
      {
        name: "layer-store",
      },
    ),
  );
};

/**
 * 🎯 전역 레이어 스토어 (하위 호환성)
 *
 * 기존 코드와의 호환성을 위해 유지합니다.
 * 단일 지도 사용 시에는 이 스토어를 사용합니다.
 */
export const useLayerStore = createLayerStore();
