import { Log } from "./Log";

export type RequestContentType =
  | "application/json"
  | "application/x-www-form-urlencoded";

export type ResponseContentType = "json" | "blob";

export interface ApiResponse<T = any> {
  code: number;
  message: string;
  result: T;
}

/** ### 응답 형식 처리를 위한 헬퍼 함수 (e.g. json, blob 등) */
const responseProcessors = {
  json: async (response: Response) => {
    const contentType = response.headers.get("content-type") || "";
    const status = response.status;

    // 비정상 상태코드: 원문 텍스트도 확보
    if (!response.ok) {
      const bodyText = await response.text().catch(() => "");
      throw new Error(
        `HTTP ${status} ${response.statusText} (ct=${contentType}) body=${bodyText.slice(0, 500)}`,
      );
    }

    // 빈 응답 처리
    const contentLength = response.headers.get("content-length");
    if (status === 204 || contentLength === "0") {
      return null; // 혹은 {} 등, 호출부 계약에 맞춰 결정
    }

    // Content-Type이 json이 아니면 원문 확인
    if (!contentType.includes("application/json")) {
      const bodyText = await response.text().catch(() => "");
      throw new Error(
        `Non-JSON response (ct=${contentType}) body=${bodyText.slice(0, 500)}`,
      );
    }

    // JSON 파싱 시도 + 실패시 원문 일부 포함하여 에러
    const raw = await response.text();
    if (!raw) return null;
    try {
      return JSON.parse(raw);
    } catch (e) {
      throw new Error(
        `Invalid JSON: ${(e as Error).message} body=${raw.slice(0, 500)}`,
      );
    }
  },
  blob: async (response: Response) => {
    const blob = await response.blob();
    return { headers: response.headers, blob };
  },
} as const;

function encodeBody(
  body: Record<string, any>,
  contentType: RequestContentType,
): string {
  return contentType === "application/json"
    ? JSON.stringify(body)
    : new URLSearchParams(body as Record<string, string>).toString();
}

/** ### API 응답 처리 공통 함수 */
async function handleApiResponse<T>(
  res: Response,
  responseContentType: ResponseContentType,
): Promise<T> {
  const data = await responseProcessors[responseContentType](res);

  // JSON 응답이고 code 필드가 있을 때만 검사
  if (
    responseContentType === "json" &&
    data &&
    typeof data === "object" &&
    "code" in data &&
    "message" in data
  ) {
    const apiData = data as ApiResponse<T>;
    if (apiData.code !== 200) {
      throw new Error(`${apiData.message} (code: ${apiData.code})`);
    }
  }

  // ApiResponse 형태가 아니면 그냥 반환
  return data as T;
}

export const fetcher = {
  /** GET */
  get: async <T = unknown>(
    url: string,
    params?: Record<string, any>,
    responseContentType: ResponseContentType = "json",
  ): Promise<T> => {
    // 원본 객체에서 undefined 제거
    for (const key in params) {
      if (params[key] === undefined) {
        delete params[key];
      }
    }
    const queryString = params
      ? Object.entries(params)
          .map(
            ([key, value]) =>
              `${encodeURIComponent(key)}=${encodeURIComponent(value)}`,
          )
          .join("&")
      : "";

    const fullUrl = queryString
      ? url.includes("?")
        ? `${url}&${queryString}`
        : `${url}?${queryString}`
      : url;

    Log.logRequest("GET", fullUrl);

    try {
      // 🎯 서버 사이드에서 필요한 헤더 추가
      const headers: HeadersInit = {
        Accept: "application/json",
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      };

      // 서버 사이드에서만 Referer/Origin 추가
      if (typeof window === "undefined") {
        const origin =
          (globalThis as any).process?.env?.NEXT_PUBLIC_SITE_ORIGIN ||
          "http://localhost:3001";
        headers["Referer"] = origin;
        headers["Origin"] = origin;
      }

      const res = await fetch(fullUrl, { headers });

      const data = await handleApiResponse<T>(res, responseContentType);
      Log.logResponse("GET", fullUrl, data);
      return data;
    } catch (error) {
      Log.logError("GET", fullUrl, error);
      throw error;
    }
  },

  /** POST */
  post: async <
    T = unknown,
    B extends Record<string, any> = Record<string, any>,
  >(
    url: string,
    body: B,
    requestContentType: RequestContentType = "application/json",
    responseContentType: ResponseContentType = "json",
  ): Promise<T> => {
    Log.logRequest("POST", url, body);

    try {
      const headers: HeadersInit = {
        "Content-Type": requestContentType,
        Accept: "application/json",
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      };

      // 서버 사이드에서만 Referer/Origin 추가
      if (typeof window === "undefined") {
        const origin =
          (globalThis as any).process?.env?.NEXT_PUBLIC_SITE_ORIGIN ||
          "http://localhost:3001";
        headers["Referer"] = origin;
        headers["Origin"] = origin;
      }

      const encodedBody = encodeBody(body, requestContentType);

      const res = await fetch(url, {
        method: "POST",
        headers,
        body: encodedBody,
      });

      const data = await handleApiResponse<T>(res, responseContentType);
      Log.logResponse("POST", url, data);
      return data;
    } catch (error) {
      Log.logError("POST", url, error);
      throw error;
    }
  },

  /** UPLOAD (multipart/form-data) */
  upload: async <T = unknown>(
    url: string,
    queryParams: Record<string, string | number> = {},
    files: { field: string; file: File | Blob }[],
    extraFields?: Record<string, string | number>,
    responseContentType: ResponseContentType = "json",
  ): Promise<T> => {
    const queryString = queryParams
      ? Object.entries(queryParams)
          .map(
            ([key, value]) =>
              `${encodeURIComponent(key)}=${encodeURIComponent(value)}`,
          )
          .join("&")
      : "";

    const fullUrl = queryString
      ? url.includes("?")
        ? `${url}&${queryString}`
        : `${url}?${queryString}`
      : url;

    Log.logRequest("UPLOAD", fullUrl, { files, extraFields });

    try {
      const formData = new FormData();

      files.forEach(({ field, file }) => {
        formData.append(field, file);
      });

      if (extraFields) {
        Object.entries(extraFields).forEach(([key, value]) => {
          formData.append(key, String(value));
        });
      }

      const res = await fetch(fullUrl, {
        method: "POST",
        body: formData,
      });

      const data = await handleApiResponse<T>(res, responseContentType);
      Log.logResponse("UPLOAD", fullUrl, data);
      return data;
    } catch (error) {
      Log.logError("UPLOAD", fullUrl, error);
      throw error;
    }
  },
};
