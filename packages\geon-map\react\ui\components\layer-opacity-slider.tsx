import { useLayer } from "@geon-map/react-odf";
import { Slider } from "@geon-ui/react/primitives/slider";
import { cn } from "@geon-ui/react/utils";
import React, { useCallback } from "react";

// 그룹 투명도 슬라이더 Props
export interface LayerOpacitySliderProps
  extends React.HTMLAttributes<HTMLDivElement> {
  layerIds: string[];
  value?: number;
  onValueChange?: (value: number, layerIds: string[]) => void;
}

export const LayerOpacitySlider = React.forwardRef<
  HTMLDivElement,
  LayerOpacitySliderProps
>(({ layerIds, value = 1, onValueChange, className, ...props }, ref) => {
  const { setOpacity } = useLayer();
  const handleValueChange = useCallback(
    (value: number[]) => {
      layerIds.forEach((id) => {
        setOpacity(id, value[0] ?? 1);
      });
      onValueChange?.(value[0] ?? 1, layerIds);
    },
    [layerIds, setOpacity, onValueChange],
  );
  return (
    <div
      ref={ref}
      className={cn("flex items-center gap-2 w-20", className)}
      {...props}
    >
      <Slider
        className="w-16 h-2 relative"
        min={0}
        max={1}
        step={0.1}
        value={[value]}
        onValueChange={handleValueChange}
        // 이벤트 버블링 방지
        onPointerDown={(e) => e.stopPropagation()}
        onClick={(e) => e.stopPropagation()}
      ></Slider>
    </div>
  );
});
LayerOpacitySlider.displayName = "LayerOpacitySlider";
