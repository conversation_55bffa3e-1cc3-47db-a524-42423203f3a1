import React from "react";

import { type FormState } from "./view";

export default function Edit({
  form,
  setForm,
  onSubmit,
  isPending,
  onCancel,
  children, // 확장용
}: {
  form: FormState;
  setForm: React.Dispatch<React.SetStateAction<FormState>>;
  onSubmit: (e: React.FormEvent) => void;
  isPending: boolean;
  onCancel: () => void;
  children?: React.ReactNode;
}) {
  // 필드 정의
  const textFields: { label: string; key: keyof FormState }[] = [
    { label: "제목", key: "nttSj" },
  ];

  const textareaFields: { label: string; key: keyof FormState }[] = [
    { label: "내용", key: "nttCn" },
  ];

  const checkboxFields: { label: string; key: keyof FormState }[] = [
    // { label: "팝업 사용", key: "popupAt" },
  ];

  const dateFields: { label: string; key: keyof FormState }[] = [
    // { label: "팝업 시작", key: "popupBeginDt" },
    // { label: "팝업 종료", key: "popupEndDt" },
  ];

  return (
    <form id="dataspace-edit-form" onSubmit={onSubmit} className="space-y-4">
      <div className="space-y-3 rounded-md border bg-white p-4">
        {/* 텍스트 입력 */}
        {textFields.map(({ label, key }) => (
          <label key={key} className="block text-sm">
            <span className="mb-1 block font-medium">{label}</span>
            <input
              className="w-full rounded border px-3 py-2"
              value={form[key] as string}
              onChange={(e) =>
                setForm((f) => ({ ...f, [key]: e.target.value }) as FormState)
              }
            />
          </label>
        ))}

        {/* 텍스트 에어리어 */}
        {textareaFields.map(({ label, key }) => (
          <label key={key} className="block text-sm">
            <span className="mb-1 block font-medium">{label}</span>
            <textarea
              className="min-h-[200px] w-full rounded border px-3 py-2"
              value={form[key] as string}
              onChange={(e) =>
                setForm((f) => ({ ...f, [key]: e.target.value }) as FormState)
              }
            />
          </label>
        ))}

        {/* 체크박스 */}
        <div className="grid grid-cols-2 gap-3">
          {checkboxFields.map(({ label, key }) => (
            <label key={key} className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={form[key] as boolean}
                onChange={(e) =>
                  setForm((f) => ({
                    ...f,
                    [key]: e.target.checked,
                  }))
                }
              />
              {label}
            </label>
          ))}
        </div>

        {/* 날짜 */}
        <div className="grid grid-cols-2 gap-3">
          {dateFields.map(({ label, key }) => (
            <label key={key} className="block text-sm">
              <span className="mb-1 block font-medium">{label}</span>
              <input
                type="datetime-local"
                className="w-full rounded border px-3 py-2"
                value={form[key] as string}
                onChange={(e) =>
                  setForm((f) => ({ ...f, [key]: e.target.value }) as FormState)
                }
              />
            </label>
          ))}
        </div>
        {/* 확장 영역 (파일 업로드 등) */}
        {children}
      </div>

      {/* 버튼 영역 */}
      <div className="flex gap-2">
        <button
          type="button"
          className="rounded-md border px-3 py-1 text-sm hover:bg-gray-50"
          onClick={onCancel}
        >
          취소
        </button>
        <button
          type="submit"
          className="rounded-md border bg-blue-600 px-3 py-1 text-sm text-white hover:bg-blue-700"
          disabled={isPending}
        >
          {isPending ? "저장 중..." : "저장"}
        </button>
      </div>
    </form>
  );
}
