"use client";

import { cn } from "@geon-ui/react/lib/utils";

import { useDialog } from "./draggable-dialog";

interface DialogContentProps {
  children: React.ReactNode;
  className?: string;
  padding?: boolean;
}

export function DialogContent({
  children,
  className,
  padding = true,
}: DialogContentProps) {
  const { isMaximized } = useDialog();

  return (
    <div
      className={cn(
        "flex-1 overflow-auto bg-white",
        // 패딩 설정
        padding && "p-6",
        // 최대화 상태에서의 스타일 조정
        isMaximized && "h-full",
        className,
      )}
    >
      {children}
    </div>
  );
}
