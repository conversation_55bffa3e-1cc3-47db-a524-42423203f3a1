"use client";

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@geon-ui/react/primitives/sidebar";
import Link from "next/link";

import { ADMIN_MENUS } from "../../_utils";

export default function NavMenu() {
  return (
    <>
      {ADMIN_MENUS.map((item) => {
        return (
          <SidebarGroup key={item.id}>
            <SidebarGroupLabel>{item.title}</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {item.items?.map((subItem) => (
                  <SidebarMenuSubItem key={subItem.id}>
                    <SidebarMenuSubButton
                      asChild
                      onClick={() => {
                        // setInnerOpen(true);
                        // outer.setOpen(false);
                        // setSelectedServiceId(`${item.id}:${subItem.id}`);
                      }}
                    >
                      <Link href={`/admin/${item.id}/${subItem.id}`}>
                        {subItem.title}
                      </Link>
                    </SidebarMenuSubButton>
                  </SidebarMenuSubItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        );
      })}
    </>
  );
}
