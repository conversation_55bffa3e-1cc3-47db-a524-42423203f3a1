"use client";

import { cn } from "@geon-ui/react/lib/utils";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@geon-ui/react/primitives/table";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";

export interface ViewTableProps<TData, TValue>
  extends React.ComponentProps<typeof Table> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  pinHeader?: boolean;
}
/**
 * 데이터 조회를 위한 `@tanstack/react-table` 테이블
 *
 * 선택/정렬 등의 기능이 없는 단순 조회 테이블
 */
export default function ViewTable<TData, TValue>({
  columns,
  data,
  pinHeader,
  className,
  ...props
}: ViewTableProps<TData, TValue>) {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <Table
      className={cn("w-full overflow-hidden overflow-y-auto", className)}
      {...props}
    >
      <TableHeader
        className={cn("bg-background", pinHeader && "sticky top-0 z-10")}
      >
        {table.getHeaderGroups().map((headerGroup) => (
          <TableRow key={headerGroup.id}>
            {headerGroup.headers.map((header) => (
              <TableHead key={header.id}>
                {header.isPlaceholder
                  ? null
                  : flexRender(
                      header.column.columnDef.header,
                      header.getContext(),
                    )}
              </TableHead>
            ))}
          </TableRow>
        ))}
      </TableHeader>
      <TableBody>
        {table.getRowModel().rows?.length ? (
          table.getRowModel().rows.map((row) => (
            <TableRow
              key={row.id}
              data-state={row.getIsSelected() && "selected"}
            >
              {row.getVisibleCells().map((cell) => (
                <TableCell key={cell.id}>
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </TableCell>
              ))}
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell colSpan={columns.length} className="h-24 text-center">
              No results.
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}
