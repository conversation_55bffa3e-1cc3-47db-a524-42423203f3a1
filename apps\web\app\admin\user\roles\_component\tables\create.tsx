"use client";

import {
  type APIResponseType,
  Author,
  createGeonMagpClient,
  type MagpClient,
} from "@geon-query/model";
import {
  useAppMutation,
  useAppQuery,
  useAppQueryClient,
} from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import React from "react";

interface CreateProps {
  mergeUserId: string;
  userIdList: string[];
  listQueryKey: any;
}

export default function Create({
  mergeUserId,
  userIdList,
  listQueryKey: queryKey,
}: CreateProps) {
  const client = createGeonMagpClient();
  const qc = useAppQueryClient();

  // 권한 옵션 목록 쿼리
  const {
    data: authorOptionList,
    isLoading: isAuthorOptionListLoading,
    error: authorOptionListError,
    isError: isAuthorOptionListError,
  } = useAppQuery<APIResponseType<MagpClient["author"]["itemList"]>>({
    queryKey: ["magp/admin/author/item"],
    queryFn: () => client.author.itemList({}),
  });

  const [selectedAuthor, setSelectedAuthor] = React.useState<string>("");

  // 권한 등록 뮤테이션
  const authorInsertMutation = useAppMutation({
    mutationFn: async () => {
      return client.author.insert({
        mergeUserId,
        userIdList,
        authorIdList: [selectedAuthor],
      });
    },
    onSuccess: (data) => {
      if (data?.result !== 0) {
        alert(`${userIdList[0]}에게 권한을 부여했습니다.`);
        qc.invalidateQueries(queryKey);
      } else {
        alert(`이미 해당 권한이 ${userIdList[0]} 에게 존재합니다.`);
      }
    },
    onError: (err: any) => {
      console.error(err);
    },
  });

  if (isAuthorOptionListLoading) return <Skeleton className="size-full" />;
  if (
    isAuthorOptionListError ||
    !authorOptionList ||
    typeof authorOptionList.result === "string"
  )
    return (
      <div className="text-destructive flex justify-center align-middle">
        Error loading notices: {authorOptionListError as string}
        {authorOptionList &&
          `, ${authorOptionList?.result as unknown as string}`}
      </div>
    );

  return (
    <div className="flex items-center gap-2">
      <select
        value={selectedAuthor}
        onChange={(e) => setSelectedAuthor(e.target.value)}
      >
        <option value="">권한 선택</option>
        {(authorOptionList?.result ?? []).map((authorOption: Author) => (
          <option
            key={authorOption.authorGroupId}
            value={authorOption.authorGroupId}
          >
            {authorOption.authorGroupNm}
          </option>
        ))}
      </select>
      <button
        onClick={() => {
          if (!selectedAuthor) return alert("권한을 선택하세요");

          authorInsertMutation.mutate();
        }}
        className="ml-4 rounded bg-blue-500 px-2 py-1 text-xs text-white hover:bg-blue-600"
      >
        추가
      </button>
    </div>
  );
}
