"use client";

import { useDownload } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import { Popover, PopoverTrigger } from "@geon-ui/react/primitives/popover";
import { Download as DownloadIcon, FileDown, ImageDown } from "lucide-react";
import * as React from "react";

import {
  ToolbarContent,
  ToolbarItem,
  ToolbarTrigger,
} from "./base/toolbar-item";

export type DownloadType = "png" | "pdf";

// Props for compound components
export interface ToolbarDownloadProps
  extends React.HTMLAttributes<HTMLDivElement> {
  /** 다운로드 대상 요소 선택자 */
  targetElementSelector?: string;
  /** 제외할 요소 선택자들 */
  exceptionElementSelectors?: string[];
}

export interface ToolbarDownloadTriggerProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 툴팁 텍스트 */
  tooltip?: string;
  /** 버튼 크기 */
  size?: "sm" | "lg" | "default" | "icon";
}

export interface ToolbarDownloadContentProps
  extends React.HTMLAttributes<HTMLDivElement> {}

export interface ToolbarDownloadItemProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 다운로드 타입 */
  downloadType: DownloadType;
}

// Download Context 생성
interface DownloadContextValue {
  downloadPDF: () => void;
  downloadImage: () => void;
}

const DownloadContext = React.createContext<DownloadContextValue | null>(null);

export const useDownloadContext = () => {
  const context = React.useContext(DownloadContext);
  if (!context) {
    throw new Error(
      "ToolbarDownload components must be used within ToolbarDownload",
    );
  }
  return context;
};

// Main ToolbarDownload Container
export const ToolbarDownload = React.forwardRef<
  HTMLDivElement,
  ToolbarDownloadProps
>(
  (
    {
      targetElementSelector,
      exceptionElementSelectors,
      className,
      children,
      ...props
    },
    ref,
  ) => {
    const { downloadPDF, downloadImage } = useDownload({
      targetElementSelector,
      exceptionElementSelectors,
    });

    const contextValue = React.useMemo(
      () => ({
        downloadPDF,
        downloadImage,
      }),
      [downloadPDF, downloadImage],
    );

    return (
      <DownloadContext.Provider value={contextValue}>
        <ToolbarItem ref={ref} className={className} {...props}>
          <Popover>
            <PopoverTrigger asChild>
              {React.Children.toArray(children).find(
                (child) =>
                  React.isValidElement(child) &&
                  child.type === ToolbarDownloadTrigger,
              )}
            </PopoverTrigger>
            {React.Children.toArray(children).find(
              (child) =>
                React.isValidElement(child) &&
                child.type === ToolbarDownloadContent,
            )}
          </Popover>
        </ToolbarItem>
      </DownloadContext.Provider>
    );
  },
);

ToolbarDownload.displayName = "ToolbarDownload";

// ToolbarDownloadTrigger Component
export const ToolbarDownloadTrigger = React.forwardRef<
  HTMLButtonElement,
  ToolbarDownloadTriggerProps
>(
  (
    { tooltip = "다운로드", size = "default", className, children, ...props },
    ref,
  ) => {
    return (
      <ToolbarTrigger
        ref={ref}
        tooltip={tooltip}
        size={size}
        className={className}
        {...props}
      >
        {children || <DownloadIcon className="h-4 w-4" />}
      </ToolbarTrigger>
    );
  },
);

ToolbarDownloadTrigger.displayName = "ToolbarDownloadTrigger";

// ToolbarDownloadContent Component
export const ToolbarDownloadContent = React.forwardRef<
  HTMLDivElement,
  ToolbarDownloadContentProps
>(({ className, children, ...props }, ref) => {
  return (
    <ToolbarContent
      ref={ref}
      align="center"
      sideOffset={16}
      className={cn("w-auto flex flex-row gap-2 p-3", className)}
      {...props}
    >
      {children}
    </ToolbarContent>
  );
});

ToolbarDownloadContent.displayName = "ToolbarDownloadContent";

// ToolbarDownloadItem Component
export const ToolbarDownloadItem = React.forwardRef<
  HTMLButtonElement,
  ToolbarDownloadItemProps
>(({ downloadType, className, children, onClick, ...props }, ref) => {
  const { downloadPDF, downloadImage } = useDownloadContext();

  const handleDownload = React.useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      switch (downloadType) {
        case "png":
          downloadImage();
          break;
        case "pdf":
          downloadPDF();
          break;
        default:
          console.error("Invalid download type:", downloadType);
          break;
      }
      onClick?.(e);
    },
    [downloadType, downloadPDF, downloadImage, onClick],
  );

  const getIcon = () => {
    switch (downloadType) {
      case "png":
        return <ImageDown className="h-4 w-4" />;
      case "pdf":
        return <FileDown className="h-4 w-4" />;
      default:
        return <DownloadIcon className="h-4 w-4" />;
    }
  };

  const getLabel = () => {
    switch (downloadType) {
      case "png":
        return "PNG";
      case "pdf":
        return "PDF";
      default:
        return downloadType.toUpperCase();
    }
  };

  return (
    <Button
      ref={ref}
      variant="ghost"
      size="sm"
      className={cn(
        "flex flex-col items-center justify-center gap-1 h-auto px-3 py-2 min-w-12",
        className,
      )}
      onClick={handleDownload}
      {...props}
    >
      {children || (
        <>
          {getIcon()}
          <span className="text-xs font-medium">{getLabel()}</span>
        </>
      )}
    </Button>
  );
});

ToolbarDownloadItem.displayName = "ToolbarDownloadItem";
