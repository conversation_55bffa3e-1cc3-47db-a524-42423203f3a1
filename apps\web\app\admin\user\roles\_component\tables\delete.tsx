"use client";

import { createGeonMagpClient, UserAuthorMgmtRequest } from "@geon-query/model";
import { useAppMutation, useAppQueryClient } from "@geon-query/react-query";
import React from "react";

interface DeleteProps extends UserAuthorMgmtRequest {
  listQueryKey: any;
}
export default function Delete({
  mergeUserId,
  userIdList,
  authorIdList,
  listQueryKey: queryKey,
}: DeleteProps) {
  const client = createGeonMagpClient();
  const qc = useAppQueryClient();

  const userAuthorRelateDeleteMutation = useAppMutation({
    mutationFn: async () => {
      return client.author.delete({
        mergeUserId,
        userIdList,
        authorIdList,
      });
    },
    onSuccess: (data) => {
      if (data?.result !== 0) {
        alert(`${userIdList[0]}의 권한을 삭제했습니다.`);
        qc.invalidateQueries(queryKey);
      } else {
        alert(
          `${userIdList[0]}에게 권한이 존재하지 않습니다. 관리자에게 문의하세요.`,
        );
      }
    },
    onError: (err: any) => {
      console.error(err);
    },
  });

  return (
    <button
      onClick={() => {
        userAuthorRelateDeleteMutation.mutate();
      }}
      className="ml-4 rounded bg-red-500 px-2 py-1 text-xs text-white hover:bg-red-600"
    >
      제거
    </button>
  );
}
