import { ControlsProvider, MapProvider } from "@geon-map/react-odf";
import React from "react";

export default function WidgetLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <MapProvider defaultOptions={{ projection: "EPSG:5186" }}>
      <ControlsProvider
        scaleOptions={{ size: 100, scaleInput: false }}
        clearOptions={{ clearAll: true }}
        measureOptions={{
          tools: ["distance", "area", "round", "spot"],
          continuity: false,
        }}
        overviewOptions={{ enabled: true }}
        drawOptions={{
          continuity: false,
          tools: [
            "text",
            "polygon",
            "lineString",
            "box",
            "point",
            "circle",
            "curve",
          ],
          style: {
            fill: { color: [255, 255, 0, 1] },
            stroke: { color: [0, 255, 0, 0.8], width: 5 },
          },
        }}
      >
        {children}
      </ControlsProvider>
    </MapProvider>
  );
}
