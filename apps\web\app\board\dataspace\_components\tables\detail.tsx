// app/board/dataspace/[id]/page.tsx

"use client";

import {
  type APIResponseType,
  createGeonMagpClient,
  type MagpClient,
} from "@geon-query/model";
import {
  useAppMutation,
  useAppQuery,
  useAppQueryClient,
} from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import { useRouter } from "next/navigation";

import { PaginationLink } from "@/app/board/_components/pagination-link";

import View, { FormState } from "./view";

//TODO 수정자 아이디 store에서 호출
const userId = "admin";
type Props = { id: string };
export default function Detail({ id }: Props) {
  const client = createGeonMagpClient();
  const router = useRouter();

  const { data, isLoading, isError, error } = useAppQuery<
    APIResponseType<MagpClient["dataspace"]["select"]>
  >({
    queryKey: ["magp/dataspace", { nttId: id }],
    queryFn: () => client.dataspace.select({ nttId: id }),
    enabled: Boolean(id),
  });
  const qc = useAppQueryClient(); // ✅ 래핑된 훅 사용
  const delMutation = useAppMutation({
    mutationFn: async () =>
      client.dataspace.delete({ nttId: id, updusrId: userId }),
    onSuccess: () => {
      qc.invalidateQueries({ queryKey: ["magp/dataspace"] });
      router.push(`/board/dataspace`);
    },
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError || !data || typeof data.result === "string") {
    return <div className="text-red-500">Error: {String(error)}</div>;
  }

  const form: FormState = {
    nttSj: data.result?.nttSj ?? "",
    nttCn: data.result?.nttCn ?? "",
    upperExpsrAt: data.result?.upperExpsrAt === "Y",
    smsSndngAt: data.result?.smsSndngAt === "Y",
    othbcAt: data.result?.othbcAt === "Y",
    pstgBeginDt: data.result?.pstgBeginDt?.slice(0, 10) ?? "",
    linkUrl: data.result?.linkUrl ?? "",
    popupAt: data.result?.popupAt === "Y",
    popupBeginDt: data.result?.popupBeginDt?.slice(0, 10) ?? "",
    popupEndDt: data.result?.popupEndDt?.slice(0, 10) ?? "",
    popupPortalExpsrAt: data.result?.popupPortalExpsrAt === "Y",
    popupInsttExpsrAt: data.result?.popupInsttExpsrAt === "Y",
    atchmnflId: data.result?.atchmnflId || null,
    attachment: data.result?.attachment || null,
    registerId: userId,
    updusrId: userId,
  };

  return (
    <div className="mx-auto w-full max-w-5xl space-y-6 p-4">
      <div className="flex items-center justify-between">
        <h1 className="text-xl font-semibold">데이터공간 상세</h1>
        <div className="flex gap-2">
          <PaginationLink
            href={`/board/dataspace/${id}/edit`}
            className="rounded-md border px-3 py-1 text-sm hover:bg-gray-50"
          >
            수정
          </PaginationLink>
          <button
            className="rounded-md border px-3 py-1 text-sm text-red-600 hover:bg-red-50"
            onClick={() => {
              if (confirm("삭제하시겠습니까?")) delMutation.mutate();
            }}
            disabled={delMutation.isPending}
          >
            {delMutation.isPending ? "삭제 중..." : "삭제"}
          </button>
          <PaginationLink
            href={`/board/dataspace`}
            className="rounded-md border px-3 py-1 text-sm hover:bg-gray-50"
          >
            목록으로
          </PaginationLink>
        </div>
      </div>

      <View form={form} />
    </div>
  );
}
