"use client";

import { cn } from "@geon-ui/react/lib/utils";
import * as React from "react";

export interface SwipeDragHandleProps
  extends React.HTMLAttributes<HTMLDivElement> {
  /** 스와이프 값 (0-100) */
  value: number;
  /** 값 변경 콜백 */
  onValueChange: (value: number) => void;
  /** 맵 크기 [width, height] (useSwipe에서 제공) */
  mapSize?: [number, number] | null;
  /** 드래그 가능 여부 */
  enabled?: boolean;
  /** 표시 여부 */
  visible?: boolean;
}

export const SwipeDragHandle = React.forwardRef<
  HTMLDivElement,
  SwipeDragHandleProps
>(
  (
    {
      value,
      onValueChange,
      mapSize,
      enabled = true,
      visible = true,
      className,
      ...props
    },
    ref,
  ) => {
    const [isDragging, setIsDragging] = React.useState(false);
    const handleRef = React.useRef<HTMLDivElement>(null);
    const containerRef = React.useRef<HTMLDivElement>(null);

    // mapSize 기준으로 계산 (useSwipe와 동일한 기준)
    const mapWidth = mapSize?.[0] || 800;

    // swipeValue (0-100%)를 mapWidth pixel 위치로 변환
    const pixelPosition = React.useMemo(() => {
      const position = (value / 100) * mapWidth;
      return position;
    }, [value, mapWidth]);

    // 드래그 시작
    const handleDragStart = React.useCallback(
      (clientX: number) => {
        if (!enabled) return;
        setIsDragging(true);

        const handleDrag = (e: MouseEvent | TouchEvent) => {
          e.preventDefault();
          const currentX = "touches" in e ? e.touches[0].clientX : e.clientX;
          const rect = containerRef.current?.getBoundingClientRect();

          if (rect) {
            const relativeX = currentX - rect.left;
            // DOM 컨테이너 기준 좌표를 mapWidth 기준으로 변환
            const domWidth = rect.width;
            const scaleRatio = mapWidth / domWidth;
            const mapRelativeX = relativeX * scaleRatio;

            const percentage = Math.max(
              0,
              Math.min(100, (mapRelativeX / mapWidth) * 100),
            );

            onValueChange(Math.round(percentage));
          }
        };

        const handleDragEnd = () => {
          setIsDragging(false);
          document.removeEventListener("mousemove", handleDrag);
          document.removeEventListener("mouseup", handleDragEnd);
          document.removeEventListener("touchmove", handleDrag);
          document.removeEventListener("touchend", handleDragEnd);
        };

        document.addEventListener("mousemove", handleDrag);
        document.addEventListener("mouseup", handleDragEnd);
        document.addEventListener("touchmove", handleDrag, { passive: false });
        document.addEventListener("touchend", handleDragEnd);
      },
      [enabled, onValueChange],
    );

    // 마우스 이벤트
    const handleMouseDown = React.useCallback(
      (e: React.MouseEvent) => {
        e.preventDefault();
        handleDragStart(e.clientX);
      },
      [handleDragStart],
    );

    // 터치 이벤트
    const handleTouchStart = React.useCallback(
      (e: React.TouchEvent) => {
        e.preventDefault();
        handleDragStart(e.touches[0].clientX);
      },
      [handleDragStart],
    );

    // 표시되지 않으면 렌더링하지 않음
    if (!visible) return null;

    return (
      <div
        ref={containerRef}
        className={cn("absolute inset-0 pointer-events-none", className)}
        {...props}
      >
        <div
          ref={handleRef}
          className={cn(
            "absolute top-0 bottom-0 z-50 flex items-center justify-center pointer-events-auto",
            "w-2 cursor-col-resize select-none",
            // 드래그 중일 때는 위치 transition 비활성화, 다른 속성만 transition
            isDragging
              ? "scale-110"
              : "hover:scale-105 transition-transform duration-200 ease-out",
            enabled ? "cursor-col-resize" : "cursor-not-allowed",
          )}
          style={{
            left: `${pixelPosition}px`,
            transform: "translateX(-50%)",
          }}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
        >
          {/* 핸들 라인 */}
          <div
            className={cn(
              "w-1 h-full rounded-full shadow-lg transition-all duration-200",
              "bg-geon-primary border-2 border-white",
              isDragging
                ? "bg-geon-pressed shadow-xl"
                : "hover:bg-geon-hover hover:shadow-md",
              !enabled && "opacity-50",
            )}
          />

          {/* 핸들 그립 */}
          <div
            className={cn(
              "absolute w-6 h-8 rounded-md shadow-md transition-all duration-200",
              "bg-white border-2 border-geon-primary",
              "flex items-center justify-center",
              isDragging
                ? "border-geon-pressed shadow-lg scale-110"
                : "hover:border-geon-hover hover:shadow-md hover:scale-105",
              !enabled && "opacity-50",
            )}
          >
            {/* 그립 아이콘 */}
            <div className="flex flex-col gap-0.5">
              <div className="w-0.5 h-4 bg-geon-primary rounded-full" />
            </div>
          </div>

          {/* 값 표시 툴팁 */}
          {isDragging && (
            <div
              className={cn(
                "absolute -top-10 left-1/2 transform -translate-x-1/2",
                "px-2 py-1 bg-black text-white text-xs rounded",
                "pointer-events-none z-60",
              )}
            >
              {Math.round(value)}%
            </div>
          )}
        </div>
      </div>
    );
  },
);

SwipeDragHandle.displayName = "SwipeDragHandle";
