export type FieldType =
  | "text"
  | "select"
  | "number"
  | "date"
  | "dateRange"
  | "buttonGroup"
  | "spatialSearch";

export type FieldOption = {
  label: string;
  value: string;
};

export type SearchFieldSchema = {
  id: string;
  label: string;
  type: FieldType;
  placeholder?: string;
  options?: FieldOption[]; // for select
  defaultValue?: string | [string | null, string | null]; // string or [from,to] for dateRange
  // for buttonGroup
  buttons?: { id: string; label: string }[];
  // 전체 너비 사용 여부
  fullWidth?: boolean;
  // 정렬 순서
  order?: number;
  // 동적 로딩을 위한 API 엔드포인트
  apiEndpoint?: string;
};

export type ResultColumnSchema =
  | {
      id: string;
      label: string;
      accessor: string; // key in data row
      type?: "string";
    }
  | {
      id: string;
      label: string;
      type: "actions";
      actions: { id: string; label: string }[];
    };

export type ServiceSearchSchema = {
  serviceId: string;
  title: string;
  fields: SearchFieldSchema[];
  result: {
    columns: ResultColumnSchema[];
  };
};

export type ServiceSearchResponse<Row = Record<string, any>> = {
  schema: ServiceSearchSchema;
  data: Row[];
  totalCount?: number;
  hasMore?: boolean;
};

// 지도 연동을 위한 타입들
export type MapBounds = {
  north: number;
  south: number;
  east: number;
  west: number;
};

export type MapPoint = {
  lat: number;
  lng: number;
};

export type GeospatialData = {
  id: string;
  name: string;
  coordinates: MapPoint;
  bounds?: MapBounds;
  properties?: Record<string, any>;
};

export type MapSearchOptions = {
  bounds?: MapBounds;
  center?: MapPoint;
  radius?: number; // meters
  zoom?: number;
  includeGeometry?: boolean;
};

// 지도 상호작용 이벤트
export type MapInteractionEvent =
  | { type: "marker-click"; data: GeospatialData }
  | { type: "area-select"; bounds: MapBounds }
  | { type: "point-select"; point: MapPoint }
  | { type: "zoom-change"; zoom: number }
  | { type: "bounds-change"; bounds: MapBounds };

// 지도 연동 API 인터페이스
export interface MapIntegration {
  // 검색 결과를 지도에 표시
  showResults(results: GeospatialData[]): Promise<void>;

  // 특정 지점/영역을 강조 표시
  highlightLocation(location: GeospatialData): Promise<void>;

  // 지도에서 현재 보이는 영역 내 검색
  searchInBounds(bounds: MapBounds): Promise<GeospatialData[]>;

  // 지점 중심 반경 검색
  searchInRadius(center: MapPoint, radius: number): Promise<GeospatialData[]>;

  // 지도 상태 가져오기
  getCurrentBounds(): MapBounds;
  getCurrentCenter(): MapPoint;
  getCurrentZoom(): number;

  // 지도 이동/확대
  panTo(point: MapPoint): Promise<void>;
  fitBounds(bounds: MapBounds): Promise<void>;
  setZoom(zoom: number): Promise<void>;

  // 이벤트 리스너
  onInteraction(callback: (event: MapInteractionEvent) => void): () => void; // cleanup function 반환
}

// API 스키마 관련 타입들
export type ApiFieldUiType = "text" | "number" | "date" | "code" | "hidden";
export type ApiSearchMode = "eq" | "like" | "between" | "none";

export type ApiFieldSchema = {
  column: string;
  label: string;
  uiType: ApiFieldUiType;
  code?: string; // 코드 타입인 경우 코드그룹명
  search: { mode: ApiSearchMode };
};

export type ApiGeometryColumn = {
  columnName: string;
  geometryType: string;
  srid: number;
};

export type ApiSchemaResponse = {
  resource: string;
  resourceName: string;
  fields: ApiFieldSchema[];
  grouped: {
    text: string[];
    number: string[];
    date: string[];
    code: string[];
    hidden: string[];
  };
  searchField: string[];
  geometryColumns: ApiGeometryColumn[];
};

// 클라이언트 스키마 정의 타입들
export type ClientFieldSchema = {
  id: string;
  label: string;
  type: FieldType;
  order?: number;
  apiEndpoint?: string; // 동적 로딩용
  placeholder?: string;
  options?: FieldOption[];
  buttons?: { id: string; label: string }[];
  fullWidth?: boolean;
  defaultValue?: any;
};

export type ClientSearchSchema = {
  common: ClientFieldSchema[]; // 모든 메뉴에 공통으로 적용되는 필드들
  custom: ClientFieldSchema[]; // 메뉴별 커스텀 필드들
  defaultFields: string[]; // 기본으로 표시할 필드 ID 목록 (API + custom 필드 포함)
  availableFields?: string[]; // 추가 가능한 필드 ID 목록 (지정하지 않으면 모든 필드가 추가 가능)
};

export type MenuClientSchemas = {
  [menuId: string]: ClientSearchSchema;
};
