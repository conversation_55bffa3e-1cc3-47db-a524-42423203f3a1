"use client";

import { cn } from "@geon-ui/react/lib/utils";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarTrigger,
} from "@geon-ui/react/primitives/sidebar";
import React from "react";

import { useMapSidebar } from "../../../_contexts/sidebar";

export default function InnerSidebar({
  mapId,
  className,
  ...props
}: React.ComponentProps<typeof Sidebar> & {
  mapId: string;
}) {
  const { setInnerOpen, innerOpen } = useMapSidebar();

  // TODO: fetch config with mapId

  React.useEffect(() => {
    setInnerOpen(true);
  }, [setInnerOpen]);

  return (
    <Sidebar
      className={cn("left-(--sidebar-width-icon)", className)}
      variant="floating"
      {...props}
    >
      {/* 여기부터 */}
      <SidebarHeader>{mapId} Header</SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton>{mapId} Menu <PERSON></SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      {/* 여기까지 config 에 따라 render 수정 */}
      <SidebarTrigger
        className={cn(
          "absolute right-0 translate-x-[100%]",
          !innerOpen &&
            "translate-x-[calc(var(--sidebar-width-icon)+100%+0.5rem)] delay-100",
        )}
        variant="secondary"
      />
      <SidebarRail />
    </Sidebar>
  );
}
