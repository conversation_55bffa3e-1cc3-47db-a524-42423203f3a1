"use client";

import { BaseLayerInfo, useBaseLayer } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Checkbox } from "@geon-ui/react/primitives/checkbox";
import { Popover, PopoverTrigger } from "@geon-ui/react/primitives/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@geon-ui/react/primitives/select";
import { MapIcon } from "lucide-react";
import * as React from "react";

import {
  ToolbarContent,
  ToolbarItem,
  ToolbarTrigger,
} from "./base/toolbar-item";

// Props for compound components
export interface ToolbarBasemapProps
  extends React.HTMLAttributes<HTMLDivElement> {
  /** 선택된 배경지도 변경 콜백 */
  onBasemapChange?: (basemap: BaseLayerInfo) => void;
  /** 하이브리드 레이어 표시 여부 */
  showHybrid?: boolean;
}

export interface ToolbarBasemapTriggerProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 툴팁 텍스트 */
  tooltip?: string;
  /** 버튼 크기 */
  size?: "sm" | "lg" | "default" | "icon";
}

export interface ToolbarBasemapContentProps
  extends React.HTMLAttributes<HTMLDivElement> {}

// Basemap Context 생성
interface BasemapContextValue {
  onBasemapChange?: (basemap: BaseLayerInfo) => void;
  showHybrid: boolean;
  currentBaseLayerId?: string;
  categories: string[];
  overlayCategories: string[];
  getBaseLayersByCategory: (category: string) => BaseLayerInfo[];
  getOverlayLayersByCategory: (category: string) => BaseLayerInfo[];
  switchBaseLayer: (layerId: string) => Promise<void>;
  toggleOverlayLayer: (layerId: string) => Promise<void>;
  isOverlayLayerActive: (layerId: string) => boolean;
}

const BasemapContext = React.createContext<BasemapContextValue | null>(null);

export const useBasemapContext = () => {
  const context = React.useContext(BasemapContext);
  if (!context) {
    throw new Error(
      "ToolbarBasemap components must be used within ToolbarBasemap",
    );
  }
  return context;
};

// Main ToolbarBasemap Container
export const ToolbarBasemap = React.forwardRef<
  HTMLDivElement,
  ToolbarBasemapProps
>(
  (
    { onBasemapChange, showHybrid = true, className, children, ...props },
    ref,
  ) => {
    const {
      currentBaseLayerId,
      categories,
      overlayCategories,
      getBaseLayersByCategory,
      getOverlayLayersByCategory,
      switchBaseLayer,
      toggleOverlayLayer,
      isOverlayLayerActive,
    } = useBaseLayer();

    const contextValue = React.useMemo(
      () => ({
        onBasemapChange,
        showHybrid,
        currentBaseLayerId,
        categories,
        overlayCategories,
        getBaseLayersByCategory,
        getOverlayLayersByCategory,
        switchBaseLayer,
        toggleOverlayLayer,
        isOverlayLayerActive,
      }),
      [
        onBasemapChange,
        showHybrid,
        currentBaseLayerId,
        categories,
        overlayCategories,
        getBaseLayersByCategory,
        getOverlayLayersByCategory,
        switchBaseLayer,
        toggleOverlayLayer,
        isOverlayLayerActive,
      ],
    );

    return (
      <BasemapContext.Provider value={contextValue}>
        <ToolbarItem ref={ref} className={className} {...props}>
          <Popover>
            <PopoverTrigger asChild>
              {React.Children.toArray(children).find(
                (child) =>
                  React.isValidElement(child) &&
                  child.type === ToolbarBasemapTrigger,
              )}
            </PopoverTrigger>
            {React.Children.toArray(children).find(
              (child) =>
                React.isValidElement(child) &&
                child.type === ToolbarBasemapContent,
            )}
          </Popover>
        </ToolbarItem>
      </BasemapContext.Provider>
    );
  },
);

ToolbarBasemap.displayName = "ToolbarBasemap";

// ToolbarBasemapTrigger Component
export const ToolbarBasemapTrigger = React.forwardRef<
  HTMLButtonElement,
  ToolbarBasemapTriggerProps
>(
  (
    { tooltip = "배경지도", size = "default", className, children, ...props },
    ref,
  ) => {
    return (
      <ToolbarTrigger
        ref={ref}
        tooltip={tooltip}
        size={size}
        className={className}
        {...props}
      >
        {children || <MapIcon className="h-4 w-4" />}
      </ToolbarTrigger>
    );
  },
);

ToolbarBasemapTrigger.displayName = "ToolbarBasemapTrigger";

// ToolbarBasemapContent Component
export const ToolbarBasemapContent = React.forwardRef<
  HTMLDivElement,
  ToolbarBasemapContentProps
>(({ className, children, ...props }, ref) => {
  const {
    categories,
    overlayCategories,
    getBaseLayersByCategory,
    getOverlayLayersByCategory,
    switchBaseLayer,
    toggleOverlayLayer,
    isOverlayLayerActive,
    currentBaseLayerId,
    onBasemapChange,
    showHybrid,
  } = useBasemapContext();

  const [selectedCategory, setSelectedCategory] = React.useState<string>("");
  const [hybridEnabled, setHybridEnabled] = React.useState<boolean>(showHybrid);

  // 첫 번째 카테고리를 기본 선택
  React.useEffect(() => {
    if (categories.length > 0 && !selectedCategory) {
      setSelectedCategory(categories[0]);
    }
  }, [categories, selectedCategory]);

  // 현재 선택된 카테고리의 레이어들
  const currentBaseItems = getBaseLayersByCategory(selectedCategory);
  const currentOverlayItems = getOverlayLayersByCategory(selectedCategory);

  // 모든 카테고리
  const allCategories = [...new Set([...categories, ...overlayCategories])];

  // 배경지도 선택 핸들러
  const handleBasemapSelect = React.useCallback(
    async (basemap: BaseLayerInfo) => {
      await switchBaseLayer(basemap.id);
      onBasemapChange?.(basemap);
    },
    [switchBaseLayer, onBasemapChange],
  );

  // 하이브리드 토글 핸들러
  const handleHybridToggle = React.useCallback(
    async (checked: boolean) => {
      setHybridEnabled(checked);
      for (const layer of currentOverlayItems) {
        if (layer.layerType === "hybrid") {
          const isActive = isOverlayLayerActive(layer.id);
          if (checked && !isActive) {
            await toggleOverlayLayer(layer.id);
          } else if (!checked && isActive) {
            await toggleOverlayLayer(layer.id);
          }
        }
      }
    },
    [currentOverlayItems, isOverlayLayerActive, toggleOverlayLayer],
  );

  return (
    <ToolbarContent
      ref={ref}
      align="center"
      sideOffset={16}
      className={cn("w-80 flex flex-col gap-4 p-4", className)}
      {...props}
    >
      {/* 카테고리 선택 및 하이브리드 설정 */}
      <div className="flex items-center gap-3">
        <div className="flex-1">
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger>
              <SelectValue placeholder="카테고리 선택" />
            </SelectTrigger>
            <SelectContent>
              {allCategories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category || "기타"}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 하이브리드 체크박스 */}
        {showHybrid && currentOverlayItems.length > 0 && (
          <div className="flex items-center gap-2">
            <Checkbox
              checked={hybridEnabled}
              onCheckedChange={(checked) => handleHybridToggle(!!checked)}
              id="hybrid-toggle"
            />
            <label htmlFor="hybrid-toggle" className="text-sm font-medium">
              하이브리드
            </label>
          </div>
        )}
      </div>

      {/* 배경지도 목록 */}
      <div className="space-y-2">
        <div className="text-sm font-medium">배경지도</div>
        <div className="grid gap-2 max-h-60 overflow-y-auto">
          {currentBaseItems.map((layer) => (
            <button
              key={layer.id}
              onClick={() => handleBasemapSelect(layer)}
              className={cn(
                "flex items-center gap-3 p-2 rounded-md text-left hover:bg-muted transition-colors",
                currentBaseLayerId === layer.id && "bg-muted",
              )}
            >
              {/* 썸네일 */}
              <div className="flex h-10 w-10 shrink-0 items-center justify-center overflow-hidden rounded bg-muted">
                {layer.thumbnail ? (
                  <img
                    src={layer.thumbnail}
                    alt={layer.name}
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <MapIcon className="h-4 w-4 text-muted-foreground" />
                )}
              </div>

              {/* 정보 */}
              <div className="flex-1 min-w-0">
                <div className="font-medium text-sm truncate">{layer.name}</div>
                <div className="text-xs text-muted-foreground truncate">
                  {layer.category}
                </div>
              </div>

              {/* 선택 표시 */}
              {currentBaseLayerId === layer.id && (
                <div className="h-2 w-2 shrink-0 rounded-full bg-primary"></div>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* 오버레이 레이어 목록 */}
      {hybridEnabled && currentOverlayItems.length > 0 && (
        <div className="space-y-2">
          <div className="text-sm font-medium">오버레이</div>
          <div className="space-y-2">
            {currentOverlayItems.map((layer) => {
              const isActive = isOverlayLayerActive(layer.id);
              return (
                <div
                  key={layer.id}
                  className="flex items-center gap-3 p-2 rounded-md hover:bg-muted"
                >
                  <Checkbox
                    checked={isActive}
                    onCheckedChange={() => toggleOverlayLayer(layer.id)}
                    id={`overlay-${layer.id}`}
                  />
                  <label
                    htmlFor={`overlay-${layer.id}`}
                    className="text-sm flex-1 cursor-pointer"
                  >
                    {layer.name}
                  </label>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {children}
    </ToolbarContent>
  );
});

ToolbarBasemapContent.displayName = "ToolbarBasemapContent";
