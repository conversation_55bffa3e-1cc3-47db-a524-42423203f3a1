"use client";

import { UseLayerTOCWidget } from "@geon-map/react-ui/components";

interface SmartTOCWidgetProps {
  className?: string;
  /** 필터링할 레이어 타입들 (기본적으로 draw, measure, clear 제외) */
  excludeLayerTypes?: string[];
  /** 추가 필터링 조건 */
  layerFilter?: (layer: any) => boolean;
  /** 헤더 표시 여부 */
  showHeader?: boolean;
  /** 그룹 opacity 활성화 여부 */
  isGroupOpacityEnabled?: boolean;
  /** 레이어 opacity 활성화 여부 */
  isLayerOpacityEnabled?: boolean;
}

/**
 * UseLayerTOCWidget을 래핑하는 스마트 TOC Widget
 *
 * 특징:
 * - useLayer 상태만 사용하여 이중 상태 관리 문제 해결
 * - Layer 컴포넌트와 완벽한 동기화
 * - useTOC 제거로 성능 향상
 */
export function SmartTOCWidget({
  className = "absolute left-5 top-20 flex max-h-[700px] min-h-[300px] w-[400px] flex-col",
  excludeLayerTypes = ["draw", "measure", "clear"],
  layerFilter,
  showHeader = true,
  isGroupOpacityEnabled = false,
  isLayerOpacityEnabled = false,
}: SmartTOCWidgetProps) {
  return (
    <UseLayerTOCWidget
      className={className}
      excludeLayerTypes={excludeLayerTypes}
      layerFilter={layerFilter}
      showHeader={showHeader}
      isGroupOpacityEnabled={isGroupOpacityEnabled}
      isLayerOpacityEnabled={isLayerOpacityEnabled}
    />
  );
}
