import { useBaseLayer, useEvent, useStores } from "@geon-map/react-odf";
import { useMap } from "@geon-map/react-odf";
import { useCallback, useEffect, useRef, useState } from "react";

interface UseSwipeOptions {
  value: number;
  onValueChange: (value: number) => void;
  leftLayerId: string;
  rightLayerId: string;
  enabled: boolean;
  onEnabledChange: (enabled: boolean) => void;
  onLeftLayerChange?: (layerId: string) => void;
  onRightLayerChange?: (layerId: string) => void;
}

interface UseSwipeReturn {
  // 상태
  mapSize: [number, number] | null;

  // 레이어 변경 핸들러 (내부 처리된)
  handleLeftLayerChange: (layerId: string) => void;
  handleRightLayerChange: (layerId: string) => void;
  handleEnabledToggle: () => void;
}

interface RenderEvent {
  context: CanvasRenderingContext2D;
  frameState?: any;
  inversePixelTransform?: any;
  pixelRatio?: number;
  postRender?: boolean;
  size?: [number, number];
  viewHints?: any;
  viewState?: any;
  [key: string]: any;
}

export function useSwipe(options: UseSwipeOptions): UseSwipeReturn {
  const {
    value,
    onValueChange,
    leftLayerId,
    rightLayerId,
    enabled,
    onEnabledChange,
    onLeftLayerChange,
    onRightLayerChange,
  } = options;

  const { map } = useMap();
  const { registerListener } = useEvent();
  const { getBaseLayerById } = useBaseLayer();
  const { layerStore } = useStores();

  // 🎯 SwipeWidget이 생성한 레이어 추적 (다른 배경지도와 구분)
  const swipeCreatedLayersRef = useRef<Set<string>>(new Set());

  // 🎯 SwipeWidget 활성화 전 기존 배경지도 상태 추적
  const previousBaseLayerRef = useRef<string | null>(null);

  // 맵 크기 상태
  const [mapSize, setMapSize] = useState<[number, number] | null>(null);

  // 최신 값 참조를 위한 ref (의존성 배열 최적화)
  const valueRef = useRef(value);
  valueRef.current = value;

  // 🎯 내부 래퍼 함수들 (layerStore 직접 접근 캡슐화)
  const getLayerById = useCallback(
    (layerId: string) => {
      return layerStore.getState().getLayerById(layerId);
    },
    [layerStore],
  );

  const addLayerToStore = useCallback(
    (layer: any) => {
      layerStore.getState().addLayer(layer);
    },
    [layerStore],
  );

  const getLayerFactory = useCallback(() => {
    return layerStore.getState().layerFactory;
  }, [layerStore]);

  const restorePreviousBaseLayer = useCallback(() => {
    if (previousBaseLayerRef.current) {
      const previousLayerId = previousBaseLayerRef.current;
      const previousLayer = layerStore.getState().getLayerById(previousLayerId);

      if (previousLayer?.odfLayer) {
        previousLayer.odfLayer.setVisible(true);
        previousLayer.visible = true;
        layerStore.getState().setCurrentBaseLayer(previousLayerId);
      }

      previousBaseLayerRef.current = null;
    }
  }, [layerStore]);

  const leftBaseLayer = leftLayerId ? getBaseLayerById(leftLayerId) : null;
  const rightBaseLayer = rightLayerId ? getBaseLayerById(rightLayerId) : null;

  const isValidSetup = leftBaseLayer && rightBaseLayer && enabled && map;

  const updateMapSize = useCallback(() => {
    if (!map) return;
    const size = map.getSize();
    if (size) {
      setMapSize(size as [number, number]);
    }
  }, [map]);

  // 🎯 OpenLayers 렌더링 이벤트 핸들러
  const createPreRenderHandler = useCallback(
    (isLeft: boolean) => (event: RenderEvent) => {
      if (!map) return;

      const ctx = event.context;
      const mapSize = map.getSize();

      if (!mapSize) return;

      // WebGL context (scissor test 사용)
      if ((ctx as any).enable && (ctx as any).disable) {
        (ctx as any).enable((ctx as any).SCISSOR_TEST);

        const bottomLeft = (window as any).odf?.Render?.getRenderPixel
          ? (window as any).odf.Render.getRenderPixel(event, [0, mapSize[1]])
          : [0, mapSize[1]];
        const topRight = (window as any).odf?.Render?.getRenderPixel
          ? (window as any).odf.Render.getRenderPixel(event, [mapSize[0], 0])
          : [mapSize[0], 0];

        const renderWidth = topRight[0] - bottomLeft[0];
        const renderHeight = topRight[1] - bottomLeft[1];

        if (isLeft) {
          // 왼쪽 레이어: 0 ~ value% 영역
          const width = Math.round(renderWidth * (valueRef.current / 100));

          (ctx as any).scissor(0, 0, width, renderHeight);
        } else {
          // 오른쪽 레이어: value% ~ 100% 영역
          const leftOffset = Math.round(renderWidth * (valueRef.current / 100));
          const width = renderWidth - leftOffset;
          (ctx as any).scissor(leftOffset, 0, width, renderHeight);
        }
      } else {
        // Canvas 2D context (clip 사용)
        const canvasWidth = ctx.canvas.width;
        const canvasHeight = ctx.canvas.height;
        const clipWidth = canvasWidth * (valueRef.current / 100);

        ctx.save();
        ctx.beginPath();

        if (isLeft) {
          // 왼쪽 레이어 클리핑 영역
          ctx.rect(0, 0, clipWidth, canvasHeight);
        } else {
          // 오른쪽 레이어 클리핑 영역
          ctx.rect(clipWidth, 0, canvasWidth - clipWidth, canvasHeight);
        }

        ctx.clip();
      }
    },
    [map],
  );

  const createPostRenderHandler = useCallback(
    () => (event: RenderEvent) => {
      const ctx = event.context;

      // WebGL context 복원
      if ((ctx as any).disable) {
        (ctx as any).disable((ctx as any).SCISSOR_TEST);
      } else {
        // Canvas 2D context 복원
        ctx.restore();
      }
    },
    [],
  );

  // 🎯 배경지도 레이어 생성 보장
  const ensureBaseLayerExists = useCallback(
    async (baseLayerId: string, isLeft: boolean): Promise<any> => {
      const layerFactory = getLayerFactory();
      const baseLayerInfo = getBaseLayerById(baseLayerId);

      if (!layerFactory || !baseLayerInfo) {
        return null;
      }

      // 기존 레이어 확인
      const existingLayer = getLayerById(baseLayerId);

      if (existingLayer) {
        // 기존 레이어의 Z-Index 재설정
        if (existingLayer.odfLayer && existingLayer.odfLayer.setZIndex) {
          const zIndex = isLeft ? -2 : -1;
          existingLayer.odfLayer.setZIndex(zIndex);
        }
        return existingLayer;
      }

      try {
        const odfLayer = layerFactory.produce("api", baseLayerInfo.layerParams);
        odfLayer.setODFId(baseLayerId);

        const zIndex = isLeft ? -2 : -1;

        const newLayer = {
          id: baseLayerId,
          name: baseLayerInfo.name,
          type: "baselayer" as const,
          visible: false, // 초기에는 숨김 (스와이프에서 클리핑으로 제어)
          zIndex,
          odfLayer,
          isBaseLayer: true,
          baseLayerInfo,
          params: baseLayerInfo.layerParams,
        };

        addLayerToStore(newLayer);
        swipeCreatedLayersRef.current.add(baseLayerId);

        // ODF 레이어 맵에 추가 및 설정
        odfLayer.setMap(map);
        odfLayer.setZIndex(zIndex);
        odfLayer.setVisible(false); // 초기에는 숨김 상태

        return newLayer;
      } catch (error) {
        console.error(`Error creating base layer ${baseLayerId}:`, error);
        return null;
      }
    },
    [getBaseLayerById, getLayerById, getLayerFactory, addLayerToStore, map],
  );

  // 🎯 기존 배경지도 충돌 방지 로직
  const hideConflictingBaseLayers = useCallback(() => {
    const currentBaseLayerId = layerStore.getState().currentBaseLayerId;

    if (
      currentBaseLayerId &&
      currentBaseLayerId !== leftLayerId &&
      currentBaseLayerId !== rightLayerId
    ) {
      // 이전 상태 저장 (첫 번째 활성화 시에만)
      if (previousBaseLayerRef.current === null) {
        previousBaseLayerRef.current = currentBaseLayerId;
      }

      // 기존 배경지도 비활성화
      const existingLayer = layerStore
        .getState()
        .getLayerById(currentBaseLayerId);
      if (existingLayer?.odfLayer) {
        existingLayer.odfLayer.setVisible(false);
        existingLayer.visible = false;
      }
    }
  }, [layerStore, leftLayerId, rightLayerId]);

  // 맵 크기 업데이트
  useEffect(() => {
    if (!isValidSetup) return;

    updateMapSize();

    const cleanupResize = registerListener(
      map,
      "change:size" as any,
      updateMapSize,
      { listenerId: "swipe-widget-resize" },
    );

    return cleanupResize;
  }, [isValidSetup, map, registerListener, updateMapSize]);

  // enabled 상태 변경 처리
  useEffect(() => {
    if (!enabled) {
      // 스와이프 비활성화 시 SwipeWidget이 생성한 레이어들만 숨김 처리
      if (leftLayerId && swipeCreatedLayersRef.current.has(leftLayerId)) {
        const leftLayer = getLayerById(leftLayerId);
        if (leftLayer?.odfLayer) {
          leftLayer.odfLayer.setVisible(false);
        }
      }
      if (rightLayerId && swipeCreatedLayersRef.current.has(rightLayerId)) {
        const rightLayer = getLayerById(rightLayerId);
        if (rightLayer?.odfLayer) {
          rightLayer.odfLayer.setVisible(false);
        }
      }

      restorePreviousBaseLayer();

      if (map) {
        try {
          map.render();
        } catch (error) {
          console.warn("클리핑 정리 중 오류:", error);
        }
      }
    }
  }, [
    enabled,
    leftLayerId,
    rightLayerId,
    map,
    getLayerById,
    restorePreviousBaseLayer,
  ]);

  // 배경지도 레이어 생성 및 이벤트 등록 관리
  useEffect(() => {
    if (!isValidSetup || !leftLayerId || !rightLayerId) return;

    const eventCleanups: (() => void)[] = [];
    let leftLayer: any = null;
    let rightLayer: any = null;

    const initializeSwipe = async () => {
      try {
        hideConflictingBaseLayers();

        // 양쪽 배경지도 레이어 생성 보장
        [leftLayer, rightLayer] = await Promise.all([
          ensureBaseLayerExists(leftLayerId, true),
          ensureBaseLayerExists(rightLayerId, false),
        ]);

        if (!leftLayer || !rightLayer) {
          console.warn("❌ Failed to ensure base layers exist");
          return;
        }

        if (rightLayer.odfLayer) {
          const rightPreRenderCleanup = registerListener(
            rightLayer.odfLayer,
            "prerender" as any,
            createPreRenderHandler(false),
            { listenerId: `swipe-right-prerender-${rightLayerId}` },
          );

          const rightPostRenderCleanup = registerListener(
            rightLayer.odfLayer,
            "postrender" as any,
            createPostRenderHandler(),
            { listenerId: `swipe-right-postrender-${rightLayerId}` },
          );

          eventCleanups.push(rightPreRenderCleanup, rightPostRenderCleanup);
        }

        // 레이어들을 보이도록 설정 (클리핑으로 제어)
        if (leftLayer.odfLayer) {
          leftLayer.odfLayer.setVisible(true);
        }
        if (rightLayer.odfLayer) {
          rightLayer.odfLayer.setVisible(true);
        }

        if (map) {
          map.render();
        }
      } catch (error) {
        console.error("❌ Error initializing swipe:", error);
      }
    };

    initializeSwipe();

    // cleanup 함수
    return () => {
      eventCleanups.forEach((cleanup) => cleanup());

      if (
        leftLayer?.odfLayer &&
        swipeCreatedLayersRef.current.has(leftLayerId)
      ) {
        leftLayer.odfLayer.setVisible(false);
      }
      if (
        rightLayer?.odfLayer &&
        swipeCreatedLayersRef.current.has(rightLayerId)
      ) {
        rightLayer.odfLayer.setVisible(false);
      }

      restorePreviousBaseLayer();

      if (map) {
        map.render();
      }
    };
  }, [
    isValidSetup,
    leftLayerId,
    rightLayerId,
    ensureBaseLayerExists,
    registerListener,
    hideConflictingBaseLayers,
    restorePreviousBaseLayer,
    map,
  ]);

  // 스와이프 값 변경 시 재렌더링 트리거
  useEffect(() => {
    if (enabled && map) {
      map.render();
    }
  }, [value, enabled, map]);

  // 레이어 변경 핸들러들
  const handleLeftLayerChange = useCallback(
    (layerId: string) => {
      onLeftLayerChange?.(layerId);
    },
    [onLeftLayerChange],
  );

  const handleRightLayerChange = useCallback(
    (layerId: string) => {
      onRightLayerChange?.(layerId);
    },
    [onRightLayerChange],
  );

  const handleEnabledToggle = useCallback(() => {
    onEnabledChange(!enabled);
  }, [enabled, onEnabledChange]);

  return {
    mapSize,
    handleLeftLayerChange,
    handleRightLayerChange,
    handleEnabledToggle,
  };
}
