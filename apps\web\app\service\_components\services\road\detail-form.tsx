"use client";

import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import { Calendar } from "@geon-ui/react/primitives/calendar";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@geon-ui/react/primitives/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@geon-ui/react/primitives/form";
import { Input } from "@geon-ui/react/primitives/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@geon-ui/react/primitives/popover";
import { ScrollArea } from "@geon-ui/react/primitives/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@geon-ui/react/primitives/select";
import { Separator } from "@geon-ui/react/primitives/separator";
import { Textarea } from "@geon-ui/react/primitives/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { ko } from "date-fns/locale";
import {
  Building,
  Calendar as CalendarIcon,
  Clock,
  MapPin,
  MessageSquare,
} from "lucide-react";
import { forwardRef, useCallback, useImperativeHandle } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import type { FacilityDetailFormProps } from "../../../_types/facility-detail";

/**
 * 폼 검증 스키마
 */
const facilityFormSchema = z
  .object({
    registryType: z.string().min(1, "대장구분을 선택해주세요"),
    featureCode: z.string().min(1, "지형지물부호를 입력해주세요"),
    managementNumber: z.string().min(1, "관리번호를 입력해주세요"),
    adminDistrict: z.string().min(1, "행정구역을 입력해주세요"),
    mapSheetNumber: z.string().min(1, "도엽번호를 입력해주세요"),
    managementAgency: z.string().min(1, "관리기관을 입력해주세요"),
    installStartDate: z.date({
      error: "설치 시작일을 선택해주세요",
    }),
    installEndDate: z.date({
      error: "설치 종료일을 선택해주세요",
    }),
    remarks: z.string().optional(),
    areaInfo: z.string().min(1, "영역 정보를 입력해주세요"),
  })
  .refine((data) => data.installStartDate <= data.installEndDate, {
    message: "설치 종료일은 시작일보다 늦어야 합니다",
    path: ["installEndDate"],
  });

type FacilityFormData = z.infer<typeof facilityFormSchema>;

/**
 * 폼 참조 타입
 */
export interface RoadDetailFormRef {
  submit: () => void;
}

/**
 * 대장구분 옵션
 */
const REGISTRY_TYPE_OPTIONS = [
  { value: "general", label: "일반 대장" },
  { value: "special", label: "특수 대장" },
  { value: "temporary", label: "임시 대장" },
];

/**
 * 도로 등록 폼 컴포넌트
 */
export const RoadDetailForm = forwardRef<
  RoadDetailFormRef,
  FacilityDetailFormProps
>(({ initialData, facilityType, onSubmit, onCancel, loading = false }, ref) => {
  const form = useForm<FacilityFormData>({
    resolver: zodResolver(facilityFormSchema),
    defaultValues: {
      registryType: initialData?.registryType || "",
      featureCode: initialData?.featureCode || "",
      managementNumber: initialData?.managementNumber || "",
      adminDistrict: initialData?.adminDistrict || "",
      mapSheetNumber: initialData?.mapSheetNumber || "",
      managementAgency: initialData?.managementAgency || "",
      installStartDate: initialData?.installStartDate
        ? new Date(initialData.installStartDate)
        : undefined,
      installEndDate: initialData?.installEndDate
        ? new Date(initialData.installEndDate)
        : undefined,
      remarks: initialData?.remarks || "",
      areaInfo: initialData?.areaInfo || "",
    },
  });

  /**
   * 폼 제출 처리
   */
  const handleSubmit = useCallback(
    (data: FacilityFormData) => {
      onSubmit({
        ...data,
        remarks: data.remarks || "", // undefined를 빈 문자열로 변환
        facilityType,
        facilityId: initialData?.facilityId,
        facilityName: initialData?.facilityName,
      });
    },
    [
      onSubmit,
      facilityType,
      initialData?.facilityId,
      initialData?.facilityName,
    ],
  );

  // 외부에서 폼 제출을 트리거할 수 있도록 ref 노출
  useImperativeHandle(
    ref,
    () => ({
      submit: () => {
        form.handleSubmit(handleSubmit)();
      },
    }),
    [form.handleSubmit, handleSubmit],
  );

  return (
    <ScrollArea className="h-[600px]">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
          className="space-y-6 p-6"
        >
          {/* 기본 정보 섹션 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                기본 정보
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* 대장구분 */}
                <FormField
                  control={form.control}
                  name="registryType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>대장구분 *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="대장구분을 선택하세요" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {REGISTRY_TYPE_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 지형지물부호 */}
                <FormField
                  control={form.control}
                  name="featureCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>지형지물부호 *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="지형지물부호를 입력하세요"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Separator />

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* 관리번호 */}
                <FormField
                  control={form.control}
                  name="managementNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>관리번호 *</FormLabel>
                      <FormControl>
                        <Input placeholder="관리번호를 입력하세요" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 행정구역 */}
                <FormField
                  control={form.control}
                  name="adminDistrict"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>행정구역 *</FormLabel>
                      <FormControl>
                        <Input placeholder="행정구역을 입력하세요" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* 도엽번호 */}
                <FormField
                  control={form.control}
                  name="mapSheetNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>도엽번호 *</FormLabel>
                      <FormControl>
                        <Input placeholder="도엽번호를 입력하세요" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 관리기관 */}
                <FormField
                  control={form.control}
                  name="managementAgency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>관리기관 *</FormLabel>
                      <FormControl>
                        <Input placeholder="관리기관을 입력하세요" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* 설치 정보 섹션 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                설치 정보
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* 설치 시작일 */}
                <FormField
                  control={form.control}
                  name="installStartDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>설치 시작일 *</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground",
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP", { locale: ko })
                              ) : (
                                <span>설치 시작일을 선택하세요</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date > new Date() || date < new Date("1900-01-01")
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 설치 종료일 */}
                <FormField
                  control={form.control}
                  name="installEndDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>설치 종료일 *</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground",
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP", { locale: ko })
                              ) : (
                                <span>설치 종료일을 선택하세요</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date < new Date("1900-01-01")}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* 추가 정보 섹션 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                추가 정보
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 영역정보등록 */}
              <FormField
                control={form.control}
                name="areaInfo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      영역정보등록 *
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="영역 정보를 입력하세요..."
                        className="resize-none"
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 비고 */}
              <FormField
                control={form.control}
                name="remarks"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>비고</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="추가 설명이나 특이사항을 입력하세요..."
                        className="resize-none"
                        rows={4}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        </form>
      </Form>
    </ScrollArea>
  );
});

RoadDetailForm.displayName = "RoadDetailForm";
