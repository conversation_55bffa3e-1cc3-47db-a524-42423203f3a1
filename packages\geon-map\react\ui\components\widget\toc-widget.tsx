import { Legend, useLayer } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import { ScrollArea } from "@geon-ui/react/primitives/scroll-area";
import { Slider } from "@geon-ui/react/primitives/slider";
import { ChevronDown, ChevronRight, Eye, EyeOff, Layers } from "lucide-react";
import React, { useCallback, useMemo, useState } from "react";

import { useTOC } from "../../hooks";
import {
  GroupOpacitySliderProps,
  TOCNode,
  TOCOptions,
  TOCRootProps,
  TOCTreeGroupContextProps,
  TOCTreeItemContextProps,
  TOCWidgetProps,
} from "../../types";
import { LayerOpacitySlider } from "../layer-opacity-slider";

// Context
interface TOCContextValue {
  tocNodes: TOCNode[];
  handleToggleVisibility: (id: string, visible: boolean) => void;
  handleToggleExpanded: (id: string, expanded: boolean) => void;
  expandedGroups: Set<string>;
  zoomToLayer: (id: string, duration?: number) => boolean;
  isGroupOpacityEnabled?: boolean;
  isLayerOpacityEnabled?: boolean;
  getGroupOpacity: (id: string) => number;
  updateGroupOpacityState: (id: string, opacity: number) => void;
  getLayerIdsInGroup: (groupId: string) => string[];
  updateLayerOpacityState: (id: string, opacity: number) => void;
  getLayerOpacity: (id: string) => number;
}

const TOCContext = React.createContext<TOCContextValue | null>(null);

const useTOCContext = () => {
  const context = React.useContext(TOCContext);
  if (!context) {
    throw new Error("useTOCContext must be used within TOC component");
  }
  return context;
};

export const TOC = React.forwardRef<HTMLDivElement, TOCRootProps>(
  (
    {
      className,
      children,
      data,
      onLayerVisibilityChange,
      onGroupExpandedChange,
      isGroupOpacityEnabled = false,
      isLayerOpacityEnabled = false,
      ...props
    },
    ref,
  ) => {
    const tocOptions: TOCOptions = {
      data,
      onLayerVisibilityChange,
      onGroupExpandedChange,
      isGroupOpacityEnabled,
      isLayerOpacityEnabled,
    };

    const {
      tocNodes,
      expandedGroups,
      handleToggleVisibility,
      handleToggleExpanded,
      zoomToLayer,
      getGroupOpacity,
      getLayerOpacity,
      updateGroupOpacityState,
      updateLayerOpacityState,
      getLayerIdsInGroup,
    } = useTOC(tocOptions);

    const contextValue: TOCContextValue = {
      tocNodes: tocNodes,
      handleToggleVisibility: handleToggleVisibility,
      handleToggleExpanded: handleToggleExpanded,
      expandedGroups: expandedGroups,
      zoomToLayer: zoomToLayer,
      isGroupOpacityEnabled: isGroupOpacityEnabled,
      isLayerOpacityEnabled: isLayerOpacityEnabled,
      getGroupOpacity,
      getLayerOpacity,
      updateGroupOpacityState,
      updateLayerOpacityState,
      getLayerIdsInGroup,
    };

    return (
      <TOCContext.Provider value={contextValue}>
        <div
          ref={ref}
          className={cn("right-4 top-35 flex flex-col gap-2 h-full", className)}
          {...props}
        >
          {children}
        </div>
      </TOCContext.Provider>
    );
  },
);
TOC.displayName = "TOC";

// Main Container
export const TOCContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        "bg-white border rounded-lg shadow-lg p-4 flex flex-col h-full",
        className,
      )}
      {...props}
    >
      {children}
    </div>
  );
});
TOCContent.displayName = "TOCContent";

// Header
export const TOCHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn("flex items-center justify-between mb-4", className)}
      {...props}
    >
      {children}
    </div>
  );
});
TOCHeader.displayName = "TOCHeader";

// Title
export const TOCTitle = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, children, ...props }, ref) => {
  return (
    <h3 ref={ref} className={cn("text-lg font-semibold", className)} {...props}>
      {children || "레이어 목록"}
    </h3>
  );
});
TOCTitle.displayName = "TOCTitle";

// Toolbar
export const TOCToolbar = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn("flex items-center gap-1", className)}
      {...props}
    >
      {children}
    </div>
  );
});
TOCToolbar.displayName = "TOCToolbar";

// Body (Scroll Area)
export const TOCBody = React.forwardRef<
  React.ComponentRef<typeof ScrollArea>,
  React.ComponentPropsWithoutRef<typeof ScrollArea>
>(({ className, children, ...props }, ref) => {
  return (
    <ScrollArea
      ref={ref}
      className={cn("flex-1 min-h-0", className)}
      {...props}
    >
      {children}
    </ScrollArea>
  );
});
TOCBody.displayName = "TOCBody";

export const TOCTreeGroup = React.forwardRef<
  HTMLDivElement,
  TOCTreeGroupContextProps
>(({ className, data, level = 0, expanded, ...props }, ref) => {
  const {
    handleToggleVisibility,
    handleToggleExpanded,
    expandedGroups,
    isGroupOpacityEnabled,
    getLayerIdsInGroup,
    getGroupOpacity,
    updateGroupOpacityState,
  } = useTOCContext();

  // 우선순위: 1. props의 expanded, 2. data.expanded, 3. expandedGroups에서 확인
  const isExpanded =
    expanded !== undefined
      ? expanded
      : data.type === "group" && data.expanded !== undefined
        ? data.expanded
        : expandedGroups.has(data.id);

  const handleToggleExpandedClick = () => {
    handleToggleExpanded(data.id, !isExpanded);
  };

  const handleToggleVisibilityClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    handleToggleVisibility(data.id, !data.visible);
  };

  const indentStyle = { paddingLeft: `${level * 10}px` };

  return (
    <div ref={ref} className={cn("", className)} {...props}>
      {/* 그룹 헤더 */}
      <div className="flex items-center gap-2 p-2 rounded" style={indentStyle}>
        <Button
          variant="ghost"
          size="sm"
          className="h-4 w-4 p-0"
          onClick={(e) => {
            e.stopPropagation();
            handleToggleExpandedClick();
          }}
        >
          {isExpanded ? (
            <ChevronDown className="h-3 w-3" />
          ) : (
            <ChevronRight className="h-3 w-3" />
          )}
        </Button>

        <Button
          onClick={handleToggleVisibilityClick}
          className="h-6 w-6 p-0 bg-transparent hover:bg-transparent border-none shadow-none cursor-pointer"
        >
          {data.visible ? (
            <Eye className="h-4 w-4 text-green-600" />
          ) : (
            <EyeOff className="h-4 w-4 text-gray-400" />
          )}
        </Button>

        <Layers className="h-4 w-4 text-blue-600" />

        <span className="flex-1 text-sm font-medium">{data.name}</span>

        {isGroupOpacityEnabled && getLayerIdsInGroup(data.id).length > 0 ? (
          <LayerOpacitySlider
            layerIds={getLayerIdsInGroup(data.id)}
            value={getGroupOpacity(data.id)}
            onValueChange={(value) => {
              updateGroupOpacityState(data.id, value);
            }}
          />
        ) : null}
      </div>

      {/* 자식 레이어들 */}
      {isExpanded && data.type === "group" && data.children && (
        <div className="ml-2">
          {data.children.map((child) =>
            child.type === "group" ? (
              <TOCTreeGroup key={child.id} data={child} level={level + 1} />
            ) : (
              <TOCTreeItem key={child.id} data={child} level={level + 1} />
            ),
          )}
        </div>
      )}
    </div>
  );
});
TOCTreeGroup.displayName = "TOCTreeGroup";

export const TOCTreeItem = React.forwardRef<
  HTMLDivElement,
  TOCTreeItemContextProps
>(({ className, data, level = 0, ...props }, ref) => {
  const {
    handleToggleVisibility,
    zoomToLayer,
    isLayerOpacityEnabled,
    updateLayerOpacityState,
    getLayerOpacity,
  } = useTOCContext();

  const handleToggleVisibilityClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    handleToggleVisibility(data.id, !data.visible);
  };
  const handleItemClick = () => {
    zoomToLayer(data.id, 1000);
  };

  const indentStyle = { paddingLeft: `${level * 10}px` };

  return (
    <div
      ref={ref}
      className={cn(
        "flex items-center gap-2 p-2 rounded cursor-pointer",
        className,
      )}
      style={indentStyle}
      onClick={handleItemClick}
      {...props}
    >
      {/* 들여쓰기를 위한 빈 공간 */}
      <div className="w-4" />

      <Button
        onClick={handleToggleVisibilityClick}
        className="h-6 w-6 p-0 bg-transparent hover:bg-transparent border-none shadow-none cursor-pointer"
      >
        {data.visible ? (
          <Eye className="h-4 w-4 text-green-600" />
        ) : (
          <EyeOff className="h-4 w-4 text-gray-400" />
        )}
      </Button>
      <Legend layerId={data.layerId} />

      <span className="flex-1 text-sm">{data.name}</span>

      {isLayerOpacityEnabled && data.layerId ? (
        <LayerOpacitySlider
          layerIds={[data.layerId]}
          value={getLayerOpacity(data.id)}
          onValueChange={(value) => {
            if (data.layerId) {
              updateLayerOpacityState(data.id, value);
            }
          }}
        />
      ) : null}
    </div>
  );
});
TOCTreeItem.displayName = "TOCTreeItem";

// Tree
export const TOCTree = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const { tocNodes } = useTOCContext();

  return (
    <div ref={ref} className={cn("space-y-1", className)} {...props}>
      {tocNodes.map((layer) =>
        layer.type === "group" ? (
          <TOCTreeGroup key={layer.id} data={layer} />
        ) : (
          <TOCTreeItem key={layer.id} data={layer} />
        ),
      )}
    </div>
  );
});
TOCTree.displayName = "TOCTree";

export const GroupOpacitySlider = React.forwardRef<
  HTMLDivElement,
  GroupOpacitySliderProps
>(({ groupId, className, ...props }, ref) => {
  const { updateGroupOpacityState, getGroupOpacity } = useTOCContext();
  const handleValueChange = useCallback(
    (value: number[]) => {
      updateGroupOpacityState(groupId, value[0] ?? 1);
    },
    [groupId, updateGroupOpacityState],
  );
  return (
    <div
      ref={ref}
      className={cn("flex items-center gap-2 w-20", className)}
      {...props}
    >
      <Slider
        className="w-16 h-2"
        min={0}
        max={1}
        step={0.1}
        value={[getGroupOpacity(groupId)]}
        onValueChange={handleValueChange}
      ></Slider>
    </div>
  );
});
GroupOpacitySlider.displayName = "GroupOpacitySlider";

export const TOCWidget = React.forwardRef<HTMLDivElement, TOCWidgetProps>(
  (
    {
      data,
      onLayerVisibilityChange,
      onGroupExpandedChange,
      isGroupOpacityEnabled = false,
      isLayerOpacityEnabled = false,
      className,
      showHeader = true,
      ...props
    },
    ref,
  ) => {
    const handleLayerVisibilityChange = useCallback(
      (id: string, visible: boolean) => {
        console.log(`Layer ${id} visibility changed to:`, visible);
        onLayerVisibilityChange?.(id, visible);
      },
      [onLayerVisibilityChange],
    );

    const handleGroupExpandedChange = useCallback(
      (id: string, expanded: boolean) => {
        console.log(`Group ${id} expanded state changed to:`, expanded);
        onGroupExpandedChange?.(id, expanded);
      },
      [onGroupExpandedChange],
    );

    return (
      <TOC
        ref={ref}
        data={data}
        onLayerVisibilityChange={handleLayerVisibilityChange}
        onGroupExpandedChange={handleGroupExpandedChange}
        isGroupOpacityEnabled={isGroupOpacityEnabled}
        isLayerOpacityEnabled={isLayerOpacityEnabled}
        className={className}
        {...props}
      >
        <TOCContent>
          {showHeader ? (
            <TOCHeader>
              <TOCTitle />
              <TOCToolbar>
                <Button variant="ghost" size="sm">
                  <Layers className="h-4 w-4" />
                </Button>
              </TOCToolbar>
            </TOCHeader>
          ) : (
            <></>
          )}
          <TOCBody>
            <TOCTree />
          </TOCBody>
        </TOCContent>
      </TOC>
    );
  },
);
TOCWidget.displayName = "TOCWidget";

// ===== 새로운 UseLayerTOCWidget: useLayer 상태만 사용 =====

interface UseLayerTOCWidgetProps {
  className?: string;
  showHeader?: boolean;
  /** 필터링할 레이어 타입들 (기본적으로 draw, measure, clear 제외) */
  excludeLayerTypes?: string[];
  /** 추가 필터링 조건 */
  layerFilter?: (layer: any) => boolean;
  /** 그룹 opacity 활성화 여부 */
  isGroupOpacityEnabled?: boolean;
  /** 레이어 opacity 활성화 여부 */
  isLayerOpacityEnabled?: boolean;
}

/**
 * useLayer 상태만 사용하는 TOC Widget
 *
 * 특징:
 * - useTOC 제거하고 useLayer 직접 사용
 * - 이중 상태 관리 문제 해결
 * - Layer 컴포넌트와 완벽한 동기화
 */
export const UseLayerTOCWidget = React.forwardRef<
  HTMLDivElement,
  UseLayerTOCWidgetProps
>(
  (
    {
      className = "absolute left-5 top-20 flex max-h-[700px] min-h-[300px] w-[400px] flex-col",
      showHeader = true,
      excludeLayerTypes = ["draw", "measure", "clear"],
      layerFilter,
      isGroupOpacityEnabled = false,
      isLayerOpacityEnabled = false,
      ...props
    },
    ref,
  ) => {
    const { layers, setVisible, setOpacity, getOpacity, fitToLayer } =
      useLayer();
    const [expandedGroups, setExpandedGroups] = useState<Set<string>>(
      new Set(),
    );

    console.log("[UseLayerTOCWidget] Render", {
      layersCount: layers.length,
      excludeLayerTypes,
    });

    // useLayer 상태를 TOCNode 형식으로 변환
    const tocNodes = useMemo((): TOCNode[] => {
      if (!layers || layers.length === 0) return [];

      let filteredLayers = layers;

      // 기본 타입 필터링
      if (excludeLayerTypes.length > 0) {
        filteredLayers = filteredLayers.filter(
          (layer) => !excludeLayerTypes.includes(layer.type || ""),
        );
      }

      // 커스텀 필터 적용
      if (layerFilter) {
        filteredLayers = filteredLayers.filter(layerFilter);
      }

      // Layer를 TOCNode로 변환
      const tocNodes: TOCNode[] = filteredLayers.map((layer) => ({
        id: layer.id,
        name: layer.name || layer.id,
        type: "layer" as const,
        visible: layer.visible ?? true,
        opacity: layer.opacity ?? 1,
        layerId: layer.id, // ODF 레이어 ID
      }));

      console.log("[UseLayerTOCWidget] TOC Nodes:", tocNodes);
      return tocNodes;
    }, [layers, excludeLayerTypes, layerFilter]);

    // 레이어 visibility 토글
    const handleToggleVisibility = useCallback(
      (id: string, visible: boolean) => {
        console.log(
          `[UseLayerTOCWidget] Layer ${id} visibility changed to:`,
          visible,
        );
        setVisible(id, visible);
      },
      [setVisible],
    );

    // 레이어 opacity 변경
    const handleOpacityChange = useCallback(
      (id: string, opacity: number) => {
        console.log(
          `[UseLayerTOCWidget] Layer ${id} opacity changed to:`,
          opacity,
        );
        setOpacity(id, opacity);
      },
      [setOpacity],
    );

    // 레이어로 줌
    const handleZoomToLayer = useCallback(
      (id: string) => {
        console.log(`[UseLayerTOCWidget] Zoom to layer:`, id);
        fitToLayer(id);
      },
      [fitToLayer],
    );

    // 레이어 아이템 렌더링
    const renderLayerItem = useCallback(
      (node: TOCNode) => {
        // node가 layer 타입인지 확인
        if (node.type !== "layer") return null;

        const layer = layers.find((l) => l.id === node.layerId);
        const currentVisible = layer?.visible ?? node.visible ?? true;
        const currentOpacity = layer?.opacity ?? node.opacity ?? 1;

        return (
          <div
            key={node.id}
            className="flex items-center gap-2 p-2 rounded cursor-pointer hover:bg-gray-50"
          >
            {/* Visibility Toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handleToggleVisibility(node.id, !currentVisible);
              }}
              className="p-1 h-6 w-6"
            >
              {currentVisible ? (
                <Eye className="h-4 w-4" />
              ) : (
                <EyeOff className="h-4 w-4" />
              )}
            </Button>

            {/* Layer Name */}
            <span
              className="flex-1 text-sm truncate"
              onClick={() => handleZoomToLayer(node.id)}
            >
              {node.name}
            </span>

            {/* Opacity Slider */}
            {isLayerOpacityEnabled && (
              <div className="w-20">
                <Slider
                  value={[currentOpacity * 100]}
                  onValueChange={([value]) =>
                    handleOpacityChange(node.id, (value ?? 100) / 100)
                  }
                  max={100}
                  step={1}
                  className="w-full"
                />
              </div>
            )}
          </div>
        );
      },
      [
        layers,
        handleToggleVisibility,
        handleZoomToLayer,
        handleOpacityChange,
        isLayerOpacityEnabled,
      ],
    );

    return (
      <div
        ref={ref}
        className={cn(
          "bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden",
          className,
        )}
        {...props}
      >
        {/* Header */}
        {showHeader && (
          <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
            <h3 className="text-sm font-medium text-gray-900">레이어 목록</h3>
            <Button variant="ghost" size="sm">
              <Layers className="h-4 w-4" />
            </Button>
          </div>
        )}

        {/* Body */}
        <ScrollArea className="flex-1">
          <div className="p-2 space-y-1">
            {tocNodes.length === 0 ? (
              <div className="text-center text-gray-500 text-sm py-4">
                표시할 레이어가 없습니다
              </div>
            ) : (
              tocNodes.map(renderLayerItem)
            )}
          </div>
        </ScrollArea>
      </div>
    );
  },
);
UseLayerTOCWidget.displayName = "UseLayerTOCWidget";
