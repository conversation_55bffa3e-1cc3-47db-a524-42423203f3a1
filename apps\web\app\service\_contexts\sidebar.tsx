"use client";

import { SidebarProvider, useSidebar } from "@geon-ui/react/primitives/sidebar";
import { usePathname } from "next/navigation";
import type { ReactNode } from "react";
import { createContext, useContext, useEffect, useMemo, useState } from "react";

type ServiceSidebarContextProps = {
  // outer sidebar state
  outer: ReturnType<typeof useSidebar>;

  // inner sidebar state
  innerOpen: boolean;
  setInnerOpen: (open: boolean) => void;

  // navigation state
  active: string | null;
  setActive: (menu: string | null) => void;
  content: ReactNode | null;
  setContent: (content: ReactNode | null) => void;

  // layer-based service selection
  selectedServiceId: string | null;
  setSelectedServiceId: (serviceId: string | null) => void;
};

const ServiceSidebarContext = createContext<ServiceSidebarContextProps | null>(
  null,
);

export function useServiceSidebar() {
  const context = useContext(ServiceSidebarContext);
  if (!context) {
    throw new Error(
      "useServiceSidebar must be used within a ServiceSidebarProvider",
    );
  }
  return context;
}

export function ServiceInnerSidebarProvider({
  children,
}: {
  children: ReactNode;
}) {
  const { innerOpen, setInnerOpen } = useServiceSidebar();

  return (
    <SidebarProvider
      open={innerOpen}
      onOpenChange={setInnerOpen}
      style={
        {
          "--sidebar-width": "28rem",
        } as React.CSSProperties
      }
    >
      {children}
    </SidebarProvider>
  );
}

export function ServiceSidebarProvider({ children }: { children: ReactNode }) {
  const outer = useSidebar();
  const pathname = usePathname();

  const getInitialState = () => {
    const pathSegments = pathname.split("/").filter(Boolean);

    if (pathSegments[0] === "service" && pathSegments[1]) {
      const serviceId = pathSegments[1];
      const subServiceId = pathSegments[2];

      // 서비스 페이지(/service/road, /service/water 등)에 있다면 innerSidebar를 열어야 함
      return {
        innerOpen: true,
        selectedServiceId: subServiceId
          ? `${serviceId}:${subServiceId}`
          : serviceId,
        active: subServiceId ? serviceId : null,
      };
    }

    return {
      innerOpen: false,
      selectedServiceId: null,
      active: null,
    };
  };

  const initialState = getInitialState();
  const [innerOpen, setInnerOpen] = useState(initialState.innerOpen);
  const [active, setActive] = useState<string | null>(initialState.active);
  const [content, setContent] = useState<React.ReactNode | null>(null);
  const [selectedServiceId, setSelectedServiceId] = useState<string | null>(
    initialState.selectedServiceId,
  );

  // URL 변경 시 상태 업데이트
  useEffect(() => {
    const newState = getInitialState();
    setInnerOpen(newState.innerOpen);
    setSelectedServiceId(newState.selectedServiceId);
    setActive(newState.active);

    // innerSidebar가 열려야 하는 상황이면 outer sidebar는 닫기
    if (newState.innerOpen) {
      outer.setOpen(false);
    }
  }, [pathname, outer]);

  const contextValue = useMemo(
    () => ({
      outer,
      innerOpen,
      setInnerOpen,
      active,
      setActive,
      content,
      setContent,
      selectedServiceId,
      setSelectedServiceId,
    }),
    [
      outer,
      innerOpen,
      setInnerOpen,
      active,
      setActive,
      content,
      setContent,
      selectedServiceId,
      setSelectedServiceId,
    ],
  );

  return (
    <ServiceSidebarContext.Provider value={contextValue}>
      {children}
    </ServiceSidebarContext.Provider>
  );
}
