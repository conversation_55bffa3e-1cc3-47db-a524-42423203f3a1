"use client";

import { createGeonMagpClient } from "@geon-query/model";
import { useAppMutation } from "@geon-query/react-query";
import React from "react";

interface AttachmentUploaderProps {
  registerId: string;
  onUploaded: (atchmnflId: string | null) => void; // null도 전달 가능
}

export default function AttachmentUploader({
  registerId,
  onUploaded,
}: AttachmentUploaderProps) {
  const client = createGeonMagpClient();
  const [atchmnflId, setAtchmnflId] = React.useState<string | null>(null);

  // file input ref
  const fileInputRef = React.useRef<HTMLInputElement | null>(null);

  /** 업로드 mutation */
  const uploadMutation = useAppMutation({
    mutationFn: async (file: File) =>
      client.attachment.upload({ registerId }, [
        { file, field: "attachments" },
      ]),
    onSuccess: (res) => {
      setAtchmnflId(res.result.atchmnflId);
      onUploaded(res.result.atchmnflId);
    },
    onError: (err: any) => {
      console.error(err);
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    if (!file) return;
    uploadMutation.mutate(file);
  };

  /** 업로드 취소 (= 상태 및 input 초기화) */
  const handleRemove = () => {
    setAtchmnflId(null);
    onUploaded(null);

    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className="mt-4">
      <label className="mb-1 block text-sm font-medium">첨부파일</label>
      <input type="file" ref={fileInputRef} onChange={handleFileChange} />

      {uploadMutation.isPending && (
        <p className="text-gray-500">업로드 중...</p>
      )}

      {atchmnflId && (
        <div className="mt-2 flex items-center gap-2 text-green-600">
          <span>업로드됨: {atchmnflId}</span>
          <button
            type="button"
            onClick={handleRemove}
            className="rounded bg-red-100 px-2 py-0.5 text-xs text-red-600 hover:bg-red-200"
          >
            ❌ 취소
          </button>
        </div>
      )}

      {uploadMutation.isError && (
        <div
          className="mt-2 rounded border border-red-400 bg-red-100 px-4 py-2 text-sm text-red-700"
          role="alert"
        >
          <strong className="font-bold">에러!</strong>
          <span className="ml-2">
            {uploadMutation.error instanceof Error
              ? uploadMutation.error.message
              : "업로드 처리 중 오류가 발생했습니다."}
          </span>
        </div>
      )}
    </div>
  );
}
