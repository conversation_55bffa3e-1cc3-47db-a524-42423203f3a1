import { ControlsProvider, MapProvider } from "@geon-map/react-odf";
import React from "react";

import { OuterSidebar } from "./_components";
import { MapInnerSidebarProvider } from "./_contexts/sidebar";

/**
 * 맞춤형 지도 조회 페이지 홈 화면
 *
 * OuterSidebar 만 제공
 */
export default function Page() {
  return (
    <React.Fragment>
      <OuterSidebar />
      <MapInnerSidebarProvider>
        <MapProvider defaultOptions={{ projection: "EPSG:5186" }}>
          <ControlsProvider
            scaleOptions={{ size: 100, scaleInput: false }}
            clearOptions={{ clearAll: true }}
            measureOptions={{
              tools: ["distance", "area", "round", "spot"],
              continuity: false,
            }}
            overviewOptions={{ enabled: true }}
            drawOptions={{
              continuity: false,
              tools: [
                "text",
                "polygon",
                "lineString",
                "box",
                "point",
                "circle",
                "curve",
              ],
              style: {
                fill: { color: [255, 255, 0, 1] },
                stroke: { color: [0, 255, 0, 0.8], width: 5 },
              },
            }}
          >
            <div className="fixed left-0 top-0 flex h-screen w-screen items-center justify-center overflow-hidden">
              Map Home Page
            </div>
          </ControlsProvider>
        </MapProvider>
      </MapInnerSidebarProvider>
    </React.Fragment>
  );
}
