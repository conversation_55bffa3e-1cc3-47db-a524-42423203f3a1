/**
 * 데이터베이스 컬럼 정보
 */
export interface ColumnInfo {
  /** 컬럼명 */
  columnName: string;
  /** 컬럼 타입 */
  columnType: string;
  /** 컬럼 설명 */
  columnComment: string;
  /** 컬럼 순서 */
  ordinalPosition: number;
}

/**
 * 지오메트리 컬럼 정보
 */
export interface GeometryColumn {
  /** 컬럼명 */
  columnName: string;
  /** 지오메트리 타입 */
  geometryType: string;
  /** 좌표계 ID */
  srid: number;
}

/**
 * 테이블 스키마 정보
 */
export interface TableSchema {
  /** 스키마명 */
  schemaName: string;
  /** 테이블명 */
  tableName: string;
  /** 테이블 타입 코드 */
  tableTypeCode: string;
  /** 테이블 타입 */
  tableType: string;
  /** 컬럼 정보 배열 */
  columns: ColumnInfo[];
  /** 지오메트리 컬럼 정보 배열 */
  geometryColumns: GeometryColumn[];
}

/**
 * API 응답 구조
 */
export interface TableSchemaResponse {
  /** 응답 코드 */
  code: number;
  /** 응답 메시지 */
  message: string;
  /** 결과 데이터 */
  result: TableSchema;
}

/**
 * 동적 테이블 렌더링을 위한 Props
 */
export interface DynamicTableProps {
  /** 테이블 스키마 정보 */
  schema: TableSchema;
  /** 테이블 데이터 (컬럼명 기준) */
  data: Record<string, any>;
  /** 로딩 상태 */
  loading?: boolean;
  /** 제외할 컬럼명들 */
  excludeColumns?: string[];
  /** 컬럼별 커스텀 렌더러 */
  columnRenderers?: Record<string, (value: any) => React.ReactNode>;
  /** 테이블 클래스명 */
  className?: string;
}

/**
 * 컬럼 값 포맷팅 결과
 */
export interface FormattedValue {
  /** 포맷팅된 값 */
  value: string;
  /** 원본 값 */
  rawValue: any;
}
