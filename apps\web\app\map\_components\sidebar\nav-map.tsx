"use client";

import { Avatar, AvatarFallback } from "@geon-ui/react/primitives/avatar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@geon-ui/react/primitives/collapsible";
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@geon-ui/react/primitives/sidebar";
import { ChevronRight } from "lucide-react";
import Link from "next/link";

import { safeUrl } from "@/utils/security";

import { useMapSidebar } from "../../_contexts/sidebar";
import { MAPS } from "../../_utils";

export default function NavMap() {
  const { active, setActive, outer, innerOpen, setInnerOpen } = useMapSidebar();

  // TODO: MAPS 리스트 받아오기

  return (
    <SidebarGroup>
      <SidebarMenu>
        {MAPS.map((map) => {
          return (
            <Collapsible
              key={map.department}
              asChild
              className="group/collapsible"
              open={active === map.department}
            >
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton
                    tooltip={map.department}
                    onClick={() => {
                      if (!outer.open) outer.setOpen(true);
                      if (innerOpen) setInnerOpen(false);
                      setActive(map.department);
                    }}
                  >
                    <Avatar className="-ml-2 size-8">
                      <AvatarFallback>
                        {map.department.slice(0, 2)}
                      </AvatarFallback>
                    </Avatar>
                    <span>{map.department}</span>
                    <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    {map.groups.map((group) => {
                      return (
                        <Collapsible
                          asChild
                          key={`${map.department}${group.code}`}
                        >
                          <SidebarMenuSubItem>
                            <CollapsibleTrigger asChild>
                              <SidebarMenuSubButton>
                                {group.code}
                              </SidebarMenuSubButton>
                            </CollapsibleTrigger>
                            <CollapsibleContent>
                              {group.items.map((item) => (
                                <SidebarMenuSubButton
                                  key={item.id}
                                  className="ml-4"
                                  asChild
                                >
                                  <Link
                                    href={decodeURIComponent(
                                      safeUrl(`/map/${item.id}`) || "",
                                    )}
                                    onClick={() => {
                                      if (outer.open) outer.setOpen(false);
                                      setInnerOpen(true);
                                    }}
                                  >
                                    {item.title}
                                  </Link>
                                </SidebarMenuSubButton>
                              ))}
                            </CollapsibleContent>
                          </SidebarMenuSubItem>
                        </Collapsible>
                      );
                    })}
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
