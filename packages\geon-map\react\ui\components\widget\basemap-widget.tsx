"use client"; // 클라이언트 컴포넌트임을 명시 (React 18 이상)

import { BaseLayerInfo, LayerProps, useBaseLayer } from "@geon-map/react-odf";
import { apiUrl } from "@geon-query/model";
import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import { Checkbox } from "@geon-ui/react/primitives/checkbox";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@geon-ui/react/primitives/hover-card";
import { MapIcon } from "lucide-react";
import * as React from "react";

// 기본값 import

// 1. 타입 정의 - 기존 BasemapInfo와 호환성 유지
export type BasemapInfo = BaseLayerInfo;

// 2. 전체 매핑 타입 정의 (선택적으로 쓰고 싶을 때는 Partial로도 가능)
export type BasemapMapType = BasemapInfo[];

const DEFAULT_BASE_LAYERS: BaseLayerInfo[] = [
  {
    id: "eMapBasic",
    name: "바로e맵 기본지도",
    description: "바로e맵 기본 지도 레이어",
    category: "바로e맵",

    // 🆕 새로운 필드들
    layerType: "base",
    layerParams: {
      projection: "EPSG:5179",
      version: "1.0.0",
      format: "image/png",
      request: "GetTile",
      layer: "korean_map",
      style: "korean",
      tileGrid: {
        origin: [-200000, 4000000],
        resolutions: [
          2088.96, 1044.48, 522.24, 261.12, 130.56, 65.28, 32.64, 16.32, 8.16,
          4.08, 2.04, 1.02, 0.51, 0.255, 0.1275, 0.06375,
        ],
        matrixIds: [
          "L05",
          "L06",
          "L07",
          "L08",
          "L09",
          "L10",
          "L11",
          "L12",
          "L13",
          "L14",
          "L15",
          "L16",
          "L17",
          "L18",
          "L19",
          "L20",
        ],
      },
      service: "wmts",
      server: "https://city.geon.kr/api/map/api/map/baroemap",
    },
  },
];

export const hybridLayerOption: LayerProps = {
  fit: false,
  visible: false,
  type: "api",
  server: apiUrl({
    endpoint:
      "/api/vworld/wmts/{{layer}}/{{tileMatrix}}/{{tilerow}}/{{tileCol}}.png",
    type: "map",
  }),
  service: "wmts",
  projection: "EPSG:3857",
  layer: "Hybrid",
  tileGrid: {
    origin: [-20037508.342789244, 20037508.342789244],
    resolutions: [
      2445.98490512564, 1222.99245256282, 611.49622628141, 305.748113140705,
      152.8740565703525, 76.43702828517625, 38.21851414258813,
      19.109257071294063, 9.554628535647032, 4.777314267823516,
      2.388657133911758, 1.194328566955879, 0.5971642834779395,
      0.29858214173896974,
    ],
    matrixIds: [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19],
  },
};

// 3. 기본값은 constants에서 import
export const DEFAULT_BASE_MAPS: BasemapMapType = DEFAULT_BASE_LAYERS;

// BasemapContext: 현재 선택된 배경지도 상태를 하위 컴포넌트에 공유하기 위한 컨텍스트
export const BasemapContext = React.createContext<{
  basemapInfo?: BasemapInfo;
  onValueChange?: (value: BasemapInfo) => void;
} | null>(null);

// BasemapContext를 사용하는 훅
export function useBasemapContext() {
  const context = React.useContext(BasemapContext);
  if (!context) {
    throw new Error("Basemap 컴포넌트 내부에서만 사용할 수 있습니다.");
  }
  return context;
}

// Basemap 컴포넌트 - 배경지도 선택 기능의 루트
export interface BasemapProps extends React.ComponentPropsWithoutRef<"div"> {
  /**
   * 현재 선택된 배경지도 ID
   * @default 'eMapBasic'
   */
  basemapInfo?: BasemapInfo;
  /**
   * 배경지도가 변경될 때 호출되는 함수
   */

  onValueChange?: (value: BasemapInfo) => void;
}

export function Basemap({
  basemapInfo,
  onValueChange,
  className,
  children,
  ...props
}: BasemapProps) {
  // 🆕 새로운 BaseLayer 시스템 사용
  const { switchBaseLayer } = useBaseLayer();

  const handleValueChange = React.useCallback(
    async (_basemapInfo: BasemapInfo) => {
      console.log("🎯 Basemap switching to:", _basemapInfo.name);
      // 🆕 새로운 BaseLayer 시스템 사용
      await switchBaseLayer(_basemapInfo.id);
      onValueChange?.(_basemapInfo); // 외부 콜백 호출
    },
    [switchBaseLayer, onValueChange],
  );

  React.useEffect(() => {
    if (basemapInfo) {
      // 🆕 컴포넌트 마운트 시 초기 배경지도 설정
      switchBaseLayer(basemapInfo.id);
    }
  }, [basemapInfo, switchBaseLayer]);

  return (
    <BasemapContext.Provider
      value={{ basemapInfo, onValueChange: handleValueChange }}
    >
      <div className={cn("inline-flex", className)} {...props}>
        <HoverCard openDelay={100}>
          {children} {/* Trigger + Content 등 포함됨 */}
        </HoverCard>
      </div>
    </BasemapContext.Provider>
  );
}

// BasemapTrigger: 사용자 입력을 받아 HoverCard를 열어주는 버튼 역할

export function BasemapTrigger({
  className,
  children,
  ...props
}: React.ComponentPropsWithoutRef<"button">) {
  return (
    <HoverCardTrigger asChild>
      <Button className={cn(className)} {...props}>
        {children}
      </Button>
    </HoverCardTrigger>
  );
}

// BasemapContent: HoverCard에 표시될 배경지도 선택 영역 (HoverCard의 본문)
export function BasemapContent({
  className,
  children,
  ...props
}: React.ComponentPropsWithoutRef<typeof HoverCardContent>) {
  return (
    <HoverCardContent
      className={cn(
        "flex flex-col gap-0.5 p-0.5 bg-background/90 backdrop-blur-md border shadow-lg rounded-xl",
        className,
      )}
      align="end"
      side="left"
      alignOffset={-40}
      sideOffset={8}
      {...props}
    >
      {children} {/* Item 목록 들어감 */}
    </HoverCardContent>
  );
}
// BasemapItem: 선택 가능한 개별 배경지도 항목 버튼
export interface BasemapItemProps
  extends React.ComponentPropsWithoutRef<"button"> {
  basemapInfo: BasemapInfo; // 해당 아이템이 나타내는 배경지도 ID
}
// BasemapItem: 배경지도 항목 하나 (선택하면 onValueChange 발생)
export function BasemapItem({
  basemapInfo,
  className,
  children,
  ...props
}: BasemapItemProps) {
  const { onValueChange } = useBasemapContext();
  return (
    <Button
      variant="ghost"
      size="sm"
      className={cn(
        "w-full h-auto p-0 justify-start text-sm relative group transition-all duration-200",
        //isSelected ? "bg-muted" : "hover:bg-muted/50",
        className,
      )}
      onClick={() => onValueChange?.(basemapInfo)} // 클릭 시 선택 값 변경
      {...props}
    >
      {children}
    </Button>
  );
}

// Props 정의
export interface BasemapWidgetProps {
  selectBasemap?: BasemapInfo;
  onValueChange?: (value: BasemapInfo) => void;
  className?: string;
  baseMaps?: BasemapMapType;
  hybrid?: boolean;
}

export type GroupedBasemaps = Record<string, BasemapInfo[]>;

//편리하게 사용할 수 있는 위젯 - 새로운 BaseLayer 시스템 사용
export function BasemapWidget({
  selectBasemap = DEFAULT_BASE_MAPS[0],
  onValueChange,
  className,
  hybrid = true,
}: BasemapWidgetProps) {
  // 🆕 새로운 BaseLayer 시스템 사용
  const {
    currentBaseLayerId,
    categories,
    getBaseLayersByCategory,
    switchBaseLayer,
    overlayCategories,
    getOverlayLayersByCategory,
    toggleOverlayLayer,
    isOverlayLayerActive,
  } = useBaseLayer();

  const [selectedGroup, setSelectedGroup] = React.useState<string>("");
  const [hybridEnabled, setHybridEnabled] = React.useState<boolean>(!!hybrid);

  // 첫 번째 카테고리를 기본 선택
  React.useEffect(() => {
    if (categories.length > 0 && !selectedGroup) {
      setSelectedGroup(categories[0]);
    }
  }, [categories, selectedGroup]);

  // 현재 선택된 그룹의 레이어들
  const currentBaseItems = getBaseLayersByCategory(selectedGroup);
  const currentOverlayItems = getOverlayLayersByCategory(selectedGroup);

  // 모든 카테고리
  const allCategories = [...new Set([...categories, ...overlayCategories])];

  // 하이브리드 토글 핸들러
  const handleHybridToggle = React.useCallback(
    async (checked: boolean) => {
      setHybridEnabled(checked);
      // 현재 그룹의 모든 하이브리드 레이어 토글
      for (const layer of currentOverlayItems) {
        if (layer.layerType === "hybrid") {
          const isActive = isOverlayLayerActive(layer.id);
          if (checked && !isActive) {
            await toggleOverlayLayer(layer.id);
          } else if (!checked && isActive) {
            await toggleOverlayLayer(layer.id);
          }
        }
      }
    },
    [currentOverlayItems, isOverlayLayerActive, toggleOverlayLayer],
  );

  // 배경지도 선택 핸들러
  const handleBaseLayerSelect = React.useCallback(
    async (basemapInfo: BasemapInfo) => {
      console.log("🎯 Switching to base layer:", basemapInfo.name);
      await switchBaseLayer(basemapInfo.id);
      onValueChange?.(basemapInfo);
    },
    [switchBaseLayer, onValueChange],
  );

  return (
    <Basemap
      basemapInfo={selectBasemap}
      onValueChange={onValueChange}
      className={cn("absolute right-4 top-20 flex flex-col gap-2", className)}
    >
      <BasemapTrigger className="bg-white/70 hover:bg-white dark:bg-zinc-800/90 dark:hover:bg-zinc-800">
        <MapIcon />
      </BasemapTrigger>

      <BasemapContent>
        {/* 상단 바: 그룹 선택 + 하이브리드 체크박스 */}
        <div className="sticky top-0 z-10 mb-2 flex items-center gap-3 bg-white/80 p-2 backdrop-blur-sm dark:bg-zinc-900/70">
          <select
            className="w-full rounded-md border border-zinc-300 bg-white px-2 py-1 text-sm outline-none focus:ring dark:border-zinc-700 dark:bg-zinc-800"
            value={selectedGroup}
            onChange={(e) => setSelectedGroup(e.target.value)}
            disabled={allCategories.length === 0}
          >
            {allCategories.map((key) => (
              <option key={key} value={key}>
                {key || "기타"}
              </option>
            ))}
          </select>

          {/* Select 오른쪽에 하이브리드 체크박스 - 오버레이 레이어가 있을 때만 표시 */}
          {currentOverlayItems.length > 0 && (
            <label className="flex items-center gap-2 text-xs text-zinc-700 dark:text-zinc-300">
              <Checkbox
                checked={hybridEnabled}
                onCheckedChange={(v) => handleHybridToggle(!!v)}
                aria-label="하이브리드 지도"
              />
              하이브리드
            </label>
          )}
        </div>

        {/* 배경지도 리스트 */}
        {currentBaseItems.map((layer) => (
          <BasemapItem
            key={layer.id}
            basemapInfo={layer}
            onClick={() => handleBaseLayerSelect(layer)}
          >
            <div className="flex items-center gap-3 rounded-md p-2 transition-colors duration-200 hover:bg-white/70 dark:hover:bg-zinc-800/60">
              {/* 썸네일 - 고정 크기로 개선 */}
              <div className="flex h-12 w-12 shrink-0 items-center justify-center overflow-hidden rounded-md bg-zinc-100 dark:bg-zinc-700">
                {layer.thumbnail ? (
                  <img
                    src={layer.thumbnail}
                    alt={layer.name}
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <span className="text-xs text-zinc-500">IMG</span>
                )}
              </div>

              {/* 정보 */}
              <div className="flex flex-col items-start gap-0.5 min-w-0 flex-1">
                <span className="font-medium text-sm truncate">
                  {layer.name}
                </span>
                <span className="text-xs leading-tight text-zinc-500 dark:text-zinc-400 truncate">
                  {layer.category}
                </span>
              </div>

              {/* 선택 표시 */}
              {currentBaseLayerId === layer.id && (
                <div className="h-2 w-2 shrink-0 rounded-full bg-blue-500"></div>
              )}
            </div>
          </BasemapItem>
        ))}

        {/* 오버레이 레이어 목록 (하이브리드 활성화 시) */}
        {hybridEnabled && currentOverlayItems.length > 0 && (
          <div className="border-t pt-2 mt-2">
            <div className="mb-2 px-2 text-xs font-medium text-zinc-600 dark:text-zinc-400">
              오버레이
            </div>
            {currentOverlayItems.map((layer) => {
              const isActive = isOverlayLayerActive(layer.id);
              return (
                <div
                  key={layer.id}
                  className="flex items-center gap-3 rounded-md p-2 hover:bg-zinc-50 dark:hover:bg-zinc-700/40"
                >
                  <Checkbox
                    checked={isActive}
                    onCheckedChange={() => toggleOverlayLayer(layer.id)}
                    className="h-3 w-3 shrink-0"
                  />
                  <span className="text-sm flex-1 min-w-0 truncate">
                    {layer.name}
                  </span>
                </div>
              );
            })}
          </div>
        )}
      </BasemapContent>
    </Basemap>
  );
}

// 별칭 export
export const Root = Basemap;
export const Trigger = BasemapTrigger;
export const Content = BasemapContent;
export const Item = BasemapItem;
export const Widget = BasemapWidget;
