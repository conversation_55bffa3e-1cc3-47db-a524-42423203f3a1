"use client";

/**
 * shadcn Button 컴포넌트에 테마 색상만 오버라이드한 버튼 컴포넌트
 * 폴더 위치는 조정 필요해보임.
 */

import { cn } from "@geon-ui/react/lib/utils";
import { Button as ShadcnButton } from "@geon-ui/react/primitives/button";
import * as React from "react";

export interface ButtonProps extends React.ComponentProps<typeof ShadcnButton> {
  /** 활성 상태 */
  active?: boolean;
}

// Button Component - shadcn Button + geon.css 색상 시스템
export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ active = false, className, variant = "ghost", ...props }, ref) => {
    return (
      <ShadcnButton
        ref={ref}
        variant={variant}
        className={cn(
          // geon.css 색상 적용
          variant === "default" && [
            "bg-geon-primary text-geon-primary-foreground",
            "hover:bg-geon-hover hover:text-geon-hover-foreground",
            "active:bg-geon-pressed active:text-geon-pressed-foreground",
          ],
          variant === "secondary" && [
            "bg-geon-secondary text-geon-secondary-foreground",
            "hover:bg-geon-hover hover:text-geon-hover-foreground",
            "border-geon-border hover:border-geon-primary/30",
          ],
          variant === "ghost" && [
            "text-geon-foreground",
            "hover:bg-geon-hover hover:text-geon-hover-foreground",
            "active:bg-geon-pressed active:text-geon-pressed-foreground",
          ],
          variant === "destructive" && [
            "bg-geon-destructive text-geon-destructive-foreground",
            "hover:bg-geon-destructive/90",
          ],
          // 활성 상태
          active && [
            "bg-geon-active text-geon-active-foreground",
            "hover:bg-geon-pressed hover:text-geon-pressed-foreground",
            "ring-1 ring-geon-primary/20",
          ],
          className,
        )}
        {...props}
      />
    );
  },
);

Button.displayName = "Button";
