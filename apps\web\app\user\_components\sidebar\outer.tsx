import { cn } from "@geon-ui/react/lib/utils";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
} from "@geon-ui/react/primitives/sidebar";
import React from "react";

import HomeButton from "./home-button";
import NavService from "./nav-service";
import NavUser from "./nav-user";

// This is sample data.
const data = {
  user: {
    name: "user1",
    role: "Administrator",
    avatar: "/avatars/shadcn.jpg",
  },
};

export default function OuterSidebar({
  className,
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar {...props} collapsible="icon" className={cn("z-50", className)}>
      <SidebarHeader>
        <HomeButton />
      </SidebarHeader>
      <SidebarContent>
        <NavService />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  );
}
