"use client";

import { RegionSelectorWidget } from "@geon-map/react-ui/components";
import { useRegionSelector } from "@geon-map/react-ui/hooks";
interface RegionSelectorWidgetServiceProps {
  className?: string;
}
// 🔧 API 클라이언트 설정 방법 (둘 중 하나 선택)
// ✅ 방법 1: 기본 설정 사용 (geonAPI.ts의 BASE_URL, crtfckey 사용)
// import { defaultGeonAddrgeoClient } from "@geon-query/model";

// ✅ 방법 2: 커스텀 설정 사용 (직접 baseUrl, apiKey 지정)
import { createGeonAddrgeoClient } from "@geon-query/model";

export default function RegionSelectorWidgetService({
  className = "absolute left-100 top-5 flex items-center gap-2",
}: RegionSelectorWidgetServiceProps) {
  //const apiClient = defaultGeonAddrgeoClient;
  const apiClient = createGeonAddrgeoClient({
    baseUrl: "https://city.geon.kr/api/",
    crtfckey: "UxizIdSqCePz93ViFt8ghZFFJuOzvUp0",
  });

  const { handleRegionSelect, handleRegionList, handlePnuSelect } =
    useRegionSelector({
      apiType: "geon",
      apiClient,
    });

  return (
    <RegionSelectorWidget
      // 지역 선택시 호출되며, 폴리곤 정보 조회
      fetchRegionInfo={handleRegionSelect}
      // 지역 선택시 호출되며, 하위 지역 목록 표출
      fetchRegionList={handleRegionList}
      //지도의 지역 셋팅을 위해 현재 지도의 pnu 데이터 조회(지도이동시에도 포함)
      fetchPnu={handlePnuSelect}
      className={className}
      useLi={false}
    />
  );
}
