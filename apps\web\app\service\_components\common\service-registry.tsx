"use client";

import { ComponentType, lazy } from "react";

/**
 * 서비스 컴포넌트 타입 정의
 */
export interface ServiceComponents {
  DetailView: ComponentType<any>;
  DetailForm: ComponentType<any>;
  DetailHeader: ComponentType<any>;
}

/**
 * 서비스별 컴포넌트 동적 로딩 레지스트리
 */
export const serviceRegistry: Record<string, () => Promise<ServiceComponents>> =
  {
    road: () =>
      import("../services/road").then((module) => ({
        DetailView: module.RoadDetailView,
        DetailForm: module.RoadDetailForm,
        DetailHeader: module.RoadDetailHeader,
      })),
    water: () =>
      import("../services/water").then((module) => ({
        DetailView: module.RoadDetailView,
        DetailForm: module.RoadDetailForm,
        DetailHeader: module.RoadDetailHeader,
      })),

    // 다른 서비스들을 여기에 추가
    // facility: () => import("../services/facility").then(...),
  };

/**
 * 서비스 컴포넌트 동적 로딩 함수
 */
export function loadServiceComponents(
  serviceName: string,
): ServiceComponents | null {
  const serviceLoader = serviceRegistry[serviceName];

  if (!serviceLoader) {
    console.warn(`Service "${serviceName}" not found in registry`);
    return null;
  }

  // lazy loading으로 컴포넌트 생성
  const DetailView = lazy(() =>
    serviceLoader().then((components) => ({
      default: components.DetailView,
    })),
  );

  const DetailForm = lazy(() =>
    serviceLoader().then((components) => ({
      default: components.DetailForm,
    })),
  );

  const DetailHeader = lazy(() =>
    serviceLoader().then((components) => ({
      default: components.DetailHeader,
    })),
  );

  return {
    DetailView,
    DetailForm,
    DetailHeader,
  };
}

/**
 * 지원되는 서비스 목록 조회
 */
export function getSupportedServices(): string[] {
  return Object.keys(serviceRegistry);
}
