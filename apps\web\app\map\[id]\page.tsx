import { MapContainer } from "@geon-map/react-odf";
import { SidebarInset } from "@geon-ui/react/primitives/sidebar";
import { notFound } from "next/navigation";
import React from "react";

import { InnerSidebar } from "./_components";

/**
 * 맞춤형 지도 개별 조회 화면
 *
 * 개별 map config 설정해주는 wrapper 필요
 */
export default async function Page({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  // TODO: if there is no config, return 404
  if (!id) notFound();

  return (
    <React.Fragment>
      <InnerSidebar mapId={id} />
      <SidebarInset className="fixed left-0 top-0 h-screen w-screen overflow-hidden">
        {/* key 변경으로 맵 refresh */}
        <MapContainer key={id} className="size-full" />
      </SidebarInset>
    </React.Fragment>
  );
}
