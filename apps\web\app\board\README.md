# Board/Announce 가이드 (Edit 라우팅 반영)

`apps/web/app/board` 내 게시판 모듈의 구조와 컨벤션을 정리합니다. 공지사항(announce)을 기준으로 목록/상세/등록/수정 흐름을 설명합니다.

## 폴더 구조

```
apps/web/app/board/
├─ README.md                       # 문서
├─ _components/
│  ├─ search-form.tsx              # 공통 검색 컴포넌트(+ 입력)
│  ├─ pagination-link.tsx          # 페이지 파라미터를 보존하는 Link
│  └─ hooks/
│     ├─ use-pagination-params.tsx # URL의 page/size 추출
│     └─ use-pagination-router.tsx # page/size 유지 push/replace
├─ _utils/
│  └─ index.ts                     # 날짜 포맷 등 유틸
└─ announce/                       # 공지(Announce)
   ├─ layout.tsx                   # (선택) announce 전용 레이아웃
   ├─ page.tsx                     # 목록 진입 (SearchForm + List)
   ├─ create/
   │  └─ page.tsx                  # 등록 페이지 엔트리
   ├─ [id]/
   │  ├─ page.tsx                  # 상세 진입 (params 처리)
   │  └─ edit/
   │     └─ page.tsx               # 수정 페이지 (라우팅 전환)
   └─ _components/
      └─ tables/
         ├─ list.tsx               # 목록: 조회/컬럼/링크 + 등록 버튼
         ├─ detail.tsx             # 상세: 조회 + 수정/삭제
         ├─ view.tsx               # 상세 보기용 테이블 뷰
         └─ edit.tsx               # 수정 폼 UI
```

## 의존성/런타임
- 데이터/클라이언트: `@geon-query/model` 의 `createGeonMagpClient()`
- 캐싱/쿼리: `@geon-query/react-query` 의 `useAppQuery`, `useAppMutation`, `useAppQueryClient`
- 테이블/페이지네이션: `@/components/table/view`, `@/components/table/pagination`
- 라우팅: `next/link`, `next/navigation`
- 테이블: `@tanstack/react-table`

## 흐름 개요
- 목록: `/board/announce` (검색 + 리스트)
- 상세: `/board/announce/[id]`
- 등록: `/board/announce/create`
- 수정: `/board/announce/[id]/edit`  ← state 전환에서 라우팅 전환으로 변경

## 변경 요약 (Edit)
- 이전: 상세 화면 내에서 state 전환으로 Edit UI 토글.
- 이후: 별도 경로(`/board/announce/[id]/edit`)로 이동하여 수정 페이지 렌더.
- 이점: URL로 상태가 명확히 표현되고 새로고침/공유 시 일관성 유지.

## Hooks 기반 페이지 링크 보존
- `use-pagination-params`: 쿼리스트링의 `page`, `size`를 읽어 현재 페이지 정보를 획득.
- `use-pagination-router`: 현재 `page/size`를 유지한 채 `push/replace(base)`를 제공.
- `PaginationLink`: `href`에 자동으로 `?page=..&size=..`를 부착하는 Link 래퍼.

예시
- 목록 제목 링크: `<PaginationLink href={`/board/announce/${row.nttId}`} />`
- 등록 버튼: `<PaginationLink href="/board/announce/create" />`
- 상세의 수정 버튼: `<PaginationLink href={`/board/announce/${id}/edit`} />`
- 수정 화면 취소: `usePaginationRouter().push(`/board/announce/${id}`)`

## 목록(List)
- 파일: `announce/page.tsx`
  - 검색 상태를 관리하는 공통 `SearchForm` 포함.
  - 기본 검색 필드: 제목(`nttSj`), 작성자(`registerId`) 등.
- 파일: `announce/_components/tables/list.tsx`
  - 쿼리: `client.notice.list({ pageIndex, pageSize, ...filters })`
  - 키: `["magp/notice", { ...filters, pageIndex, pageSize }]`
  - 컬럼: 제목 링크는 `PaginationLink`로 상세로 이동, 등록 버튼도 동일 방식으로 페이지 파라미터 보존.
  - 페이지/사이즈는 내부 state로 제어하되 URL 초기값을 반영.

## 상세(Detail)
- 파일: `announce/[id]/page.tsx`
  - App Router에서 `params`를 비동기로 받아 `id` 추출.
- 파일: `announce/_components/tables/detail.tsx`
  - 조회: `client.notice.select({ nttId })` 후 `View` 렌더.
  - 수정: `PaginationLink`로 `/board/announce/{id}/edit` 네비게이션.
  - 삭제: `client.notice.delete({ nttId, updusrId })` 성공 시 캐시 무효화 후 목록으로 이동.
  - 목록으로: `PaginationLink`로 `/board/announce` 복귀(페이지 파라미터 유지).

## 수정(Edit)
- 파일: `announce/[id]/edit/page.tsx`
  - `useParams`, `useSearchParams`, `usePaginationRouter`로 라우팅/파라미터 처리.
  - 저장: `client.notice.update(payload)` 성공 시 상세로 복귀(`router.push(/board/announce/{id}?page..)`), 캐시 무효화 함께 수행.
  - 취소: `usePaginationRouter().push(/board/announce/{id})`로 현재 페이지 컨텍스트 유지.
  - UI: `tables/edit.tsx` 폼을 사용.

## 등록(Create)
- 파일: `announce/create/page.tsx`
  - 엔트리에서 `Create` 컴포넌트 렌더.
- 파일: `announce/_components/tables/create.tsx`
  - 기본 폼 상태 구성(제목/내용/팝업 기간 등).
  - 등록: `client.notice.insert({...})` → 성공 시 캐시 무효화 후 목록으로 이동(`?page&size` 유지).

## 검색(SearchForm) 패턴
- 파일: `_components/search-form.tsx`
  - 옵션 기반으로 검색 필드 정의 및 바인딩.
  - 상위에서 검색 params를 관리하고 기본 필드(`defaultField`) 지정.

## Client/Server, 파라미터
- 클라이언트 컴포넌트 최상단에 `"use client"` 선언 필요.
- App Router에서 `params`, `searchParams`는 비동기/동기 혼용에 유의(`await params`).

## 쿼리 키와 캐시 전략
- 네임스페이스: `"magp/notice"` 고정 키 사용.
- 파라미터 객체를 키에 포함해 페이지/필터별 캐시 분리.
- 변이(등록/수정/삭제) 시 목록/상세 키 무효화로 동기화.

## 확장 가이드(새 게시판)
1) `app/board/{name}` 생성 후 `page.tsx`에서 검색+목록 구성
2) `_components/tables/list.tsx`에서 조회/컬럼/링크 구현 → `/{name}/{id}` 링크는 `PaginationLink` 사용
3) `[id]/page.tsx`에서 `await params`로 id 전달, `detail.tsx` 구현
4) 필요 시 `create/page.tsx`, `tables/create.tsx` 추가 + 수정은 라우팅(`/[id]/edit`) 권장
5) 쿼리/모델은 API 스펙에 맞춰 구현

## TODO
- 하드코드 사용자정보(`admin`)는 인증 토큰/스토어 연동으로 교체
- 폼 검증/에러 처리 강화, 날짜/시간 옵션 고도화, 다국어(i18n) 점진 개선

## 변경이력
- 2025-09-10: Edit를 state 전환에서 라우팅(`/[id]/edit`)으로 변경, 페이지 링크는 hooks(`PaginationLink`/`usePaginationRouter`)로 일원화
- 2025-09-09: Announce 등록(create) 추가 및 가이드 반영

