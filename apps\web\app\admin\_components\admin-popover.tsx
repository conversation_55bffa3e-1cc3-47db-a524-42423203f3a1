"use client";

import { But<PERSON> } from "@geon-ui/react/primitives/button";
import { Checkbox } from "@geon-ui/react/primitives/checkbox";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@geon-ui/react/primitives/popover";
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useMemo, useState } from "react";

export type PermRow = { name: string };

type AdminPopoverProps = {
  trigger?: React.ReactNode;
  items: PermRow[];
  defaultSelected?: string[];
  onAdd?: (selected: string[]) => void;
  side?: "top" | "right" | "bottom" | "left";
  align?: "start" | "center" | "end";
  sideOffset?: number;
  alignOffset?: number;
  title?: string;
};

const permColumnHelper = createColumnHelper<PermRow>();

export function AdminPopover({
  trigger,
  items,
  defaultSelected = [],
  onAdd,
  side = "right",
  align = "end",
  sideOffset = 12,
  alignOffset = 100,
  title,
}: AdminPopoverProps) {
  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState<Set<string>>(
    new Set(defaultSelected),
  );

  const data = items;
  const columns = useMemo(() => {
    // Hook Dependency Warning 해결
    const toggle = (name: string, checked: boolean | "indeterminate") => {
      const next = new Set(selected);
      if (checked === true) next.add(name);
      else next.delete(name);
      setSelected(next);
    };

    return [
      permColumnHelper.accessor("name", {
        header: () => null,
        cell: (info) => (
          <div className="py-2 pr-2 text-left">{info.getValue()}</div>
        ),
      }),
      permColumnHelper.display({
        id: "checked",
        header: () => null,
        cell: (info) => {
          const name = info.row.original.name;
          return (
            <div className="py-2">
              <Checkbox
                checked={selected.has(name)}
                onCheckedChange={(v) => toggle(name, v)}
              />
            </div>
          );
        },
      }),
    ];
  }, [selected]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  const handleAdd = () => {
    onAdd?.(Array.from(selected));
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>{trigger}</PopoverTrigger>
      <PopoverContent
        side={side}
        align={align}
        sideOffset={sideOffset}
        alignOffset={alignOffset}
        className="w-[280px]"
      >
        <div className="mb-3 text-center font-medium">{title}</div>
        <div className="max-h-64 overflow-auto rounded border">
          <table className="w-full table-fixed border-collapse">
            <thead className="text-sm">
              {table.getHeaderGroups().map((hg) => (
                <tr key={hg.id}>
                  {hg.headers.map((header) => (
                    <th key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody>
              {table.getRowModel().rows.map((row) => (
                <tr key={row.id}>
                  {row.getVisibleCells().map((cell) => (
                    <td key={cell.id} className="px-2">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="mt-3 flex justify-end gap-2">
          <Button type="button" variant="ghost" onClick={() => setOpen(false)}>
            취소
          </Button>
          <Button type="button" variant="secondary" onClick={handleAdd}>
            추가
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
