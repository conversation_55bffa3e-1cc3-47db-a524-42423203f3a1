"use client";
import { useFeature, useMap } from "@geon-map/react-odf";
import {
  AdministBaseRequest,
  AdministCtpvRequest,
  AdministEmdRequest,
  AdministLiRequest,
  AdministSggRequest,
} from "@geon-query/model";
import { useAppMutation } from "@geon-query/react-query";
import { useCallback, useMemo, useRef, useState } from "react";

import { PARENT_REGION_MAP, REGION_HIERARCHY } from "../constants";
import {
  AdministApiRequestType,
  ApiType,
  RegionChangeParams,
  RegionData,
  RegionInfo,
  RegionItem,
  RegionListData,
  RegionSelectParams,
  RegionType,
  UseRegionSelectorOptions,
  UseRegionSelectorReturn,
  WKTPolygonType,
} from "../types/region-selector-types";
import { RegionSelectorWidget as RegionSelectorConverter } from "../utils";
import { useHighlightLayer } from "./use-highlight-layer";

/**
 * 🏛️ 행정구역 선택 훅
 * - 시도/시군구/읍면동/리 계층적 선택 관리
 * - 지도 표시 및 API 연동
 * - 위젯 컴포넌트와 연동
 */
export function useRegionSelector<T extends ApiType = "geon">(
  options: UseRegionSelectorOptions<T> = {} as UseRegionSelectorOptions<T>,
): UseRegionSelectorReturn {
  const { apiClient, srid = "5186" } = options;

  const highlightStyle = useMemo(
    () => ({
      "fill-color": [220, 60, 34, 0],
      "stroke-color": [255, 104, 68],
      "stroke-width": 5,
    }),
    [],
  );

  // 지도 관련 훅들
  const { clearHighlight, highlight } = useHighlightLayer({
    style: highlightStyle,
  });
  const { fromWKT } = useFeature();
  const { isLoading } = useMap();

  // 상태 관리
  const [selectedRegion, setSelectedRegion] = useState<RegionData>({
    sido: null,
    sigungu: null,
    eupmyeondong: null,
    li: null,
  });

  const [regionLists, setRegionLists] = useState<RegionListData>({
    sido: [],
    sigungu: [],
    eupmyeondong: [],
    li: [],
  });

  // API 뮤테이션 훅
  const { selectMutation, coordMutation } = useApiMutations(apiClient, srid);

  const clearHighlightRef = useRef(clearHighlight);
  clearHighlightRef.current = clearHighlight;
  // 지도 기능 (WKT 표시, 하이라이트)
  const mapFeatures = useMapFeatures({
    clearHighlight,
    fromWKT,
    highlight,
    srid,
  });

  // ✅ 통합된 액션 객체
  const regionActions = useMemo(
    () => ({
      // 특정 지역 선택
      selectRegion: (regionType: RegionType, code: string) => {
        setSelectedRegion((prev) => ({ ...prev, [regionType]: code }));
      },

      // 하위 지역들 초기화
      clearChildRegions: (regionTypes: RegionType[]) => {
        const clearedFields = regionTypes.reduce((acc, type) => {
          acc[type] = null;
          return acc;
        }, {} as Partial<RegionData>);
        setSelectedRegion((prev) => ({ ...prev, ...clearedFields }));
      },

      // 전체 지역 초기화
      resetAllRegions: () => {
        setSelectedRegion({
          sido: null,
          sigungu: null,
          eupmyeondong: null,
          li: null,
        });
      },

      // 지역 리스트 업데이트
      updateRegionList: (regionType: RegionType, list: RegionInfo[]) => {
        setRegionLists((prev) => ({ ...prev, [regionType]: list }));
      },

      // 여러 지역 리스트 한번에 업데이트
      updateMultipleRegionLists: (updates: Partial<RegionListData>) => {
        setRegionLists((prev) => ({ ...prev, ...updates }));
      },

      // 특정 지역 리스트 초기화
      clearRegionList: (regionTypes: RegionType[]) => {
        const clearedLists = regionTypes.reduce((acc, type) => {
          acc[type] = [];
          return acc;
        }, {} as Partial<RegionListData>);
        setRegionLists((prev) => ({ ...prev, ...clearedLists }));
      },

      // 전체 초기화 (초기 로딩용)
      initialize: (
        regionData: RegionData | null,
        listData: RegionListData,
        useLi: boolean = false,
      ) => {
        // null 체크
        if (!regionData) {
          console.error("현재 위치 지역코드가 없습니다");
          return;
        }

        // 지역 데이터 설정
        setSelectedRegion(regionData);
        // useLi 필터링 적용
        const filteredRegionLists = { ...listData };
        if (!useLi) {
          filteredRegionLists.li = [];
        }
        setRegionLists(filteredRegionLists);
      },

      // 🗺️ 지도 관련 액션들
      showRegionOnMap: (wktPolygon: WKTPolygonType) => {
        mapFeatures.addRegionFeature(wktPolygon);
      },

      showRegionFromData: (regionData: RegionItem) => {
        mapFeatures.addRegionFeatureFromData(regionData);
      },

      clearMapHighlight: () => {
        clearHighlightRef.current();
      },

      // 🎯 복합 액션들
      selectAndShowRegion: (
        regionType: RegionType,
        code: string,
        regionData: RegionItem,
      ) => {
        // 상태 업데이트
        setSelectedRegion((prev) => ({ ...prev, [regionType]: code }));
        // 지도에 표시
        mapFeatures.addRegionFeatureFromData(regionData);
      },

      resetAllAndClearMap: () => {
        // 모든 상태 초기화
        setSelectedRegion({
          sido: null,
          sigungu: null,
          eupmyeondong: null,
          li: null,
        });
        setRegionLists({
          sido: [],
          sigungu: [],
          eupmyeondong: [],
          li: [],
        });
        // 지도 하이라이트 지우기
        clearHighlight();
      },
    }),
    [clearHighlight, mapFeatures, setSelectedRegion, setRegionLists],
  );

  // API 파라미터 생성 유틸리티
  const createSelectParams = useCallback(
    (options: RegionSelectParams): AdministApiRequestType => {
      const { regionType, regionCode, srid } = options;
      const baseParams: AdministBaseRequest = {
        retGeom: true,
        targetSrid: srid,
      };

      const paramMap = {
        sido: () =>
          ({ ...baseParams, ctprvnCd: regionCode }) as AdministCtpvRequest,
        sigungu: () =>
          ({ ...baseParams, sigCd: regionCode }) as AdministSggRequest,
        eupmyeondong: () =>
          ({ ...baseParams, emdCd: regionCode }) as AdministEmdRequest,
        li: () => ({ ...baseParams, liCd: regionCode }) as AdministLiRequest,
      };

      return paramMap[regionType]?.() || baseParams;
    },
    [],
  );

  // API 핸들러 (지역 검색, 리스트 조회, 좌표 검색)
  const apiHandlers = useApiHandlers({
    apiClient,
    srid,
    selectMutation,
    coordMutation,
    createSelectParams,
  });

  // 지역 상태 관리 기능
  const regionStateHandlers = useRegionStateHandlers({
    setSelectedRegion,
    mapFeatures,
  });

  // 메인 지역 변경 처리 함수
  const handleRegionChange = useCallback(
    async (params: RegionChangeParams) => {
      const { regionCode, regionName, wktPolygon, regionType } = params;

      // 1. 선택된 지역 상태 업데이트
      regionStateHandlers.updateSelectedRegion(regionType, regionCode);

      // 2. 지도에 지역 표시
      regionStateHandlers.displayRegionOnMap(
        regionCode,
        regionName,
        wktPolygon,
      );

      // 3. 지역 계층 구조 설정 가져오기 및 하위 지역 초기화
      const hierarchyConfig = REGION_HIERARCHY[regionType];
      regionStateHandlers.resetChildRegions(hierarchyConfig.reset);

      // 4. 다음 지역 정보 반환
      return hierarchyConfig;
    },
    [regionStateHandlers],
  );

  // 유틸리티 함수들
  const getParentRegionType = useCallback(
    (regionType: RegionType): RegionType | undefined => {
      return PARENT_REGION_MAP[regionType];
    },
    [],
  );

  return {
    // 상태
    regionLists,
    selectedRegion,
    isLoading: selectMutation.isPending || coordMutation.isPending || isLoading,
    error:
      (selectMutation.error as Error) || (coordMutation.error as Error) || null,

    // ✅ 통합된 액션 기반 인터페이스
    actions: regionActions,

    // 핸들러
    handleRegionSelect: apiHandlers.handleRegionSelect,
    handleRegionList: apiHandlers.handleRegionList,
    handlePnuSelect: apiHandlers.handlePnuSelect,
    handleRegionChange,

    // 유틸리티
    getParentRegionType,
  };
}

/**
 * API 뮤테이션 관리 헬퍼 훅
 * - 지역 선택 API 호출
 * - 좌표 기반 지역 검색
 */
function useApiMutations(apiClient: any, srid: string) {
  // 지역 선택 뮤테이션 (시도/시군구/읍면동/리)
  const selectMutation = useAppMutation({
    mutationFn: async ({
      regionType,
      apiParams,
    }: {
      regionType: RegionType;
      apiParams: AdministApiRequestType;
    }) => {
      if (!apiClient) {
        throw new Error("API 클라이언트가 필요합니다.");
      }

      const apiMap = {
        sido: (params: AdministCtpvRequest) => apiClient.administ.ctpv(params),
        sigungu: (params: AdministSggRequest) => apiClient.administ.sgg(params),
        eupmyeondong: (params: AdministEmdRequest) =>
          apiClient.administ.emd(params),
        li: (params: AdministLiRequest) => apiClient.administ.li(params),
      };

      return apiMap[regionType](apiParams as any);
    },
  });

  // 좌표 기반 지역 검색 뮤테이션
  const coordMutation = useAppMutation({
    mutationFn: async (lonLat: [number, number]) => {
      if (!apiClient) {
        throw new Error("API 클라이언트가 필요합니다.");
      }

      const point = lonLat;

      return apiClient.address.coord({
        lng: point[0],
        lat: point[1],
        showMultipleResults: false,
        targetSrid: srid,
      });
    },
  });

  return { selectMutation, coordMutation };
}

/**
 * API 핸들러 관리 헬퍼 훅
 * - 지역 선택, 리스트 조회, 좌표 검색 핸들러
 */
function useApiHandlers({
  apiClient,
  srid,
  selectMutation,
  coordMutation,
  createSelectParams,
}: {
  apiClient: any;
  srid: string;
  selectMutation: any;
  coordMutation: any;
  createSelectParams: (options: RegionSelectParams) => AdministApiRequestType;
}) {
  // 특정 지역 정보 조회 (WKT 포함)
  const handleRegionSelect = useCallback(
    async (
      regionType: RegionType,
      regionCode: string,
    ): Promise<RegionItem | null> => {
      try {
        const params = createSelectParams({ regionCode, regionType, srid });
        const result = await selectMutation.mutateAsync({
          regionType,
          apiParams: params,
        });
        return RegionSelectorConverter.item(result) || null;
      } catch (error) {
        console.error("행정구역 검색 오류:", error);
        return null;
      }
    },
    [createSelectParams, selectMutation, srid],
  );

  // 지역 리스트 조회 (드롭다운용)
  const handleRegionList = useCallback(
    async (
      regionType: RegionType,
      parentCode?: string | null,
    ): Promise<RegionInfo[] | null> => {
      try {
        if (!apiClient) {
          throw new Error("API 클라이언트가 필요합니다.");
        }

        const baseParams: AdministBaseRequest = {
          retGeom: false,
          targetSrid: srid,
        };

        const result = await getRegionListByType(
          regionType,
          parentCode,
          apiClient,
          baseParams,
        );
        return RegionSelectorConverter.list(result) || null;
      } catch (error) {
        console.error("행정구역 리스트 조회 오류:", error);
        return null;
      }
    },
    [apiClient, srid],
  );

  // 좌표로 지역 검색 (지도 클릭, PNU 검색)
  const handlePnuSelect = useCallback(
    async (lonLat: [number, number]): Promise<RegionData | null> => {
      try {
        const result = await coordMutation.mutateAsync(lonLat);
        return RegionSelectorConverter.regionCodesFromCoord(result) || null;
      } catch (error) {
        console.error("좌표 검색 오류:", error);
        return null;
      }
    },
    [coordMutation],
  );

  return {
    handleRegionSelect,
    handleRegionList,
    handlePnuSelect,
  };
}

/**
 * 지도 기능 관리 헬퍼 훅
 * - WKT 폴리곤 표시
 * - 지도 하이라이트 관리
 */
function useMapFeatures({
  clearHighlight,
  fromWKT,
  highlight,
  srid,
}: {
  clearHighlight: () => void;
  fromWKT: (wkt: string) => any;
  highlight: (feature: any, options: any) => void;
  srid: string;
}) {
  // WKT 폴리곤을 지도에 표시
  const addRegionFeature = useCallback(
    (wktPolygon: WKTPolygonType) => {
      clearHighlight();
      const feature = fromWKT(wktPolygon);
      if (feature) {
        highlight(feature, { srid, isFitToLayer: true });
      }
    },
    [clearHighlight, fromWKT, highlight, srid],
  );

  // 지역 데이터에서 WKT 추출해서 지도에 표시
  const addRegionFeatureFromData = useCallback(
    (regionData: RegionItem) => {
      if (regionData.wktPolygon) {
        addRegionFeature(regionData.wktPolygon);
      } else {
        console.warn("지도에 표시할 WKT 지오메트리가 없습니다.");
      }
    },
    [addRegionFeature],
  );

  return {
    addRegionFeature,
    addRegionFeatureFromData,
  };
}

/**
 * 지역 상태 관리 헬퍼 훅
 * - 선택된 지역 상태 업데이트
 * - 하위 지역 초기화
 * - 지도 표시 연동
 */
function useRegionStateHandlers({
  setSelectedRegion,
  mapFeatures,
}: {
  setSelectedRegion: React.Dispatch<React.SetStateAction<RegionData>>;
  mapFeatures: {
    addRegionFeatureFromData: (regionData: RegionItem) => void;
  };
}) {
  // 선택된 지역 상태 업데이트
  const updateSelectedRegion = useCallback(
    (regionType: RegionType, regionCode: string) => {
      setSelectedRegion((prev) => ({ ...prev, [regionType]: regionCode }));
    },
    [setSelectedRegion],
  );

  // 지도에 지역 표시
  const displayRegionOnMap = useCallback(
    (regionCode: string, regionName: string, wktPolygon: WKTPolygonType) => {
      mapFeatures.addRegionFeatureFromData({
        code: regionCode,
        name: regionName,
        wktPolygon,
      });
    },
    [mapFeatures],
  );

  // 하위 지역들 초기화 (상위 지역 변경시)
  const resetChildRegions = useCallback(
    (regionTypes: RegionType[] | null) => {
      if (regionTypes) {
        const resetFields = regionTypes.reduce((acc, regionType) => {
          acc[regionType] = null;
          return acc;
        }, {} as Partial<RegionData>);

        setSelectedRegion((prev) => ({ ...prev, ...resetFields }));
      }
    },
    [setSelectedRegion],
  );

  return {
    updateSelectedRegion,
    displayRegionOnMap,
    resetChildRegions,
  };
}

/**
 * 부모코드로 지역 리스트 조회하는 유틸리티 함수
 * - 시도: 전체 목록
 * - 시군구: 시도코드 필요
 * - 읍면동: 시군구코드 필요
 * - 리: 읍면동코드 필요
 */
async function getRegionListByType(
  regionType: RegionType,
  parentCode: string | null | undefined,
  apiClient: any,
  baseParams: AdministBaseRequest,
) {
  const apiMap = {
    sido: () => apiClient.administ.ctpvList(baseParams),
    sigungu: () => {
      if (!parentCode) throw new Error("시군구 조회시 시도코드가 필요합니다.");
      return apiClient.administ.sggList({
        ...baseParams,
        ctprvnCd: parentCode,
      });
    },
    eupmyeondong: () => {
      if (!parentCode)
        throw new Error("읍면동 조회시 시군구코드가 필요합니다.");
      return apiClient.administ.emdList({ ...baseParams, sigCd: parentCode });
    },
    li: () => {
      if (!parentCode) throw new Error("리 조회시 읍면동코드가 필요합니다.");
      return apiClient.administ.liList({ ...baseParams, emdCd: parentCode });
    },
  };

  return apiMap[regionType]();
}
