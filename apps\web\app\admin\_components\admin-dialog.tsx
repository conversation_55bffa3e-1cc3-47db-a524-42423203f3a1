"use client";

import { But<PERSON> } from "@geon-ui/react/primitives/button";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@geon-ui/react/primitives/dialog";
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ReactNode, useMemo } from "react";

export type InfoRow = { label: string; value: ReactNode };

type AdminDialogProps = {
  title?: string;
  rows: InfoRow[];
  trigger?: ReactNode;
  onSubmit?: () => void;
  buttonTitle?: string;
};

const columnHelper = createColumnHelper<InfoRow>();

export function AdminDialog({
  title,
  rows,
  trigger = <Button>Open</Button>,
  onSubmit,
  buttonTitle,
}: AdminDialogProps) {
  const columns = useMemo(
    () => [
      columnHelper.accessor("label", {
        header: () => null,
        cell: (info) => (
          <div className="whitespace-nowrap bg-gray-100 px-4 py-2 text-left font-medium">
            {info.getValue()}
          </div>
        ),
      }),
      columnHelper.accessor("value", {
        header: () => null,
        cell: (info) => <div className="px-4 py-2">{info.getValue()}</div>,
      }),
    ],
    [],
  );

  const table = useReactTable({
    data: rows,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <Dialog>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="max-w-xl">
        <DialogHeader>
          <DialogTitle className="mb-6 text-center">{title}</DialogTitle>
        </DialogHeader>

        <div className="flex justify-center">
          <table className="w-auto border-collapse">
            <thead className="hidden">
              {table.getHeaderGroups().map((hg) => (
                <tr key={hg.id}>
                  {hg.headers.map((header) => (
                    <th key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody>
              {table.getRowModel().rows.map((row) => (
                <tr key={row.id}>
                  {row.getVisibleCells().map((cell, idx) => (
                    <td key={cell.id} className={idx === 0 ? "align-top" : ""}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="mt-3 flex justify-end">
          <Button type="button" variant="secondary" onClick={onSubmit}>
            {buttonTitle}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
