export type CustomMap = {
  department: string;
  groups: {
    /** 공유 그룹 구분 코드(전체, 부서, 개인) */
    code: string;
    /** 공유 그룹 별 아이템 */
    items: { id: string; title: string }[];
  }[];
};

export const MAPS: CustomMap[] = [
  {
    department: "민원지적과",
    groups: [
      {
        code: "전체",
        items: [
          { id: "test-1", title: "맞춤형 지도 1" },
          { id: "test-2", title: "맞춤형 지도 2" },
        ],
      },
      {
        code: "부서",
        items: [
          { id: "test-3", title: "맞춤형 지도 3" },
          { id: "test-4", title: "맞춤형 지도 4" },
        ],
      },
      {
        code: "개인",
        items: [
          { id: "test-5", title: "맞춤형 지도 5" },
          { id: "test-6", title: "맞춤형 지도 6" },
        ],
      },
    ],
  },
  {
    department: "건축과",
    groups: [
      {
        code: "전체",
        items: [
          { id: "test-7", title: "맞춤형 지도 7" },
          { id: "test-8", title: "맞춤형 지도 8" },
        ],
      },
      {
        code: "부서",
        items: [
          { id: "test-9", title: "맞춤형 지도 9" },
          { id: "test-10", title: "맞춤형 지도 10" },
        ],
      },
      {
        code: "개인",
        items: [
          { id: "test-11", title: "맞춤형 지도 11" },
          { id: "test-12", title: "맞춤형 지도 12" },
        ],
      },
    ],
  },
];
