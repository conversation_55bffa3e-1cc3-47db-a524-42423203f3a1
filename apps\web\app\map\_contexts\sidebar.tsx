"use client";

import { SidebarProvider, useSidebar } from "@geon-ui/react/primitives/sidebar";
import type { ReactNode } from "react";
import { createContext, useContext, useMemo, useState } from "react";

type MapSidebarContextProps = {
  // outer sidebar state
  outer: ReturnType<typeof useSidebar>;

  // inner sidebar state
  innerOpen: boolean;
  setInnerOpen: (open: boolean) => void;

  // navigation state
  active: string | null;
  setActive: (menu: string | null) => void;
};

const MapSidebarContext = createContext<MapSidebarContextProps | null>(null);

export function useMapSidebar() {
  const context = useContext(MapSidebarContext);
  if (!context) {
    throw new Error("useMapSidebar must be used within a MapSidebarProvider");
  }
  return context;
}

export function MapInnerSidebarProvider({ children }: { children: ReactNode }) {
  const { innerOpen, setInnerOpen } = useMapSidebar();

  return (
    <SidebarProvider open={innerOpen} onOpenChange={setInnerOpen}>
      {children}
    </SidebarProvider>
  );
}

export function MapSidebarProvider({ children }: { children: ReactNode }) {
  const outer = useSidebar();
  const [innerOpen, setInnerOpen] = useState(false);
  const [active, setActive] = useState<string | null>(null);

  const contextValue = useMemo(
    () => ({
      outer,
      innerOpen,
      setInnerOpen,
      active,
      setActive,
    }),
    [outer, innerOpen, setInnerOpen, active, setActive],
  );

  return (
    <MapSidebarContext.Provider value={contextValue}>
      {children}
    </MapSidebarContext.Provider>
  );
}
