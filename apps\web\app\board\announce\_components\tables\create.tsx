"use client";

import { createGeonMagpClient } from "@geon-query/model";
import { useAppMutation, useAppQueryClient } from "@geon-query/react-query";
import { useRouter } from "next/navigation";
import React from "react";

import { usePaginationRouter } from "@/app/board/_components/hooks/use-pagination-router";

import Edit from "./edit";
import { type FormState } from "./view";

//TODO 아이디 store에서 호출
const userId = "admin";
export default function Create() {
  const client = createGeonMagpClient();
  const router = useRouter();
  // 등록 초기값
  const initialForm: FormState = {
    nttSj: "",
    nttCn: "",
    updusrId: userId,
    registerId: userId,
    upperExpsrAt: false,
    smsSndngAt: false,
    othbcAt: true,
    pstgBeginDt: "",
    linkUrl: "",
    popupAt: false,
    popupBeginDt: "",
    popupEndDt: "",
    popupPortalExpsrAt: false,
    popupInsttExpsrAt: false,
  };

  const [form, setForm] = React.useState<FormState>(initialForm);
  const qc = useAppQueryClient(); // ✅ 래핑된 훅 사용
  const insertMutation = useAppMutation({
    mutationFn: async () =>
      client.notice.insert({
        nttSj: form.nttSj,
        nttCn: form.nttCn,
        registerId: form.registerId,
        updusrId: userId,
        popupBeginDt: form.popupBeginDt
          ? new Date(form.popupBeginDt).toISOString()
          : null,
        popupEndDt: form.popupEndDt
          ? new Date(form.popupEndDt).toISOString()
          : null,
      }),
    onSuccess: () => {
      alert("등록이 완료되었습니다.");
      qc.invalidateQueries({ queryKey: ["magp/notice"] });
      router.push(`/board/announce`);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    insertMutation.mutate();
  };
  const { push } = usePaginationRouter();

  return (
    <div className="mx-auto w-full max-w-5xl space-y-6 p-4">
      <h1 className="text-xl font-semibold">공지 등록</h1>
      <Edit
        form={form}
        setForm={setForm}
        onSubmit={handleSubmit}
        isPending={insertMutation.isPending}
        onCancel={() => push(`/board/announce`)}
      />
    </div>
  );
}
