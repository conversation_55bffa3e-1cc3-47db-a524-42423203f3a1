"use client";

import { AddressSearchWidget } from "@geon-map/react-ui/components";
import { useAddressSearch } from "@geon-map/react-ui/hooks";
interface AddressSearchWidgetServiceProps {
  className?: string;
}
// 🔧 API 클라이언트 설정 방법 (둘 중 하나 선택)
// ✅ 방법 1: 기본 설정 사용 (geonAPI.ts의 BASE_URL, crtfckey 사용)
// import { defaultGeonAddrgeoClient } from "@geon-query/model";

// ✅ 방법 2: 커스텀 설정 사용 (직접 baseUrl, crtfckey 지정)
import { createGeonAddrgeoClient } from "@geon-query/model";

export default function AddressSearchWidgetService({
  className = "",
}: AddressSearchWidgetServiceProps) {
  //const apiClient = defaultGeonAddrgeoClient;
  const apiClient = createGeonAddrgeoClient({
    baseUrl: "https://city.geon.kr/api/",
    crtfckey: "UxizIdSqCePz93ViFt8ghZFFJuOzvUp0",
  });

  const { handleAddressSearch } = useAddressSearch({
    apiType: "geon",
    apiClient,
  });

  return (
    <AddressSearchWidget
      onSearch={handleAddressSearch}
      onLoadMore={handleAddressSearch}
      className={className}
      //visibleFields={{ poiName: false }}
    />
  );
}
