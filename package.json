{"name": "my-turbo", "private": true, "type": "module", "scripts": {"build": "turbo run build", "dev": "pnpm clean:build && turbo run dev", "dev:prod": "turbo run dev:prod", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "changeset": "changeset", "check-types": "turbo run check-types", "prepare": "husky", "web": "pnpm --filter web", "storybook": "pnpm --filter web storybook", "release": "turbo run build --filter=./packages/**/* && changeset publish", "clean": "rimraf node_modules/.cache .turbo && rimraf packages/**/dist && rimraf apps/**/.next", "clean:build": "rimraf --glob \"packages/*/*/dist\" \"packages/*/*/*/dist\" apps/*/.next\""}, "devDependencies": {"@changesets/cli": "^2.29.7", "cross-env": "^10.0.0", "husky": "^9.1.7", "lint-staged": "^16.1.6", "prettier": "^3.6.2", "rimraf": "^6.0.1", "turbo": "^2.5.6", "typescript": "^5.9.2"}, "packageManager": "pnpm@10.17.0+sha512.fce8a3dd29a4ed2ec566fb53efbb04d8c44a0f05bc6f24a73046910fb9c3ce7afa35a0980500668fa3573345bd644644fa98338fa168235c80f4aa17aa17fbef", "engines": {"node": ">=20"}, "dependencies": {"changesets-gitlab": "^0.13.4"}}