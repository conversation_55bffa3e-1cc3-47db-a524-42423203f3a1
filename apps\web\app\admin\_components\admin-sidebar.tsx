"use client";

import { cn } from "@geon-ui/react/lib/utils";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
} from "@geon-ui/react/primitives/sidebar";
import React from "react";

import HomeButton from "@/components/sidebar/home-button";

import NavAdminMenu from "./sidebar/nav-menu";

export default function AdminSidebar({
  className,
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar variant="sidebar" className={cn("z-10", className)} {...props}>
      <SidebarHeader>
        <HomeButton />
      </SidebarHeader>
      <SidebarContent>
        <NavAdminMenu />
      </SidebarContent>
      <SidebarFooter>Footer</SidebarFooter>
    </Sidebar>
  );
}
