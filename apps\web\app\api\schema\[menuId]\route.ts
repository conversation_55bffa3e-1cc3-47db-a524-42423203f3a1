import { NextRequest, NextResponse } from "next/server";

import type { ApiSchemaResponse } from "@/app/service/_components/dynamic-search/types";

// Mock 데이터 - 실제로는 데이터베이스나 외부 API에서 가져와야 함
const MOCK_SCHEMAS: Record<string, ApiSchemaResponse> = {
  analysis: {
    resource: "analysis",
    resourceName: "용도분석",
    fields: [
      {
        column: "gid",
        label: "도형번호",
        uiType: "number",
        search: { mode: "eq" },
      },
      {
        column: "name",
        label: "이름",
        uiType: "text",
        search: { mode: "like" },
      },
      {
        column: "divi",
        label: "구분",
        uiType: "code",
        code: "JMP001", // 공통 코드에 등록 필요
        search: { mode: "eq" },
      },
      {
        column: "updtDt",
        label: "수정일시",
        uiType: "date",
        search: { mode: "between" },
      },
      {
        column: "geom",
        label: "도형",
        uiType: "hidden",
        search: { mode: "none" },
      },
    ],
    grouped: {
      text: ["name"],
      number: ["gid"],
      date: ["updtDt"],
      code: ["divi"],
      hidden: ["geom"],
    },
    geometryColumns: [
      {
        columnName: "geom",
        geometryType: "MULTILINESTRING",
        srid: 5186,
      },
    ],
  },
  ocean: {
    resource: "ocean",
    resourceName: "해양 관리",
    fields: [
      {
        column: "gid",
        label: "도형번호",
        uiType: "number",
        search: { mode: "eq" },
      },
      {
        column: "name",
        label: "이름",
        uiType: "text",
        search: { mode: "like" },
      },
      {
        column: "divi",
        label: "구분",
        uiType: "code",
        code: "JMP001", // 공통 코드에 등록 필요
        search: { mode: "eq" },
      },
      {
        column: "updtDt",
        label: "수정일시",
        uiType: "date",
        search: { mode: "between" },
      },
      {
        column: "geom",
        label: "도형",
        uiType: "hidden",
        search: { mode: "none" },
      },
    ],
    grouped: {
      text: ["name"],
      number: ["gid"],
      date: ["updtDt"],
      code: ["divi"],
      hidden: ["geom"],
    },
    geometryColumns: [
      {
        columnName: "geom",
        geometryType: "MULTILINESTRING",
        srid: 5186,
      },
    ],
  },
  permit: {
    resource: "permit",
    resourceName: "인허가 관리",
    fields: [
      {
        column: "gid",
        label: "도형번호",
        uiType: "number",
        search: { mode: "eq" },
      },
      {
        column: "name",
        label: "이름",
        uiType: "text",
        search: { mode: "like" },
      },
      {
        column: "divi",
        label: "구분",
        uiType: "code",
        code: "JMP001", // 공통 코드에 등록 필요
        search: { mode: "eq" },
      },
      {
        column: "updtDt",
        label: "수정일시",
        uiType: "date",
        search: { mode: "between" },
      },
      {
        column: "geom",
        label: "도형",
        uiType: "hidden",
        search: { mode: "none" },
      },
    ],
    grouped: {
      text: ["name"],
      number: ["gid"],
      date: ["updtDt"],
      code: ["divi"],
      hidden: ["geom"],
    },
    geometryColumns: [
      {
        columnName: "geom",
        geometryType: "MULTILINESTRING",
        srid: 5186,
      },
    ],
  },
  sewage: {
    resource: "sewage",
    resourceName: "하수도 관리",
    fields: [
      {
        column: "gid",
        label: "도형번호",
        uiType: "number",
        search: { mode: "eq" },
      },
      {
        column: "name",
        label: "이름",
        uiType: "text",
        search: { mode: "like" },
      },
      {
        column: "divi",
        label: "구분",
        uiType: "code",
        code: "JMP001", // 공통 코드에 등록 필요
        search: { mode: "eq" },
      },
      {
        column: "updtDt",
        label: "수정일시",
        uiType: "date",
        search: { mode: "between" },
      },
      {
        column: "geom",
        label: "도형",
        uiType: "hidden",
        search: { mode: "none" },
      },
    ],
    grouped: {
      text: ["name"],
      number: ["gid"],
      date: ["updtDt"],
      code: ["divi"],
      hidden: ["geom"],
    },
    geometryColumns: [
      {
        columnName: "geom",
        geometryType: "MULTILINESTRING",
        srid: 5186,
      },
    ],
  },

  water: {
    resource: "water",
    resourceName: "상수도 공통 인터페이스",
    fields: [
      {
        column: "gid",
        label: "도형번호",
        uiType: "number",
        search: { mode: "eq" },
      },
      {
        column: "sourceTableComment",
        label: "시설물명",
        uiType: "text",
        search: { mode: "like" },
      },
      {
        column: "ftrCde",
        label: "시설물 코드",
        uiType: "code",
        search: { mode: "eq" },
      },
      {
        column: "ftrIdn",
        label: "시설물 관리번호",
        uiType: "number",
        search: { mode: "eq" },
      },
      {
        column: "hjdCde",
        label: "행정동 코드",
        uiType: "code",
        search: { mode: "eq" },
      },
      {
        column: "shtNum",
        label: "도엽번호",
        uiType: "text",
        search: { mode: "like" },
      },
      {
        column: "mngCde",
        label: "관리기관 코드",
        uiType: "code",
        search: { mode: "eq" },
      },
      {
        column: "istYmd",
        label: "설치일자",
        uiType: "date",
        search: { mode: "between" },
      },
      {
        column: "cntNum",
        label: "공사번호",
        uiType: "text",
        search: { mode: "like" },
      },
      {
        column: "sysChk",
        label: "시스템 여부",
        uiType: "code",
        search: { mode: "eq" },
      },
      {
        column: "bjdCde",
        label: "법정동 코드",
        uiType: "code",
        search: { mode: "eq" },
      },
      {
        column: "sourceTable",
        label: "테이블 원본명",
        uiType: "text",
        search: { mode: "like" },
      },
      {
        column: "geom",
        label: "도형",
        uiType: "hidden",
        search: { mode: "none" },
      },
    ],
    grouped: {
      number: ["gid", "ftrIdn"],
      code: ["ftrCde", "hjdCde", "mngCde", "sysChk", "bjdCde"],
      text: ["shtNum", "cntNum", "sourceTable", "sourceTableComment"],
      date: ["istYmd"],
      hidden: ["geom"],
    },
    searchField: ["ftrCde", "ftrIdn", "shtNum", "mngCde", "istYmd", "cntNum"],
    geometryColumns: [
      {
        columnName: "geom",
        geometryType: "POINT|MULTILINESTRING",
        srid: 5186,
      },
    ],
  },
  road: {
    resource: "road",
    resourceName: "도로 관리",
    fields: [
      {
        column: "gid",
        label: "도형번호",
        uiType: "number",
        search: { mode: "eq" },
      },
      {
        column: "name",
        label: "이름",
        uiType: "text",
        search: { mode: "like" },
      },
      {
        column: "divi",
        label: "구분",
        uiType: "code",
        code: "JMP001", // 공통 코드에 등록 필요
        search: { mode: "eq" },
      },
      {
        column: "updtDt",
        label: "수정일시",
        uiType: "date",
        search: { mode: "between" },
      },
      {
        column: "geom",
        label: "도형",
        uiType: "hidden",
        search: { mode: "none" },
      },
    ],
    grouped: {
      text: ["name"],
      number: ["gid"],
      date: ["updtDt"],
      code: ["divi"],
      hidden: ["geom"],
    },
    geometryColumns: [
      {
        columnName: "geom",
        geometryType: "MULTILINESTRING",
        srid: 5186,
      },
    ],
  },

  facility: {
    resource: "facility",
    resourceName: "시설물 관리",
    fields: [
      {
        column: "id",
        label: "시설물ID",
        uiType: "number",
        search: { mode: "eq" },
      },
      {
        column: "name",
        label: "시설물명",
        uiType: "text",
        search: { mode: "like" },
      },
      {
        column: "type",
        label: "시설유형",
        uiType: "code",
        code: "FACILITY_TYPE",
        search: { mode: "eq" },
      },
      {
        column: "status",
        label: "상태",
        uiType: "code",
        code: "FACILITY_STATUS",
        search: { mode: "eq" },
      },
      {
        column: "installDate",
        label: "설치일자",
        uiType: "date",
        search: { mode: "between" },
      },
      {
        column: "location",
        label: "위치",
        uiType: "text",
        search: { mode: "like" },
      },
      {
        column: "geom",
        label: "공간정보",
        uiType: "hidden",
        search: { mode: "none" },
      },
    ],
    grouped: {
      text: ["name", "location"],
      number: ["id"],
      date: ["installDate"],
      code: ["type", "status"],
      hidden: ["geom"],
    },
    geometryColumns: [
      {
        columnName: "geom",
        geometryType: "POINT",
        srid: 5186,
      },
    ],
  },

  park: {
    resource: "park",
    resourceName: "공원시설 관리",
    fields: [
      {
        column: "parkId",
        label: "공원ID",
        uiType: "number",
        search: { mode: "eq" },
      },
      {
        column: "parkName",
        label: "공원명",
        uiType: "text",
        search: { mode: "like" },
      },
      {
        column: "parkType",
        label: "공원유형",
        uiType: "code",
        code: "PARK_TYPE",
        search: { mode: "eq" },
      },
      {
        column: "area",
        label: "면적",
        uiType: "number",
        search: { mode: "between" },
      },
      {
        column: "openDate",
        label: "개방일자",
        uiType: "date",
        search: { mode: "between" },
      },
      {
        column: "address",
        label: "주소",
        uiType: "text",
        search: { mode: "like" },
      },
      {
        column: "geom",
        label: "공간정보",
        uiType: "hidden",
        search: { mode: "none" },
      },
    ],
    grouped: {
      text: ["parkName", "address"],
      number: ["parkId", "area"],
      date: ["openDate"],
      code: ["parkType"],
      hidden: ["geom"],
    },
    geometryColumns: [
      {
        columnName: "geom",
        geometryType: "POLYGON",
        srid: 5186,
      },
    ],
  },
};

export async function GET(
  request: NextRequest,
  { params }: { params: { menuId: string } },
) {
  try {
    const { menuId } = await params;

    // Mock 데이터에서 스키마 조회
    const schema = MOCK_SCHEMAS[menuId];

    if (!schema) {
      return NextResponse.json(
        { error: `Schema not found for menu: ${menuId}` },
        { status: 404 },
      );
    }

    // 실제 구현에서는 여기서 데이터베이스나 외부 API 호출
    // const schema = await fetchSchemaFromDatabase(menuId);
    // 또는
    // const schema = await fetchSchemaFromExternalAPI(menuId);

    return NextResponse.json(schema);
  } catch (error) {
    console.error("Error fetching schema:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

// POST 메서드로 스키마 업데이트 (관리자용)
export async function POST(
  request: NextRequest,
  { params }: { params: { menuId: string } },
) {
  try {
    const { menuId } = params;
    const schemaData: ApiSchemaResponse = await request.json();

    // 검증
    if (
      !schemaData.resource ||
      !schemaData.resourceName ||
      !Array.isArray(schemaData.fields)
    ) {
      return NextResponse.json(
        { error: "Invalid schema data" },
        { status: 400 },
      );
    }

    // Mock 데이터 업데이트 (실제로는 데이터베이스에 저장)
    MOCK_SCHEMAS[menuId] = schemaData;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error updating schema:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
