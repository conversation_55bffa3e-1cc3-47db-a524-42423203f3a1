"use client";

import { useLayer } from "@geon-map/react-odf";
import { useEffect, useRef, useState } from "react";

import type { LayerProps, LayerState } from "./layer.types";

/**
 * 선언적 API로 지도 레이어를 관리하는 컴포넌트
 *
 * @example
 * ```tsx
 * <Layer
 *   id="water-pipe"
 *   config={{
 *     type: "geoserver",
 *     server: { url: "https://geoserver.example.com" },
 *     layer: "wtl_pipe_lm",
 *     service: "wms"
 *   }}
 *   visible={true}
 *   opacity={0.8}
 *   cqlFilter="ftrIdn = '241001'"
 *   onLayerReady={(layerId) => console.log('Layer ready:', layerId)}
 * />
 * ```
 */
export function Layer({
  id,
  config,
  visible = true,
  opacity = 1,
  cqlFilter,
  zIndex,
  onLayerReady,
  onLayerError,
}: LayerProps) {
  const { addLayers, removeLayers, updateLayerVisibility, updateLayerOpacity } = useLayer();

  // 레이어 상태 관리
  const [state, setState] = useState<LayerState>({
    mapLayerId: null,
    loading: false,
    error: null,
  });

  // 이전 값들을 추적하기 위한 ref
  const prevConfigRef = useRef<string>();
  const prevCqlFilterRef = useRef<string | null>();

  // 레이어 설정이 변경되었는지 확인
  const configKey = JSON.stringify(config);
  const configChanged = prevConfigRef.current !== configKey;
  const cqlFilterChanged = prevCqlFilterRef.current !== cqlFilter;

  // 디버깅 로그
  console.log(`[Layer:${id}] Render`, {
    visible,
    opacity,
    cqlFilter,
    configChanged,
    cqlFilterChanged,
    mapLayerId: state.mapLayerId,
    loading: state.loading,
  });

  // 레이어 생성/업데이트 효과
  useEffect(() => {
    let isCancelled = false;

    const createOrUpdateLayer = async () => {
      // 설정이 변경되었거나 CQL 필터가 변경된 경우 레이어 재생성
      if (configChanged || cqlFilterChanged || !state.mapLayerId) {
        console.log(`[Layer:${id}] Creating/updating layer`, {
          configChanged,
          cqlFilterChanged,
          hasMapLayerId: !!state.mapLayerId,
        });

        setState(prev => ({ ...prev, loading: true, error: null }));

        try {
          // 기존 레이어가 있으면 제거
          if (state.mapLayerId) {
            console.log(`[Layer:${id}] Removing existing layer:`, state.mapLayerId);
            await removeLayers([state.mapLayerId]);
          }

          // 새 레이어 설정 준비
          const layerConfig = {
            ...config,
            visible,
            ...(cqlFilter && { cqlFilter }),
          };

          console.log(`[Layer:${id}] Adding new layer with config:`, layerConfig);

          // 새 레이어 추가
          const newLayerIds = await addLayers([layerConfig]);

          if (isCancelled) return;

          if (newLayerIds && newLayerIds.length > 0) {
            const newLayerId = newLayerIds[0];
            console.log(`[Layer:${id}] Layer created successfully:`, newLayerId);

            setState(prev => ({
              ...prev,
              mapLayerId: newLayerId,
              loading: false,
              error: null,
            }));

            // 레이어 준비 완료 콜백 호출
            onLayerReady?.(newLayerId);
          } else {
            throw new Error("Failed to create layer: No layer ID returned");
          }

        } catch (error) {
          if (isCancelled) return;

          console.error(`[Layer:${id}] Failed to create layer:`, error);
          const layerError = error instanceof Error ? error : new Error(String(error));

          setState(prev => ({
            ...prev,
            loading: false,
            error: layerError,
            mapLayerId: null,
          }));

          onLayerError?.(layerError);
        }
      }
    };

    createOrUpdateLayer();

    // 이전 값들 업데이트
    prevConfigRef.current = configKey;
    prevCqlFilterRef.current = cqlFilter;

    return () => {
      isCancelled = true;
    };
  }, [
    id,
    configKey,
    cqlFilter,
    visible,
    configChanged,
    cqlFilterChanged,
    state.mapLayerId,
    addLayers,
    removeLayers,
    onLayerReady,
    onLayerError,
  ]);

  // 가시성 업데이트 효과 (레이어 재생성 없이)
  useEffect(() => {
    if (state.mapLayerId && !state.loading) {
      console.log(`[Layer:${id}] Updating visibility:`, visible);
      updateLayerVisibility(state.mapLayerId, visible);
    }
  }, [id, state.mapLayerId, state.loading, visible, updateLayerVisibility]);

  // 투명도 업데이트 효과 (레이어 재생성 없이)
  useEffect(() => {
    if (state.mapLayerId && !state.loading) {
      console.log(`[Layer:${id}] Updating opacity:`, opacity);
      updateLayerOpacity(state.mapLayerId, opacity);
    }
  }, [id, state.mapLayerId, state.loading, opacity, updateLayerOpacity]);

  // 컴포넌트 언마운트 시 레이어 정리
  useEffect(() => {
    return () => {
      if (state.mapLayerId) {
        console.log(`[Layer:${id}] Cleaning up layer:`, state.mapLayerId);
        removeLayers([state.mapLayerId]).catch(error => {
          console.error(`[Layer:${id}] Failed to cleanup layer:`, error);
        });
      }
    };
  }, [id, state.mapLayerId, removeLayers]);

  // 이 컴포넌트는 UI를 렌더링하지 않음 (레이어 관리만)
  return null;
}