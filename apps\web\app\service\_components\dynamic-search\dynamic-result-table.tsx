"use client";

import { <PERSON><PERSON> } from "@geon-ui/react/primitives/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@geon-ui/react/primitives/dropdown-menu";
import { Input } from "@geon-ui/react/primitives/input";
import { ScrollArea, ScrollBar } from "@geon-ui/react/primitives/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@geon-ui/react/primitives/select";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@geon-ui/react/primitives/table";
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";
import { ChevronDown } from "lucide-react";
import * as React from "react";

import type { ResultColumnSchema } from "./types";

export type DynamicResultTableProps<
  Row extends Record<string, any> = Record<string, any>,
> = {
  columns: ResultColumnSchema[];
  rows: Row[];
  onAction?: (actionId: string, row: Row) => void;
  onRowClick?: (row: Row, index: number) => void;
  // 서버 사이드 페이지네이션 props
  searchKey?: string;
  searchPlaceholder?: string;
  pageIndex?: number;
  pageSize?: number;
  totalCount?: number;
  onPageChange?: (pageIndex: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  onSearch?: (searchTerm: string) => void;
  isLoading?: boolean;
  // 사이드바 환경에 맞춘 컴팩트 모드
  compact?: boolean;
  maxHeight?: string;
};

export default function DynamicResultTable<Row extends Record<string, any>>({
  columns,
  rows,
  onAction,
  onRowClick,
  searchKey,
  searchPlaceholder = "검색...",
  pageIndex = 1,
  pageSize = 25,
  totalCount,
  onPageChange,
  onPageSizeChange,
  onSearch,
  isLoading = false,
  compact = true,
  maxHeight = "400px",
}: DynamicResultTableProps<Row>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [searchTerm, setSearchTerm] = React.useState("");

  // tanstack table용 컬럼 변환
  const tableColumns = React.useMemo(() => {
    return columns.map((col): ColumnDef<Row> => {
      if (col.type === "actions") {
        return {
          id: col.id,
          header: col.label,
          cell: ({ row }) => (
            <div className="flex gap-1">
              {col.actions.map((action) => (
                <Button
                  key={action.id}
                  size={compact ? "sm" : "default"}
                  variant="outline"
                  className={compact ? "h-7 px-2 text-xs" : ""}
                  onClick={(e) => {
                    e.stopPropagation();
                    onAction?.(action.id, row.original);
                  }}
                >
                  {action.label}
                </Button>
              ))}
            </div>
          ),
          enableSorting: false,
        };
      }

      return {
        id: col.id,
        header: col.label,
        accessorKey: col.accessor,
        cell: ({ row }) => {
          const value = row.getValue(col.id) as string;
          return (
            <div className={compact ? "text-xs" : "text-sm"}>
              {value || "-"}
            </div>
          );
        },
      };
    });
  }, [columns, onAction, compact]);

  const table = useReactTable({
    data: rows,
    columns: tableColumns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    manualPagination: !!totalCount,
    pageCount: totalCount ? Math.ceil(totalCount / pageSize) : undefined,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      pagination: {
        pageIndex: pageIndex - 1,
        pageSize,
      },
    },
  });

  // 검색 핸들러
  const handleSearch = React.useCallback(
    (term: string) => {
      setSearchTerm(term);
      if (onSearch) {
        onSearch(term);
      } else if (searchKey) {
        table.getColumn(searchKey)?.setFilterValue(term);
      }
    },
    [onSearch, searchKey, table],
  );

  // 페이지 변경 핸들러
  const handlePageChange = (newPageIndex: number) => {
    if (onPageChange) {
      onPageChange(newPageIndex + 1);
    } else {
      table.setPageIndex(newPageIndex);
    }
  };

  // 페이지 크기 변경 핸들러
  const handlePageSizeChange = (newPageSize: string) => {
    const size = Number(newPageSize);
    if (onPageSizeChange) {
      onPageSizeChange(size);
    } else {
      table.setPageSize(size);
    }
  };

  // 페이지네이션 정보 계산
  const totalPages = totalCount
    ? Math.ceil(totalCount / pageSize)
    : table.getPageCount();
  const currentPageIndex = totalCount
    ? pageIndex
    : table.getState().pagination.pageIndex + 1;
  const currentPageSize = totalCount
    ? pageSize
    : table.getState().pagination.pageSize;
  const startItem = totalCount
    ? (pageIndex - 1) * pageSize + 1
    : table.getState().pagination.pageIndex *
        table.getState().pagination.pageSize +
      1;
  const endItem = totalCount
    ? Math.min(pageIndex * pageSize, totalCount)
    : Math.min(
        (table.getState().pagination.pageIndex + 1) *
          table.getState().pagination.pageSize,
        rows.length,
      );
  const total = totalCount || rows.length;

  return (
    <div className="flex h-full flex-col overflow-hidden">
      {/* 검색 및 컨트롤 - 고정 영역 */}
      <div
        className={`flex flex-shrink-0 items-center ${
          compact ? "py-2" : "py-3"
        }`}
      >
        {(searchKey || onSearch) && (
          <Input
            placeholder={searchPlaceholder}
            value={searchTerm}
            onChange={(e) => handleSearch(e.target.value)}
            className={compact ? "h-7 max-w-sm text-xs" : "h-8 max-w-sm"}
          />
        )}

        {/* 페이지 크기 선택 */}
        <Select
          value={currentPageSize.toString()}
          onValueChange={handlePageSizeChange}
        >
          <SelectTrigger
            className={compact ? "ml-2 h-7 w-28 text-xs" : "ml-2 h-8 w-32"}
          >
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="10">
              {compact ? "10개씩" : "10개씩 보기"}
            </SelectItem>
            <SelectItem value="25">
              {compact ? "25개씩" : "25개씩 보기"}
            </SelectItem>
            <SelectItem value="50">
              {compact ? "50개씩" : "50개씩 보기"}
            </SelectItem>
            <SelectItem value="100">
              {compact ? "100개씩" : "100개씩 보기"}
            </SelectItem>
          </SelectContent>
        </Select>

        {/* 컬럼 표시/숨김 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size={compact ? "sm" : "default"}
              className={compact ? "ml-auto h-7" : "ml-auto h-8"}
            >
              컬럼{" "}
              <ChevronDown
                className={compact ? "ml-2 h-3 w-3" : "ml-2 h-4 w-4"}
              />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* 테이블 컨테이너 - 고정 높이 + 강제 스크롤 */}
      <div className="bg-background min-h-0 flex-1 overflow-hidden rounded-md border">
        <ScrollArea className="h-full w-full">
          <div
            style={{
              minHeight: compact ? "300px" : "400px",
              maxHeight: maxHeight,
              height: "100%",
            }}
          >
            <Table className="w-full">
              {/* 고정 헤더 */}
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow
                    key={headerGroup.id}
                    className="border-b hover:bg-transparent"
                  >
                    {headerGroup.headers.map((header) => (
                      <TableHead
                        key={header.id}
                        className={`bg-muted sticky top-0 z-10 border-r last:border-r-0 ${
                          compact
                            ? "h-auto min-w-[80px] p-1 text-xs"
                            : "h-auto min-w-[120px] p-2"
                        }`}
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>

              {/* 스크롤 가능한 바디 */}
              <TableBody>
                {isLoading ? (
                  // 스켈레톤 로딩
                  Array.from({ length: Math.min(currentPageSize, 10) }).map(
                    (_, index) => (
                      <TableRow key={`skeleton-${index}`} className="border-b">
                        {tableColumns.map((_, colIndex) => {
                          const widthClasses = [
                            "w-full",
                            "w-3/4",
                            "w-1/2",
                            "w-2/3",
                            "w-5/6",
                          ];
                          const randomWidth =
                            widthClasses[
                              (index + colIndex) % widthClasses.length
                            ];

                          return (
                            <TableCell
                              key={`skeleton-cell-${index}-${colIndex}`}
                              className={`border-r last:border-r-0 ${
                                compact
                                  ? "min-w-[80px] p-1"
                                  : "min-w-[120px] p-2"
                              }`}
                            >
                              <Skeleton
                                className={`${compact ? "h-3" : "h-4"} ${randomWidth}`}
                              />
                            </TableCell>
                          );
                        })}
                      </TableRow>
                    ),
                  )
                ) : table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row, index) => (
                    <TableRow
                      key={row.id}
                      className="hover:bg-muted/30 cursor-pointer border-b"
                      onClick={() => onRowClick?.(row.original, index)}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell
                          key={cell.id}
                          className={`whitespace-nowrap border-r last:border-r-0 ${
                            compact
                              ? "min-w-[80px] p-1 text-xs"
                              : "min-w-[120px] p-2 text-sm"
                          }`}
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext(),
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={tableColumns.length}
                      className="text-muted-foreground h-24 text-center"
                    >
                      데이터가 없습니다.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          <ScrollBar orientation="vertical" />
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>

      {/* 페이지네이션 - 고정 영역 */}
      <div
        className={`flex flex-shrink-0 items-center justify-between space-x-2 ${
          compact ? "py-2" : "py-3"
        }`}
      >
        <div
          className={`text-muted-foreground flex-1 ${
            compact ? "text-xs" : "text-sm"
          }`}
        >
          총 {total.toLocaleString()}개 항목 중 {startItem.toLocaleString()}-
          {endItem.toLocaleString()}개 표시
        </div>
        <div className="flex items-center space-x-2">
          <div
            className={`text-muted-foreground ${
              compact ? "text-xs" : "text-sm"
            }`}
          >
            페이지 {currentPageIndex} / {totalPages}
          </div>
          <Button
            variant="outline"
            size={compact ? "sm" : "default"}
            onClick={() => handlePageChange(currentPageIndex - 2)}
            disabled={currentPageIndex <= 1 || isLoading}
          >
            이전
          </Button>
          <Button
            variant="outline"
            size={compact ? "sm" : "default"}
            onClick={() => handlePageChange(currentPageIndex)}
            disabled={currentPageIndex >= totalPages || isLoading}
          >
            다음
          </Button>
        </div>
      </div>
    </div>
  );
}
