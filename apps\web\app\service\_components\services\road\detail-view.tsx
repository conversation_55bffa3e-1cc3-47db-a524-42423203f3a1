"use client";

import React from "react";

import type { TableSchemaResponse } from "../../../_types/dynamic-table";
import type { FacilityDetailViewProps } from "../../../_types/facility-detail";
import { DynamicTable } from "../../common/dynamic-table";

interface RoadDetailViewProps extends FacilityDetailViewProps {
  /** 테이블 스키마 정보 (선택적) */
  schema?: TableSchemaResponse;
}

export function RoadDetailView({ data, schema }: RoadDetailViewProps) {
  // 테스트를 위해 detail.json 데이터를 직접 임포트
  const testSchema = {
    code: 200,
    message: "성공",
    result: {
      schemaName: "gcmagp",
      tableName: "vw_water_interface",
      tableTypeCode: "v",
      tableType: "VIEW",
      columns: [
        {
          columnName: "gid",
          columnType: "BIGINT",
          columnComment: "도형번호",
          ordinalPosition: 1,
        },
        {
          columnName: "ftrCde",
          columnType: "TEXT",
          columnComment: "시설물 코드",
          ordinalPosition: 2,
        },
        {
          columnName: "ftrIdn",
          columnType: "NUMERIC",
          columnComment: "시설물 관리번호",
          ordinalPosition: 3,
        },
        {
          columnName: "hjdCde",
          columnType: "TEXT",
          columnComment: "행정동 코드",
          ordinalPosition: 4,
        },
        {
          columnName: "shtNum",
          columnType: "TEXT",
          columnComment: "도엽번호",
          ordinalPosition: 5,
        },
        {
          columnName: "mngCde",
          columnType: "TEXT",
          columnComment: "관리기관 코드",
          ordinalPosition: 6,
        },
        {
          columnName: "istYmd",
          columnType: "TEXT",
          columnComment: "설치일자",
          ordinalPosition: 7,
        },
        {
          columnName: "cntNum",
          columnType: "TEXT",
          columnComment: "공사번호",
          ordinalPosition: 8,
        },
        {
          columnName: "sysChk",
          columnType: "TEXT",
          columnComment: "시스템 여부",
          ordinalPosition: 9,
        },
        {
          columnName: "bjdCde",
          columnType: "TEXT",
          columnComment: "법정동 코드",
          ordinalPosition: 10,
        },
        {
          columnName: "geom",
          columnType: "USER_DEFINED",
          columnComment: "도형",
          ordinalPosition: 11,
        },
        {
          columnName: "sourceTable",
          columnType: "TEXT",
          columnComment: "원본 테이블명",
          ordinalPosition: 12,
        },
      ],
      geometryColumns: [
        {
          columnName: "geom",
          geometryType: "POINT",
          srid: 5186,
        },
      ],
    },
  };
  // 테스트용 더미 데이터
  const testData = {
    gid: 12345,
    ftrCde: "가로등",
    ftrIdn: data.managementNumber || "20241001001",
    hjdCde: data.adminDistrict || "서울특별시 강남구",
    shtNum: data.mapSheetNumber || "371-01",
    mngCde: data.managementAgency || "서울시설공단",
    istYmd: data.installStartDate || "2024-01-15",
    cntNum: "C2024-001",
    sysChk: "Y",
    bjdCde: "1168010100",
    geom: "[POINT 데이터]",
  };

  return (
    <DynamicTable
      schema={(schema || testSchema).result}
      data={testData}
      excludeColumns={["gid", "geom", "sourceTable"]} // 불필요한 컬럼 제외
    />
  );
}
