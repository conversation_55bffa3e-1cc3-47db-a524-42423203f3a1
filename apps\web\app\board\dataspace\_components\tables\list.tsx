"use client";

import {
  type APIRequestType,
  type APIResponseType,
  createGeonMagpClient,
  type MagpClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import { createColumnHelper } from "@tanstack/react-table";
import { useSearchParams } from "next/navigation";
import React from "react";

import { PaginationLink } from "@/app/board/_components/pagination-link";
import { formatDate } from "@/app/board/_utils";
import Pagination from "@/components/table/pagination";
import ViewTable from "@/components/table/view";

export default function List({
  ...props
}: APIRequestType<MagpClient["dataspace"]["list"]>) {
  const client = createGeonMagpClient();
  const searchParams = useSearchParams();
  const initialPage = Number(searchParams.get("page") ?? 1);
  const initialSize = Number(searchParams.get("size") ?? 10);

  const [numOfRows, setNumOfRows] = React.useState(initialSize);
  const [pageNo, setPageNo] = React.useState(initialPage);

  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<MagpClient["dataspace"]["list"]>
  >({
    queryKey: [
      "magp/dataspace",
      { ...props, pageSize: numOfRows, pageIndex: pageNo },
    ],
    queryFn: () =>
      client.dataspace.list({
        ...(props as any),
        pageSize: numOfRows,
        pageIndex: pageNo,
      }),
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError || !data || typeof data.result === "string")
    return (
      <div className="text-destructive flex justify-center align-middle">
        Error loading dataspace: {error as string}
        {data && `, ${data?.result as unknown as string}`}
      </div>
    );

  const helper = createColumnHelper<(typeof data.result.resultList)[0]>();
  const columns = [
    helper.accessor("nttSj", {
      cell: (info) => {
        const row = info.row.original;
        return (
          <PaginationLink
            href={`/board/dataspace/${row.nttId}`}
            page={pageNo}
            size={numOfRows}
            className="text-blue-600 hover:underline"
          >
            {info.getValue()}
          </PaginationLink>
        );
      },
      header: "제목",
    }),
    helper.accessor("nttCn", {
      cell: (info) => info.getValue(),
      header: "내용",
    }),
    helper.accessor("registerNm", {
      cell: (info) => info.getValue(),
      header: "작성자",
    }),

    helper.accessor("registDt", {
      cell: (info) => formatDate(info.getValue()),
      header: "등록일",
    }),
    helper.accessor("nttRdcnt", {
      cell: (info) => info.getValue(),
      header: "조회수",
    }),
  ];

  return (
    <div className="flex w-full max-w-[1500px] flex-col overflow-hidden overflow-y-auto">
      <ViewTable data={data.result["resultList"]} columns={columns} pinHeader />
      {typeof data?.result !== "string" && data?.result.pageInfo && (
        <Pagination
          type="server"
          pageInfo={data.result.pageInfo}
          onPageNoChange={setPageNo}
          onNumOfRowsChange={(newNumOfRows) => {
            setNumOfRows(newNumOfRows);
            setPageNo(1);
          }}
          isLoading={isLoading}
        />
      )}
      <div className="mb-4 flex justify-end">
        <PaginationLink
          href="/board/dataspace/create"
          page={pageNo}
          size={numOfRows}
          className="rounded-md bg-blue-600 px-3 py-1 text-sm text-white hover:bg-blue-700"
        >
          등록
        </PaginationLink>
      </div>
    </div>
  );
}
