"use client";
import { MapContainer } from "@geon-map/react-odf";
import { TOCNode } from "@geon-map/react-ui/types";

import TOCWidgetPackage from "@/components/widget/toc-widget-package";

export default function Page() {
  const customLayerData: TOCNode[] = [
    {
      id: "layer-group1",
      name: "무안 그룹1",
      visible: true,
      type: "group",
      opacity: 0.8,
      expanded: true, // 확장된 상태로 시작
      children: [
        {
          id: "layer-group1-1",
          name: "무안 그룹 1-1",
          visible: true,
          type: "group",
          opacity: 0.7,
          expanded: false, // 닫힌 상태로 시작
          children: [
            {
              id: "muan_gis:2017_2018_n1a_a0080000",
              name: "무안 레이어 1",
              visible: true,
              type: "layer",
            },
            {
              id: "muan_gis:2017_2018_n1a_a0070000",
              name: "무안 레이어 2",
              visible: true,
              type: "layer",
            },
            {
              id: "muan_gis:2017_2018_n1a_a0063321",
              name: "무안 레이어 3",
              visible: true,
              type: "layer",
            },
          ],
        },
        {
          id: "muan_gis:2017_2018_n1a_a0053326",
          name: "무안 레이어 4",
          visible: true,
          type: "layer",
        },
        {
          id: "muan_gis:2017_2018_n1l_g0030000",
          name: "무안 레이어 5",
          visible: true,
          type: "layer",
        },
        {
          id: "muan_gis:2017_2018_n1a_a0010000",
          name: "무안 레이어 6",
          visible: true,
          type: "layer",
        },
      ],
    },
    {
      id: "layer-group2",
      name: "부산 그룹",
      visible: true,
      type: "group",
      expanded: false, // 닫힌 상태로 시작
      opacity: 0.2,
      children: [
        {
          id: "Wgeonapi:L100003391",
          name: "부산 레이어",
          visible: true,
          type: "layer",
          opacity: 0.2,
        },
      ],
    },
  ];

  return (
    <div className="relative h-screen w-full">
      <MapContainer className="h-full w-full">
        <TOCWidgetPackage tocData={customLayerData} />
      </MapContainer>
    </div>
  );
}
