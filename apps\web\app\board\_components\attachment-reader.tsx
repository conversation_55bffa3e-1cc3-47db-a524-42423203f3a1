import { AttachmentResponse } from "@geon-query/model";

interface AttachmentReaderProps {
  attachment: AttachmentResponse[];
  editable?: boolean;
  onRemove?: (atchmnflId: string, fileOrdr: number) => void;
}
export default function AttachmentReader({
  attachment,
  editable = false,
  onRemove,
}: AttachmentReaderProps) {
  if (!attachment || attachment.length === 0) {
    return <p className="text-gray-500">첨부파일이 없습니다.</p>;
  }

  return (
    <ul className="mt-2 space-y-2">
      {attachment.map((file) => (
        <li
          key={file.fileOrdr}
          className="flex items-center justify-between rounded border px-3 py-2 text-sm"
        >
          <div>
            <p className="font-medium">{file.orginlFileNm}</p>
            <p className="text-xs text-gray-500">
              {Math.round(file.fileMg / 1024)} KB • {file.fileExtsnNm}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <button
              type="button"
              onClick={() => {
                // attachment.download API 호출 로직
              }}
              className="text-blue-600 hover:underline"
            >
              다운로드
            </button>
            {editable && (
              <button
                type="button"
                onClick={() => onRemove?.(file.atchmnflId, file.fileOrdr)}
                className="text-red-600 hover:underline"
              >
                ❌ 삭제
              </button>
            )}
          </div>
        </li>
      ))}
    </ul>
  );
}
