/**
 * 레이어 설정을 위한 타입 정의
 */

export interface LayerConfig {
  /** 지오서버 설정 */
  type: "geoserver";
  /** 서버 설정 */
  server: { url: string };
  /** 인증키 */
  crtfckey?: string;
  /** 레이어명 */
  layer: string;
  /** 서비스 타입 */
  service: "wms" | "wfs";
  /** 요청 메소드 */
  method?: "get" | "post";
  /** 지도 영역에 맞춤 여부 */
  fit?: boolean;
}

export interface LayerProps {
  /** 고유 레이어 ID */
  id: string;
  /** 레이어 설정 */
  config: LayerConfig;
  /** 표시 여부 */
  visible?: boolean;
  /** 투명도 (0~1) */
  opacity?: number;
  /** CQL 필터 */
  cqlFilter?: string | null;
  /** z-index (레이어 순서) */
  zIndex?: number;
  /** 레이어 로드 완료 콜백 */
  onLayerReady?: (layerId: string) => void;
  /** 레이어 로드 실패 콜백 */
  onLayerError?: (error: Error) => void;
}

export interface LayerState {
  /** 지도 레이어 ID */
  mapLayerId: string | null;
  /** 로딩 상태 */
  loading: boolean;
  /** 에러 상태 */
  error: Error | null;
}