// 레이어 데이터 타입 정의
import React from "react";

export type TOCNode = GroupNode | ItemNode;

export interface GroupNode {
  id: string;
  name: string;
  type: "group";
  expanded?: boolean;
  opacity?: number;
  visible?: boolean;
  children?: TOCNode[];
}
export interface ItemNode {
  id: string;
  name: string;
  type: "layer";
  visible?: boolean;
  opacity?: number;
  layerId?: string; // ODF 레이어 ID
}

// TocTreeGroup Props
export interface TocTreeGroupProps
  extends React.HTMLAttributes<HTMLDivElement> {
  data: GroupNode;
  level?: number;
  onToggleVisibility?: (id: string, visible: boolean) => void;
  onToggleExpanded?: (id: string, expanded: boolean) => void;
  expanded?: boolean;
}

// TocTreeItem Props
export interface TocTreeItemProps extends React.HTMLAttributes<HTMLDivElement> {
  data: ItemNode;
  level?: number;
  onToggleVisibility?: (id: string, visible: boolean) => void;
}
// Custom Hook
export interface TOCOptions {
  data?: TOCNode[];
  onLayerVisibilityChange?: (id: string, visible: boolean) => void;
  onGroupExpandedChange?: (id: string, expanded: boolean) => void;
  isGroupOpacityEnabled?: boolean;
  isLayerOpacityEnabled?: boolean;
}

// Root Container - TOCOptions의 모든 속성을 최상위 props로 받도록 수정
export interface TOCRootProps extends React.HTMLAttributes<HTMLDivElement> {
  data?: TOCNode[];
  onLayerVisibilityChange?: (id: string, visible: boolean) => void;
  onGroupExpandedChange?: (id: string, expanded: boolean) => void;
  isGroupOpacityEnabled?: boolean;
  isLayerOpacityEnabled?: boolean;
}
// Tree Group - 그룹 레이어를 위한 컴포넌트
export interface TOCTreeGroupContextProps
  extends Omit<TocTreeGroupProps, "onToggleVisibility" | "onToggleExpanded"> {}

// Tree Item - 개별 레이어를 위한 컴포넌트
export interface TOCTreeItemContextProps
  extends Omit<TocTreeItemProps, "onToggleVisibility"> {}

export interface GroupOpacitySliderProps
  extends React.HTMLAttributes<HTMLDivElement> {
  groupId: string;
}

// Complete Widget - TOCOptions의 모든 속성을 최상위 props로 받도록 수정
export interface TOCWidgetProps extends React.HTMLAttributes<HTMLDivElement> {
  data?: TOCNode[];
  onLayerVisibilityChange?: (id: string, visible: boolean) => void;
  onGroupExpandedChange?: (id: string, expanded: boolean) => void;
  isGroupOpacityEnabled?: boolean;
  isLayerOpacityEnabled?: boolean;
  showHeader?: boolean;
}
