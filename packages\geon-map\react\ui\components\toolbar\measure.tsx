"use client";

import { DrawingMode } from "@geon-map/core";
import { useDraw } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Popover, PopoverTrigger } from "@geon-ui/react/primitives/popover";
import { AreaChart, Circle, Ruler, Target, X } from "lucide-react";
import * as React from "react";

import { Button } from "./base/button";
import {
  ToolbarContent,
  ToolbarItem,
  ToolbarTrigger,
} from "./base/toolbar-item";

// Drawing Mode 타입 (DrawingMode 확장)
export type MeasureMode = DrawingMode;

const MEASUREMENT_TOOLS = [
  {
    id: "measure-distance",
    mode: "measure-distance" as MeasureMode,
    label: "거리 측정",
    icon: Ruler,
    description: "두 지점 간의 거리를 측정합니다",
    color: "text-cyan-500",
  },
  {
    id: "measure-area",
    mode: "measure-area" as MeasureMode,
    label: "면적 측정",
    icon: AreaChart,
    description: "영역의 면적을 측정합니다",
    color: "text-emerald-500",
  },
  {
    id: "measure-round",
    mode: "measure-round" as MeasureMode,
    label: "둘레 측정",
    icon: Circle,
    description: "둘레를 측정합니다",
    color: "text-yellow-500",
  },
  {
    id: "measure-spot",
    mode: "measure-spot" as MeasureMode,
    label: "지점 측정",
    icon: Target,
    description: "특정 지점을 측정합니다",
    color: "text-red-500",
  },
] as const;

// Context for ToolbarMeasure
interface MeasurementContextValue {
  startDrawing: (mode: MeasureMode) => any;
  stopDrawing: () => void;
  mode: MeasureMode | null;
  isDrawing: boolean;
}

const MeasurementContext = React.createContext<MeasurementContextValue | null>(
  null,
);

const useMeasurementContext = () => {
  const context = React.useContext(MeasurementContext);
  if (!context) {
    throw new Error(
      "ToolbarMeasure components must be used within ToolbarMeasure",
    );
  }
  return context;
};

// Props for compound components
export interface ToolbarMeasureProps
  extends React.HTMLAttributes<HTMLDivElement> {}

export interface ToolbarMeasureTriggerProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 툴팁 텍스트 */
  tooltip?: string;
  /** 버튼 크기 */
  size?: "sm" | "lg" | "default" | "icon";
}

export interface ToolbarMeasureContentProps
  extends React.HTMLAttributes<HTMLDivElement> {}

export interface ToolbarMeasureToolProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 측정 모드 */
  mode: MeasureMode;
}

export interface ToolbarMeasureActionProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 액션 타입 */
  action: "stop";
}

// Main ToolbarMeasure Container (Context Provider)
export const ToolbarMeasure = React.forwardRef<
  HTMLDivElement,
  ToolbarMeasureProps
>(({ className, children, ...props }, ref) => {
  const { startDrawing, stopDrawing, mode, isDrawing } = useDraw();

  const contextValue = React.useMemo(
    () => ({
      startDrawing,
      stopDrawing,
      mode,
      isDrawing,
    }),
    [startDrawing, stopDrawing, mode, isDrawing],
  );

  return (
    <MeasurementContext.Provider value={contextValue}>
      <ToolbarItem ref={ref} className={className} {...props}>
        <Popover>
          <PopoverTrigger asChild>
            {React.Children.toArray(children).find(
              (child) =>
                React.isValidElement(child) &&
                child.type === ToolbarMeasureTrigger,
            )}
          </PopoverTrigger>
          {React.Children.toArray(children).find(
            (child) =>
              React.isValidElement(child) &&
              child.type === ToolbarMeasureContent,
          )}
        </Popover>
      </ToolbarItem>
    </MeasurementContext.Provider>
  );
});

ToolbarMeasure.displayName = "ToolbarMeasure";

// ToolbarMeasureTrigger Component
export const ToolbarMeasureTrigger = React.forwardRef<
  HTMLButtonElement,
  ToolbarMeasureTriggerProps
>(
  (
    { tooltip = "측정 도구", size = "default", className, children, ...props },
    ref,
  ) => {
    const { mode, isDrawing } = useMeasurementContext();

    // 현재 활성 도구 찾기
    const activeTool = MEASUREMENT_TOOLS.find((tool) => tool.mode === mode);

    // 기본 아이콘 (children이 없을 때)
    const CurrentIcon = activeTool?.icon || Ruler;

    return (
      <ToolbarTrigger
        ref={ref}
        tooltip={tooltip}
        size={size}
        active={isDrawing || !!activeTool}
        className={className}
        {...props}
      >
        {children || <CurrentIcon className="h-4 w-4" />}
      </ToolbarTrigger>
    );
  },
);

ToolbarMeasureTrigger.displayName = "ToolbarMeasureTrigger";

// ToolbarMeasureContent Component
export const ToolbarMeasureContent = React.forwardRef<
  HTMLDivElement,
  ToolbarMeasureContentProps
>(({ className, children, ...props }, ref) => {
  return (
    <ToolbarContent
      ref={ref}
      align="center"
      sideOffset={16}
      className={cn("w-fit flex flex-col gap-3 p-4", className)}
      {...props}
    >
      {children}
    </ToolbarContent>
  );
});

ToolbarMeasureContent.displayName = "ToolbarMeasureContent";

// ToolbarMeasureTool Component (개별 측정 도구)
export const ToolbarMeasureTool = React.forwardRef<
  HTMLButtonElement,
  ToolbarMeasureToolProps
>(({ mode, className, children, ...props }, ref) => {
  const {
    startDrawing,
    mode: currentMode,
    stopDrawing,
  } = useMeasurementContext();

  const toolConfig = MEASUREMENT_TOOLS.find((tool) => tool.mode === mode);

  if (!toolConfig) return null;

  const Icon = toolConfig.icon;
  const isActive = currentMode === mode;

  const handleClick = () => {
    if (isActive) {
      stopDrawing();
    } else {
      const { drawend } = startDrawing(mode);
      drawend((feature: any) => {
        console.log("Measure end:", feature);
      });
    }
  };

  return (
    <Button
      ref={ref}
      variant="ghost"
      size="sm"
      active={isActive}
      onClick={handleClick}
      className={cn(
        "flex items-center justify-start gap-2 h-8 px-3 min-w-32",
        className,
      )}
      title={toolConfig.description}
      {...props}
    >
      {children || (
        <>
          <Icon className={cn("h-4 w-4", toolConfig.color)} />
          <span className="text-sm">{toolConfig.label}</span>
        </>
      )}
    </Button>
  );
});

ToolbarMeasureTool.displayName = "ToolbarMeasureTool";

// ToolbarMeasureAction Component (액션 버튼들)
export const ToolbarMeasureAction = React.forwardRef<
  HTMLButtonElement,
  ToolbarMeasureActionProps
>(({ action, className, children, ...props }, ref) => {
  const { stopDrawing, isDrawing } = useMeasurementContext();

  const handleAction = () => {
    if (action === "stop") {
      stopDrawing();
    }
  };

  if (action === "stop" && !isDrawing) return null;

  return (
    <Button
      ref={ref}
      variant="destructive"
      size="sm"
      onClick={handleAction}
      className={cn("h-6 w-6 p-0", className)}
      title="중단"
      {...props}
    >
      {children || <X className="h-3 w-3" />}
    </Button>
  );
});

ToolbarMeasureAction.displayName = "ToolbarMeasureAction";
