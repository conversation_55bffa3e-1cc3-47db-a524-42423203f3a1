"use client";

import { Map } from "@geon-map/react-odf";
import React from "react";

import { CustomBaseLayerWidget } from "@/components/widget/custom-baselayer-widget";

export default function Page() {
  return (
    <React.Fragment>
      <Map className="h-screen w-full">
        <CustomBaseLayerWidget />
      </Map>
      <div className="bg-background absolute left-0 top-0 h-screen w-[400px]">
        {/* Side Panel Area */}
        TEST AREA
      </div>
    </React.Fragment>
  );
}
