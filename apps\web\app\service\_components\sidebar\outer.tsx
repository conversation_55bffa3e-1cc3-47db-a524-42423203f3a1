import { cn } from "@geon-ui/react/lib/utils";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
} from "@geon-ui/react/primitives/sidebar";
import React from "react";

import HomeButton from "@/components/sidebar/home-button";
import NavUser from "@/components/sidebar/nav-user";

import NavService from "./nav-service";

// This is sample data.
const data = {
  user: {
    name: "홍길동",
    department: "민원지적과",
  },
};

export default function OuterSidebar({
  className,
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar {...props} collapsible="icon" className={cn("z-50", className)}>
      <SidebarHeader>
        <HomeButton />
      </SidebarHeader>
      <SidebarContent>
        <NavService />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  );
}
