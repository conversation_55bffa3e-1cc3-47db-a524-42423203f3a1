import type { SpatialSearchOptions } from "../_types/facility";

export type CQLFilterOptions = {
  /** 검색 파라미터들 (ftrIdn, mngCde 등) */
  searchParams?: Record<string, any>;
  /** 공간 검색 조건 */
  spatialSearch?: SpatialSearchOptions | null;
  /** 추가 사용자 정의 조건 */
  customFilters?: string[];
};

/**
 * 검색 파라미터를 CQL 필터 문자열로 변환합니다.
 *
 * @param options - CQL 필터 생성 옵션
 * @returns CQL 필터 문자열 (조건이 없으면 null)
 */
export function createCQLFilter(options: CQLFilterOptions): string | null {
  const filters: string[] = [];

  // 검색 파라미터를 CQL 필터로 변환
  if (options.searchParams) {
    Object.entries(options.searchParams).forEach(([key, value]) => {
      if (value === undefined || value === null || value === '') {
        return; // 빈 값은 건너뛰기
      }

      // 특수한 파라미터들은 제외
      const excludeParams = ['spatialWkt', 'emdCd', 'sourceTables', 'pageSize', 'pageIndex'];
      if (excludeParams.includes(key)) {
        return;
      }

      // 값의 타입에 따라 CQL 조건 생성
      if (typeof value === 'string') {
        // 문자열은 LIKE 또는 = 조건으로
        if (key.toLowerCase().includes('name') || key.toLowerCase().includes('comment')) {
          filters.push(`${key} LIKE '%${escapeCQLValue(value)}%'`);
        } else {
          filters.push(`${key} = '${escapeCQLValue(value)}'`);
        }
      } else if (typeof value === 'number') {
        filters.push(`${key} = ${value}`);
      } else if (Array.isArray(value)) {
        // 배열은 IN 조건으로
        if (value.length > 0) {
          const values = value.map(v => typeof v === 'string' ? `'${escapeCQLValue(v)}'` : v).join(', ');
          filters.push(`${key} IN (${values})`);
        }
      }
    });
  }

  // 공간 검색 필터링
  if (options.spatialSearch) {
    const spatialFilter = createSpatialCQLFilter(options.spatialSearch);
    if (spatialFilter) {
      filters.push(spatialFilter);
    }
  }

  // 사용자 정의 필터 추가
  if (options.customFilters && options.customFilters.length > 0) {
    filters.push(...options.customFilters);
  }

  // 모든 필터를 AND로 연결
  if (filters.length === 0) {
    return null;
  }

  return filters.length === 1 ? filters[0] : `(${filters.join(' AND ')})`;
}

/**
 * 공간 검색 조건을 CQL 공간 필터로 변환합니다.
 *
 * @param spatialSearch - 공간 검색 옵션
 * @returns CQL 공간 필터 문자열
 */
function createSpatialCQLFilter(spatialSearch: SpatialSearchOptions): string | null {
  if (!spatialSearch.coordinates) {
    return null;
  }

  switch (spatialSearch.type) {
    case "point":
      // 점 검색: DWITHIN 사용
      const [x, y] = spatialSearch.coordinates;
      const radius = spatialSearch.radius || 100; // 기본값 100m
      return `DWITHIN(geom, POINT(${x} ${y}), ${radius}, meters)`;

    case "polygon":
      // 폴리곤 검색: INTERSECTS 사용
      const coordinates = spatialSearch.coordinates.flat();
      if (coordinates.length < 6) { // 최소 3개 점 필요 (x,y 쌍)
        return null;
      }
      const polygonCoords = coordinates.join(' ');
      return `INTERSECTS(geom, POLYGON((${polygonCoords})))`;

    case "radius":
      // 반경 검색: DWITHIN 사용
      const [centerX, centerY] = spatialSearch.coordinates;
      const searchRadius = spatialSearch.radius || 1000; // 기본값 1km
      return `DWITHIN(geom, POINT(${centerX} ${centerY}), ${searchRadius}, meters)`;

    case "freehand":
      // 자유형 검색: INTERSECTS 사용 (폴리곤과 동일)
      const freehandCoords = spatialSearch.coordinates.flat();
      if (freehandCoords.length < 6) {
        return null;
      }
      const freehandPolygon = freehandCoords.join(' ');
      return `INTERSECTS(geom, POLYGON((${freehandPolygon})))`;

    default:
      return null;
  }
}

/**
 * CQL 필터에서 특수 문자를 이스케이프합니다.
 *
 * @param value - 이스케이프할 문자열
 * @returns 이스케이프된 문자열
 */
function escapeCQLValue(value: string): string {
  return value.replace(/'/g, "''");
}

/**
 * 여러 CQL 필터를 논리 연산자로 결합합니다.
 *
 * @param filters - 결합할 CQL 필터 배열
 * @param operator - 논리 연산자 ('AND' | 'OR')
 * @returns 결합된 CQL 필터 문자열
 */
export function combineCQLFilters(
  filters: (string | null)[],
  operator: 'AND' | 'OR' = 'AND'
): string | null {
  const validFilters = filters.filter(f => f !== null && f.trim() !== '') as string[];

  if (validFilters.length === 0) {
    return null;
  }

  if (validFilters.length === 1) {
    return validFilters[0];
  }

  return `(${validFilters.join(` ${operator} `)})`;
}

/**
 * 시간 범위 CQL 필터를 생성합니다.
 *
 * @param field - 시간 필드명
 * @param startDate - 시작 날짜
 * @param endDate - 종료 날짜
 * @returns 시간 범위 CQL 필터 문자열
 */
export function createTimeRangeCQLFilter(
  field: string,
  startDate?: Date | string,
  endDate?: Date | string
): string | null {
  const filters: string[] = [];

  if (startDate) {
    const start = startDate instanceof Date ? startDate.toISOString() : startDate;
    filters.push(`${field} >= '${start}'`);
  }

  if (endDate) {
    const end = endDate instanceof Date ? endDate.toISOString() : endDate;
    filters.push(`${field} <= '${end}'`);
  }

  return combineCQLFilters(filters);
}

/**
 * 검색 조건에서 CQL 필터를 생성하는 헬퍼 함수
 *
 * @param searchParams - 검색 파라미터 객체 (API 요청에서 사용되는 것과 동일)
 * @param spatialSearch - 공간 검색 조건
 * @returns CQL 필터 문자열
 */
export function createCQLFilterFromSearchParams(
  searchParams: Record<string, any>,
  spatialSearch?: SpatialSearchOptions | null
): string | null {
  return createCQLFilter({
    searchParams,
    spatialSearch,
  });
}