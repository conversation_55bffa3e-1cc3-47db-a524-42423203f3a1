"use client";

import { Alert, AlertDescription } from "@geon-ui/react/primitives/alert";
import { AlertCircle, Loader2 } from "lucide-react";
import { ReactNode } from "react";

import { type ServiceSearchSchema } from "@/app/service/_components/dynamic-search";

import type { FacilityType } from "../../_types/facility";

type SchemaStateGuardProps = {
  schemaLoading: boolean;
  schemaError: string | null;
  schema: ServiceSearchSchema | null;
  selectedFacilities: FacilityType[];
  children: (schema: ServiceSearchSchema) => ReactNode;
};

export function SchemaStateGuard({
  schemaLoading,
  schemaError,
  schema,
  selectedFacilities,
  children,
}: SchemaStateGuardProps) {
  if (schemaLoading) {
    return (
      <CenteredState>
        <div className="flex items-center gap-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span className="text-muted-foreground text-sm">
            검색설정을불러오는중...
          </span>
        </div>
      </CenteredState>
    );
  }

  if (schemaError) {
    return (
      <CenteredState>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            검색설정을불러올수없습니다: {schemaError}
          </AlertDescription>
        </Alert>
      </CenteredState>
    );
  }

  if (!schema) {
    return (
      <CenteredState>
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            해당서비스의검색설정이존재하지않습니다.
          </AlertDescription>
        </Alert>
      </CenteredState>
    );
  }

  if (selectedFacilities.length === 0) {
    return (
      <CenteredState>
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            상단에서 시설물을 선택한 후 검색해 주세요.
          </AlertDescription>
        </Alert>
      </CenteredState>
    );
  }

  return children(schema);
}

function CenteredState({ children }: { children: ReactNode }) {
  return (
    <div className="flex h-full items-center justify-center p-6">
      {children}
    </div>
  );
}
