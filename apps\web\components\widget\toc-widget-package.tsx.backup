"use client";

import { useLayer } from "@geon-map/react-odf";
import { TOCWidget } from "@geon-map/react-ui/components";
import { TOCNode, TOCWidgetProps } from "@geon-map/react-ui/types";
import { crtfckey, WMS_URL } from "@geon-query/model";
import { cn } from "@geon-ui/react/utils";
import { useCallback, useEffect, useState } from "react";

interface TOCWidgetPackageProps {
  className?: string;
  tocOptions?: Omit<TOCWidgetProps, "data">;
  tocData?: TOCNode[];
  tocDataLoader?: () => Promise<TOCNode[]>;
  visible?: boolean;
}

export default function TOCWidgetPackage({
  className = "absolute left-5 top-20 flex max-h-[700px] min-h-[300px] w-[400px] flex-col",
  tocOptions,
  tocData,
  tocDataLoader,
  visible = true,
}: TOCWidgetPackageProps) {
  const { addLayers } = useLayer();
  const [tocNodes, setTOCNodes] = useState<TOCNode[]>([]);

  /**
   * TOC 데이터에서 레이어 설정을 추출합니다.
   * 계층 구조를 순회하여 레이어 노드만 찾아 지오서버 설정으로 변환합니다.
   * @param tocData - TOC 노드 배열
   * @returns 레이어 설정 배열과 레이어 매핑 정보
   */
  const extractLayerConfigs = useCallback((tocData: TOCNode[]) => {
    const configs: any[] = [];
    const originalLayerMap = new Map<string, number>();

    const traverse = (nodes: TOCNode[]) => {
      nodes.forEach((node) => {
        if (node.type === "layer") {
          originalLayerMap.set(node.id, configs.length);
          configs.push({
            type: "geoserver",
            server: { url: WMS_URL },
            crtfckey,
            layer: node.id,
            service: "wms",
            method: "post",
            visible: node.visible ?? true,
            fit: false,
          });
        } else if (node.children) {
          traverse(node.children);
        }
      });
    };

    traverse(tocData);

    // 지도에 추가할 때는 역순으로
    const reversedConfigs = [...configs].reverse();

    // layerMap도 뒤집힌 순서에 맞게 업데이트
    const layerMap = new Map<string, number>();
    originalLayerMap.forEach((originalIndex, layerId) => {
      const newIndex = configs.length - 1 - originalIndex;
      layerMap.set(layerId, newIndex);
    });

    return { configs: reversedConfigs, layerMap };
  }, []);

  /**
   * 생성된 레이어 ID를 TOC 데이터에 할당합니다.
   * addLayers로 생성된 layerId를 원본 TOC 구조의 layerId 필드에 설정합니다.
   * @param tocData - 원본 TOC 데이터
   * @param layerIds - addLayers로 생성된 레이어 ID 배열
   * @param layerMap - 레이어 인덱스 매핑 정보
   * @returns layerId가 설정된 TOC 데이터
   */
  const assignLayerIds = useCallback(
    (tocData: TOCNode[], layerIds: string[], layerMap: Map<string, number>) => {
      const updateNodes = (nodes: TOCNode[]): TOCNode[] =>
        nodes.map((node) => {
          if (node.type === "layer") {
            const index = layerMap.get(node.id);
            return {
              ...node,
              layerId: index !== undefined ? layerIds[index] : undefined,
            };
          }
          return node.children
            ? { ...node, children: updateNodes(node.children) }
            : node;
        });

      return updateNodes(tocData);
    },
    [],
  );

  useEffect(() => {
    // 1순위: tocData (정적 데이터)
    if (tocData) {
      const processStaticData = async () => {
        try {
          const { configs, layerMap } = extractLayerConfigs(tocData!);

          if (configs.length === 0) {
            setTOCNodes(tocData!);
            return;
          }

          const layerIds = await addLayers(configs);
          if (!layerIds?.length) {
            setTOCNodes(tocData!);
            return;
          }

          const updatedTocData = assignLayerIds(tocData!, layerIds, layerMap);
          setTOCNodes(updatedTocData);
        } catch (error) {
          console.error("Failed to process static TOC data:", error);
          setTOCNodes(tocData!);
        }
      };

      processStaticData();
      return;
    }

    // 2순위: tocDataLoader (동적 데이터)
    if (tocDataLoader) {
      const loadData = async () => {
        try {
          const tocData = await tocDataLoader();
          const { configs, layerMap } = extractLayerConfigs(tocData);

          if (configs.length === 0) {
            setTOCNodes(tocData);
            return;
          }

          const layerIds = await addLayers(configs);
          if (!layerIds?.length) {
            setTOCNodes(tocData);
            return;
          }

          const updatedTocData = assignLayerIds(tocData, layerIds, layerMap);
          setTOCNodes(updatedTocData);
        } catch (error) {
          console.error("Failed to load dynamic TOC data:", error);
          setTOCNodes([]);
        }
      };

      loadData();
      return;
    }

    // 3순위: 빈 배열
    setTOCNodes([]);
  }, [tocData, tocDataLoader, addLayers, extractLayerConfigs, assignLayerIds]);

  return (
    <>
      {visible && (
        <TOCWidget
          className={cn(className)}
          data={tocNodes}
          showHeader={false}
          isLayerOpacityEnabled={true}
          {...tocOptions}
        />
      )}
    </>
  );
}
