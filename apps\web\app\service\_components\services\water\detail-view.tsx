"use client";

import { ScrollArea } from "@geon-ui/react/primitives/scroll-area";

import type { FacilityDetailViewProps } from "../../../_types/facility-detail";

/**
 * 날짜 포맷팅 유틸리티
 */
function formatDate(date: Date | string | undefined): string {
  if (!date) return "-";

  const dateObj = typeof date === "string" ? new Date(date) : date;
  if (isNaN(dateObj.getTime())) return "-";

  return dateObj.toLocaleDateString("ko-KR", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
}

export function RoadDetailView({ data }: FacilityDetailViewProps) {
  return (
    <ScrollArea className="h-[400px]">
      <div className="space-y-4 p-6">
        {/* 테이블 형태의 정보 표시 */}
        <table className="w-full border-collapse border border-gray-300">
          <tbody>
            {/* 첫 번째 행: 대장구분, 지형지물부호 */}
            <tr>
              <td className="w-1/6 border border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700">
                대장구분
              </td>
              <td className="w-1/6 border border-gray-300 px-3 py-2 text-sm text-gray-900">
                {data.registryType}
              </td>
              <td className="w-1/6 border border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700">
                지형지물부호
              </td>
              <td className="w-3/6 border border-gray-300 px-3 py-2 text-sm text-gray-900">
                가로등
              </td>
            </tr>

            {/* 두 번째 행: 관리번호, 행정구역 */}
            <tr>
              <td className="w-1/6 border border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700">
                관리번호
              </td>
              <td className="w-1/6 border border-gray-300 px-3 py-2 text-sm text-gray-900">
                {data.managementNumber}
              </td>
              <td className="w-1/6 border border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700">
                행정구역
              </td>
              <td className="w-3/6 border border-gray-300 px-3 py-2 text-sm text-gray-900">
                {data.adminDistrict}
              </td>
            </tr>

            {/* 세 번째 행: 도엽번호, 관리기관 */}
            <tr>
              <td className="w-1/6 border border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700">
                도엽번호
              </td>
              <td className="w-1/6 border border-gray-300 px-3 py-2 text-sm text-gray-900">
                {data.mapSheetNumber}
              </td>
              <td className="w-1/6 border border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700">
                관리기관
              </td>
              <td className="w-3/6 border border-gray-300 px-3 py-2 text-sm text-gray-900">
                {data.managementAgency}
              </td>
            </tr>

            {/* 네 번째 행: 설치 시작일, 설치 종료일 */}
            <tr>
              <td className="w-1/6 border border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700">
                설치 시작일
              </td>
              <td className="w-1/6 border border-gray-300 px-3 py-2 text-sm text-gray-900">
                {formatDate(data.installStartDate)}
              </td>
              <td className="w-1/6 border border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700">
                설치 종료일
              </td>
              <td className="w-3/6 border border-gray-300 px-3 py-2 text-sm text-gray-900">
                {formatDate(data.installEndDate)}
              </td>
            </tr>

            {/* 다섯 번째 행: 비고 (전체 너비) */}
            <tr>
              <td className="border border-gray-300 bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700">
                비고
              </td>
              <td
                colSpan={3}
                className="border border-gray-300 px-3 py-2 text-sm text-gray-900"
              >
                {data.remarks}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </ScrollArea>
  );
}
