import { Legend } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import { ScrollArea } from "@geon-ui/react/primitives/scroll-area";
import { Slider } from "@geon-ui/react/primitives/slider";
import { ChevronDown, ChevronRight, Eye, EyeOff, Layers } from "lucide-react";
import React, { useCallback } from "react";

import { useTOC } from "../../hooks";
import {
  GroupOpacitySliderProps,
  TOCNode,
  TOCOptions,
  TOCRootProps,
  TOCTreeGroupContextProps,
  TOCTreeItemContextProps,
  TOCWidgetProps,
} from "../../types";
import { LayerOpacitySlider } from "../layer-opacity-slider";

// Context
interface TOCContextValue {
  tocNodes: TOCNode[];
  handleToggleVisibility: (id: string, visible: boolean) => void;
  handleToggleExpanded: (id: string, expanded: boolean) => void;
  expandedGroups: Set<string>;
  zoomToLayer: (id: string, duration?: number) => boolean;
  isGroupOpacityEnabled?: boolean;
  isLayerOpacityEnabled?: boolean;
  getGroupOpacity: (id: string) => number;
  updateGroupOpacityState: (id: string, opacity: number) => void;
  getLayerIdsInGroup: (groupId: string) => string[];
  updateLayerOpacityState: (id: string, opacity: number) => void;
  getLayerOpacity: (id: string) => number;
}

const TOCContext = React.createContext<TOCContextValue | null>(null);

const useTOCContext = () => {
  const context = React.useContext(TOCContext);
  if (!context) {
    throw new Error("useTOCContext must be used within TOC component");
  }
  return context;
};

export const TOC = React.forwardRef<HTMLDivElement, TOCRootProps>(
  (
    {
      className,
      children,
      data,
      onLayerVisibilityChange,
      onGroupExpandedChange,
      isGroupOpacityEnabled = false,
      isLayerOpacityEnabled = false,
      ...props
    },
    ref,
  ) => {
    const tocOptions: TOCOptions = {
      data,
      onLayerVisibilityChange,
      onGroupExpandedChange,
      isGroupOpacityEnabled,
      isLayerOpacityEnabled,
    };

    const {
      tocNodes,
      expandedGroups,
      handleToggleVisibility,
      handleToggleExpanded,
      zoomToLayer,
      getGroupOpacity,
      getLayerOpacity,
      updateGroupOpacityState,
      updateLayerOpacityState,
      getLayerIdsInGroup,
    } = useTOC(tocOptions);

    const contextValue: TOCContextValue = {
      tocNodes: tocNodes,
      handleToggleVisibility: handleToggleVisibility,
      handleToggleExpanded: handleToggleExpanded,
      expandedGroups: expandedGroups,
      zoomToLayer: zoomToLayer,
      isGroupOpacityEnabled: isGroupOpacityEnabled,
      isLayerOpacityEnabled: isLayerOpacityEnabled,
      getGroupOpacity,
      getLayerOpacity,
      updateGroupOpacityState,
      updateLayerOpacityState,
      getLayerIdsInGroup,
    };

    return (
      <TOCContext.Provider value={contextValue}>
        <div
          ref={ref}
          className={cn("right-4 top-35 flex flex-col gap-2 h-full", className)}
          {...props}
        >
          {children}
        </div>
      </TOCContext.Provider>
    );
  },
);
TOC.displayName = "TOC";

// Main Container
export const TOCContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        "bg-white border rounded-lg shadow-lg p-4 flex flex-col h-full",
        className,
      )}
      {...props}
    >
      {children}
    </div>
  );
});
TOCContent.displayName = "TOCContent";

// Header
export const TOCHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn("flex items-center justify-between mb-4", className)}
      {...props}
    >
      {children}
    </div>
  );
});
TOCHeader.displayName = "TOCHeader";

// Title
export const TOCTitle = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, children, ...props }, ref) => {
  return (
    <h3 ref={ref} className={cn("text-lg font-semibold", className)} {...props}>
      {children || "레이어 목록"}
    </h3>
  );
});
TOCTitle.displayName = "TOCTitle";

// Toolbar
export const TOCToolbar = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn("flex items-center gap-1", className)}
      {...props}
    >
      {children}
    </div>
  );
});
TOCToolbar.displayName = "TOCToolbar";

// Body (Scroll Area)
export const TOCBody = React.forwardRef<
  React.ComponentRef<typeof ScrollArea>,
  React.ComponentPropsWithoutRef<typeof ScrollArea>
>(({ className, children, ...props }, ref) => {
  return (
    <ScrollArea
      ref={ref}
      className={cn("flex-1 min-h-0", className)}
      {...props}
    >
      {children}
    </ScrollArea>
  );
});
TOCBody.displayName = "TOCBody";

export const TOCTreeGroup = React.forwardRef<
  HTMLDivElement,
  TOCTreeGroupContextProps
>(({ className, data, level = 0, expanded, ...props }, ref) => {
  const {
    handleToggleVisibility,
    handleToggleExpanded,
    expandedGroups,
    isGroupOpacityEnabled,
    getLayerIdsInGroup,
    getGroupOpacity,
    updateGroupOpacityState,
  } = useTOCContext();

  // 우선순위: 1. props의 expanded, 2. data.expanded, 3. expandedGroups에서 확인
  const isExpanded =
    expanded !== undefined
      ? expanded
      : data.type === "group" && data.expanded !== undefined
        ? data.expanded
        : expandedGroups.has(data.id);

  const handleToggleExpandedClick = () => {
    handleToggleExpanded(data.id, !isExpanded);
  };

  const handleToggleVisibilityClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    handleToggleVisibility(data.id, !data.visible);
  };

  const indentStyle = { paddingLeft: `${level * 10}px` };

  return (
    <div ref={ref} className={cn("", className)} {...props}>
      {/* 그룹 헤더 */}
      <div className="flex items-center gap-2 p-2 rounded" style={indentStyle}>
        <Button
          variant="ghost"
          size="sm"
          className="h-4 w-4 p-0"
          onClick={(e) => {
            e.stopPropagation();
            handleToggleExpandedClick();
          }}
        >
          {isExpanded ? (
            <ChevronDown className="h-3 w-3" />
          ) : (
            <ChevronRight className="h-3 w-3" />
          )}
        </Button>

        <Button
          onClick={handleToggleVisibilityClick}
          className="h-6 w-6 p-0 bg-transparent hover:bg-transparent border-none shadow-none cursor-pointer"
        >
          {data.visible ? (
            <Eye className="h-4 w-4 text-green-600" />
          ) : (
            <EyeOff className="h-4 w-4 text-gray-400" />
          )}
        </Button>

        <Layers className="h-4 w-4 text-blue-600" />

        <span className="flex-1 text-sm font-medium">{data.name}</span>

        {isGroupOpacityEnabled && getLayerIdsInGroup(data.id).length > 0 ? (
          <LayerOpacitySlider
            layerIds={getLayerIdsInGroup(data.id)}
            value={getGroupOpacity(data.id)}
            onValueChange={(value) => {
              updateGroupOpacityState(data.id, value);
            }}
          />
        ) : null}
      </div>

      {/* 자식 레이어들 */}
      {isExpanded && data.type === "group" && data.children && (
        <div className="ml-2">
          {data.children.map((child) =>
            child.type === "group" ? (
              <TOCTreeGroup key={child.id} data={child} level={level + 1} />
            ) : (
              <TOCTreeItem key={child.id} data={child} level={level + 1} />
            ),
          )}
        </div>
      )}
    </div>
  );
});
TOCTreeGroup.displayName = "TOCTreeGroup";

export const TOCTreeItem = React.forwardRef<
  HTMLDivElement,
  TOCTreeItemContextProps
>(({ className, data, level = 0, ...props }, ref) => {
  const {
    handleToggleVisibility,
    zoomToLayer,
    isLayerOpacityEnabled,
    updateLayerOpacityState,
    getLayerOpacity,
  } = useTOCContext();

  const handleToggleVisibilityClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    handleToggleVisibility(data.id, !data.visible);
  };
  const handleItemClick = () => {
    zoomToLayer(data.id, 1000);
  };

  const indentStyle = { paddingLeft: `${level * 10}px` };

  return (
    <div
      ref={ref}
      className={cn(
        "flex items-center gap-2 p-2 rounded cursor-pointer",
        className,
      )}
      style={indentStyle}
      onClick={handleItemClick}
      {...props}
    >
      {/* 들여쓰기를 위한 빈 공간 */}
      <div className="w-4" />

      <Button
        onClick={handleToggleVisibilityClick}
        className="h-6 w-6 p-0 bg-transparent hover:bg-transparent border-none shadow-none cursor-pointer"
      >
        {data.visible ? (
          <Eye className="h-4 w-4 text-green-600" />
        ) : (
          <EyeOff className="h-4 w-4 text-gray-400" />
        )}
      </Button>
      <Legend layerId={data.layerId} />

      <span className="flex-1 text-sm">{data.name}</span>

      {isLayerOpacityEnabled && data.layerId ? (
        <LayerOpacitySlider
          layerIds={[data.layerId]}
          value={getLayerOpacity(data.id)}
          onValueChange={(value) => {
            if (data.layerId) {
              updateLayerOpacityState(data.id, value);
            }
          }}
        />
      ) : null}
    </div>
  );
});
TOCTreeItem.displayName = "TOCTreeItem";

// Tree
export const TOCTree = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const { tocNodes } = useTOCContext();

  return (
    <div ref={ref} className={cn("space-y-1", className)} {...props}>
      {tocNodes.map((layer) =>
        layer.type === "group" ? (
          <TOCTreeGroup key={layer.id} data={layer} />
        ) : (
          <TOCTreeItem key={layer.id} data={layer} />
        ),
      )}
    </div>
  );
});
TOCTree.displayName = "TOCTree";

export const GroupOpacitySlider = React.forwardRef<
  HTMLDivElement,
  GroupOpacitySliderProps
>(({ groupId, className, ...props }, ref) => {
  const { updateGroupOpacityState, getGroupOpacity } = useTOCContext();
  const handleValueChange = useCallback(
    (value: number[]) => {
      updateGroupOpacityState(groupId, value[0] ?? 1);
    },
    [groupId, updateGroupOpacityState],
  );
  return (
    <div
      ref={ref}
      className={cn("flex items-center gap-2 w-20", className)}
      {...props}
    >
      <Slider
        className="w-16 h-2"
        min={0}
        max={1}
        step={0.1}
        value={[getGroupOpacity(groupId)]}
        onValueChange={handleValueChange}
      ></Slider>
    </div>
  );
});
GroupOpacitySlider.displayName = "GroupOpacitySlider";

export const TOCWidget = React.forwardRef<HTMLDivElement, TOCWidgetProps>(
  (
    {
      data,
      onLayerVisibilityChange,
      onGroupExpandedChange,
      isGroupOpacityEnabled = false,
      isLayerOpacityEnabled = false,
      className,
      showHeader = true,
      ...props
    },
    ref,
  ) => {
    const handleLayerVisibilityChange = useCallback(
      (id: string, visible: boolean) => {
        console.log(`Layer ${id} visibility changed to:`, visible);
        onLayerVisibilityChange?.(id, visible);
      },
      [onLayerVisibilityChange],
    );

    const handleGroupExpandedChange = useCallback(
      (id: string, expanded: boolean) => {
        console.log(`Group ${id} expanded state changed to:`, expanded);
        onGroupExpandedChange?.(id, expanded);
      },
      [onGroupExpandedChange],
    );

    return (
      <TOC
        ref={ref}
        data={data}
        onLayerVisibilityChange={handleLayerVisibilityChange}
        onGroupExpandedChange={handleGroupExpandedChange}
        isGroupOpacityEnabled={isGroupOpacityEnabled}
        isLayerOpacityEnabled={isLayerOpacityEnabled}
        className={className}
        {...props}
      >
        <TOCContent>
          {showHeader ? (
            <TOCHeader>
              <TOCTitle />
              <TOCToolbar>
                <Button variant="ghost" size="sm">
                  <Layers className="h-4 w-4" />
                </Button>
              </TOCToolbar>
            </TOCHeader>
          ) : (
            <></>
          )}
          <TOCBody>
            <TOCTree />
          </TOCBody>
        </TOCContent>
      </TOC>
    );
  },
);
TOCWidget.displayName = "TOCWidget";
