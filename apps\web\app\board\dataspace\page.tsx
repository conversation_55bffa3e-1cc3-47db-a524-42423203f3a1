"use client";

import React from "react";

import SearchForm, { SearchOption } from "../_components/search-form";
import List from "./_components/tables/list";

export default function Page() {
  // 공지사항 리스트 검색 파라미터 (제목/작성자)
  const [params, setParams] = React.useState({
    nttSj: "", // 제목
    registerId: "", // 작성자
  });

  const searchOptions: SearchOption[] = [
    { label: "제목", value: "nttSj" },
    { label: "작성자", value: "registerId" },
  ];

  return (
    <>
      <SearchForm
        options={searchOptions}
        params={params}
        setParams={setParams}
        defaultField="nttSj"
      />
      <List
        pageSize={10}
        pageIndex={1}
        nttSj={params.nttSj}
        registerId={params.registerId}
      />
    </>
  );
}
