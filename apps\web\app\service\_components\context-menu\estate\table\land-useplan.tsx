"use client";

import type {
  APIRequestType,
  APIResponseType,
  EstateClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import { createColumnHelper } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import React from "react";

import { Pagination, ViewTable } from "@/components/table";

export default function LandUsePlan({
  client,
  ...props
}: APIRequestType<EstateClient["land"]["useplan"]> & {
  client: EstateClient;
}) {
  // message handler
  const t = useTranslations("estate.land.useplan");
  // Pagination States
  const [numOfRows, setNumOfRows] = React.useState<number>(props.numOfRows);
  const [pageNo, setPageNo] = React.useState<number>(props.pageNo);

  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<EstateClient["land"]["useplan"]>
  >({
    queryKey: ["land/useplan", { ...props, numOfRows, pageNo }],
    queryFn: () => client.land.useplan({ ...props, numOfRows, pageNo }),
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError || !data || typeof data.result === "string")
    return (
      <div className="text-destructive flex justify-center align-middle">
        Error loading parcel data: {error as string}
        {data && `, ${data?.result as unknown as string}`}
      </div>
    );

  const helper = createColumnHelper<(typeof data.result.resultList)[0]>();
  const columns = [
    helper.accessor("regstrSeCodeNm", {
      cell: (info) => info.getValue(),
      header: t("regstrSeCodeNm"),
    }),
    helper.accessor("cnflcAtNm", {
      cell: (info) => info.getValue(),
      header: t("cnflcAtNm"),
    }),
    helper.accessor("prposAreaDstrcCodeNm", {
      cell: (info) => info.getValue(),
      header: t("prposAreaDstrcCodeNm"),
    }),
    helper.accessor("lastUpdtDt", {
      cell: (info) => new Date(info.getValue()).toLocaleDateString(),
      header: t("lastUpdtDt"),
    }),
  ];

  return (
    <div className="flex w-full flex-col overflow-hidden overflow-y-auto">
      <ViewTable data={data.result.resultList} columns={columns} pinHeader />
      {data.result.pageInfo && (
        <Pagination
          type="server"
          pageInfo={data.result.pageInfo}
          onPageNoChange={setPageNo}
          onNumOfRowsChange={(newNumOfRows) => {
            setNumOfRows(newNumOfRows);
            setPageNo(1);
          }}
          isLoading={isLoading}
        />
      )}
    </div>
  );
}
