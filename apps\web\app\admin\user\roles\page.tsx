"use client";

import React from "react";

import SearchForm, { SearchOption } from "../../_components/search-form";
import List from "./_component/tables/list";

export default function Page() {
  // 권한정보를 포함한 사용자정보 리스트 검색 파라미터 (제목/작성자)
  const [params, setParams] = React.useState({
    userNm: "", // 사용자 이름
    userId: "", // 사용자 ID
    deptNm: "", // 부서 이름
  });

  const [pageNo, setPageNo] = React.useState(1);

  const searchOptions: SearchOption[] = [
    { label: "사용자 이름", value: "userNm" },
    { label: "사용자 아이디", value: "userId" },
    { label: "부서 이름", value: "deptNm" },
  ];

  return (
    <>
      <SearchForm
        options={searchOptions}
        params={params}
        setParams={(newParams) => {
          setParams(newParams);
          setPageNo(1);
        }}
        defaultField="userNm"
      />
      <List
        pageSize={10}
        pageIndex={pageNo}
        onPageNoChange={setPageNo}
        userNm={params.userNm}
        userId={params.userId}
        deptNm={params.deptNm}
      />
    </>
  );
}
