"use client";

import { But<PERSON> } from "@geon-ui/react/primitives/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@geon-ui/react/primitives/tooltip";
import { Download, Edit3, Save, Trash2, X } from "lucide-react";

interface RoadDetailHeaderProps {
  onEdit?: () => void;
  onDownload?: () => void;
  onDelete?: () => void;
  onSave?: () => void;
  onCancel?: () => void;
  loading?: boolean;
  isEditing?: boolean;
}

/**
 * 도로 서비스 전용 헤더 액션 버튼들
 */
export function RoadDetailHeader({
  onEdit,
  onDownload,
  onDelete,
  onSave,
  onCancel,
  loading = false,
  isEditing = false,
}: RoadDetailHeaderProps) {
  // 편집 모드일 때는 저장/취소 버튼만 표시
  if (isEditing) {
    return (
      <div className="flex items-center gap-1">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={onSave}
              disabled={loading}
              className="h-8 w-8 p-0 text-green-600 hover:bg-green-50 hover:text-green-800"
            >
              <Save className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>저장</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancel}
              disabled={loading}
              className="h-8 w-8 p-0 text-gray-600 hover:bg-gray-50 hover:text-gray-800"
            >
              <X className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>취소</TooltipContent>
        </Tooltip>
      </div>
    );
  }

  // 일반 모드일 때는 편집/다운로드/삭제 버튼 표시
  return (
    <div className="flex items-center gap-1">
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={onEdit}
            disabled={loading}
            className="h-8 w-8 p-0 text-blue-600 hover:bg-blue-50 hover:text-blue-800"
          >
            <Edit3 className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>편집</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={onDownload}
            disabled={loading}
            className="h-8 w-8 p-0 text-green-600 hover:bg-green-50 hover:text-green-800"
          >
            <Download className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>다운로드</TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={onDelete}
            disabled={loading}
            className="h-8 w-8 p-0 text-red-600 hover:bg-red-50 hover:text-red-800"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>삭제</TooltipContent>
      </Tooltip>
    </div>
  );
}
