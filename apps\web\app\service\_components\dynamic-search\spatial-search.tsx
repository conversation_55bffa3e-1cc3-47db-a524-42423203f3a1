"use client";

import { featureToWKT, useDraw, useFeatureActions } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Badge } from "@geon-ui/react/primitives/badge";
import { But<PERSON> } from "@geon-ui/react/primitives/button";
import { Circle, MapPin, Pencil, Square, X } from "lucide-react";

export type SpatialSearchType = "point" | "polygon" | "radius" | "freehand";

export type SpatialSearchOptions = {
  type: SpatialSearchType;
  wkt?: string | null; // WKT 문자열
};

export const SPATIAL_SEARCH_TYPES = [
  {
    type: "point" as SpatialSearchType,
    title: "점",
    description: "특정 지점 중심 검색",
    icon: MapPin,
    color: "#3B82F6",
  },
  {
    type: "polygon" as SpatialSearchType,
    title: "면",
    description: "다각형 영역 검색",
    icon: Square,
    color: "#10B981",
  },
  {
    type: "radius" as SpatialSearchType,
    title: "반경",
    description: "원형 반경 검색",
    icon: Circle,
    color: "#F59E0B",
  },
  {
    type: "freehand" as SpatialSearchType,
    title: "자유작도",
    description: "자유 형태 영역 검색",
    icon: Pencil,
    color: "#8B5CF6",
  },
];

export type SpatialSearchPanelProps = {
  value?: SpatialSearchOptions | null;
  onChange?: (value: SpatialSearchOptions | null) => void;
  disabled?: boolean;
  compact?: boolean;
};

// 컴팩트한 공간 검색 패널 (범용)
export function SpatialSearchPanel({
  onChange,
  disabled = false,
  compact = false,
}: SpatialSearchPanelProps) {
  const { startDrawing, stopDrawing, mode: drawingMode, isDrawing } = useDraw();
  const { deleteFeature } = useFeatureActions();

  const selectSpatialSearchType = (type: SpatialSearchType) => {
    // 이미 해당 타입으로 그리기 중이면 중단
    const isDrawingThisType =
      isDrawing &&
      ((type === "point" && drawingMode === "point") ||
        (type === "polygon" && drawingMode === "polygon") ||
        (type === "radius" && drawingMode === "circle") ||
        (type === "freehand" && drawingMode === "curve"));

    if (isDrawingThisType) {
      // 그리기 중단
      stopDrawing();
      return;
    }

    // 지도 그리기 모드 매핑
    const drawingModeMap = {
      point: "point" as const,
      polygon: "polygon" as const,
      radius: "circle" as const,
      freehand: "curve" as const, // 자유작도도 polygon으로 처리
    };

    const drawMode = drawingModeMap[type];
    const { drawend } = startDrawing(drawMode);

    // 그리기 완료 시 콜백
    drawend(async (feature: any) => {
      try {
        // ✅ 안전한 WKT 변환 (Circle → Polygon 자동 처리)
        const wkt = featureToWKT(feature);
        if (!wkt) {
          console.warn("WKT 변환 실패");
          return;
        }
        const newOptions: SpatialSearchOptions = {
          type,
          //coordinates: feature, // 그려진 도형의 좌표 정보 저장
          wkt,
        };
        onChange?.(newOptions);

        // ✅ Promise.resolve() 기반 동기화로 setTimeout 제거
        //deleteFeature(feature, "draw");
      } catch (error) {
        console.error("공간 검색 처리 실패:", error);
      } finally {
        stopDrawing();
      }
    });
  };

  return (
    <div
      className={cn("flex items-center justify-between", compact && "gap-2")}
    >
      {/* 공간 검색 버튼들 */}
      {SPATIAL_SEARCH_TYPES.map((searchType) => {
        const Icon = searchType.icon;
        const isDrawingThisType =
          isDrawing &&
          ((searchType.type === "point" && drawingMode === "point") ||
            (searchType.type === "polygon" && drawingMode === "polygon") ||
            (searchType.type === "radius" && drawingMode === "circle") ||
            (searchType.type === "freehand" && drawingMode === "curve"));

        return (
          <div key={searchType.type} className="flex justify-between">
            <Button
              type="button"
              variant={isDrawingThisType ? "default" : "outline"}
              size="sm"
              disabled={disabled}
              className={cn(
                compact ? "h-6 px-2 text-xs" : "h-7 px-2 text-xs",
                isDrawingThisType && "animate-pulse",
              )}
              onClick={() => selectSpatialSearchType(searchType.type)}
            >
              <Icon
                className={cn("mr-1", compact ? "h-2 w-2" : "h-3 w-3")}
                style={{
                  color: isDrawingThisType ? "currentColor" : searchType.color,
                }}
              />
              {searchType.title}
            </Button>
          </div>
        );
      })}
    </div>
  );
}

export type SpatialSearchStatusProps = {
  value?: SpatialSearchOptions | null;
  onClear?: () => void;
  disabled?: boolean;
};

// 공간 검색 상태 표시 컴포넌트 (헤더 등에서 사용)
export function SpatialSearchStatus({
  value,
  onClear,
  disabled = false,
}: SpatialSearchStatusProps) {
  if (!value) return null;

  const searchType = SPATIAL_SEARCH_TYPES.find((t) => t.type === value.type);
  if (!searchType) return null;

  const Icon = searchType.icon;

  return (
    <Badge
      variant="outline"
      className="gap-1 pr-1"
      style={{ borderColor: searchType.color }}
    >
      <Icon className="h-3 w-3" style={{ color: searchType.color }} />
      <span>{searchType.title}</span>

      {onClear && (
        <button
          onClick={onClear}
          disabled={disabled}
          className="focus:ring-ring ml-1 rounded-sm opacity-60 hover:opacity-100 focus:outline-none focus:ring-1 disabled:opacity-30"
        >
          <X className="h-3 w-3" />
        </button>
      )}
    </Badge>
  );
}
