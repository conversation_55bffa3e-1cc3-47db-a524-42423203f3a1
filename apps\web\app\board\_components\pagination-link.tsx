// components/PaginationLink.tsx
import Link, { LinkProps } from "next/link";

import { usePaginationParams } from "./hooks/use-pagination-params";

interface PaginationLinkProps extends LinkProps {
  children: React.ReactNode;
  className?: string;
  page?: number;
  size?: number;
}

export function PaginationLink({
  href,
  children,
  className,
  page: propPage,
  size: propSize,
  ...rest
}: PaginationLinkProps) {
  const { page: currentPage, size: currentSize } = usePaginationParams();

  const page = propPage ?? currentPage;
  const size = propSize ?? currentSize;

  const finalHref = `${href}?page=${page}&size=${size}`;

  return (
    <Link {...rest} href={finalHref} className={className}>
      {children}
    </Link>
  );
}
