"use client";

import {
  type APIResponseType,
  createGeonSmtClient,
  type SmtClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import React from "react";

type Props = { userId: string };

export default function UserDetail({ userId }: Props) {
  const client = createGeonSmtClient();
  const { data, isLoading, isError, error } = useAppQuery<
    APIResponseType<SmtClient["users"]["select"]>
  >({
    queryKey: ["smt/users/id", { userId }],
    queryFn: () => client.users.select({ userId }),
    enabled: Boolean(userId),
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError || !data || typeof data.result === "string")
    return (
      <div className="text-destructive flex justify-center align-middle">
        Error loading user: {String(error)}
        {data && `, ${String(data.result as unknown as string)}`}
      </div>
    );

  const user = data.result;

  // null이나 빈 문자열일 때 "없음" 반환하는 헬퍼 함수
  const getDisplayValue = (value: string | null | undefined) => {
    return value && value.trim() !== "" ? value : "없음";
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-50">
      <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-md">
        <div className="mb-8 text-center">
          <h1 className="text-2xl font-semibold text-gray-900">사용자 정보</h1>
        </div>

        <div className="space-y-6">
          {/* 이름 */}
          <div className="flex items-center">
            <label className="w-20 text-sm font-medium text-gray-700">
              이름
            </label>
            <input
              type="text"
              value={getDisplayValue(user.userNm)}
              readOnly
              className="flex-1 rounded-md border border-gray-300 bg-gray-50 px-3 py-2 text-gray-900"
            />
          </div>

          {/* 아이디 */}
          <div className="flex items-center">
            <label className="w-20 text-sm font-medium text-gray-700">
              아이디
            </label>
            <input
              type="text"
              value={getDisplayValue(user.userId)}
              readOnly
              className="flex-1 rounded-md border border-gray-300 bg-gray-50 px-3 py-2 text-gray-900"
            />
          </div>

          {/* 부서 */}
          <div className="flex items-center">
            <label className="w-20 text-sm font-medium text-gray-700">
              부서
            </label>
            <input
              type="text"
              value={getDisplayValue(user.insttNm)}
              readOnly
              className="flex-1 rounded-md border border-gray-300 bg-gray-50 px-3 py-2 text-gray-900"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
