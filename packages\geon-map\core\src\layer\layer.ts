import { applyLayerStyle } from "../style";
import type {
  GeoserverLayerOptions,
  LayerOptions,
  LayerType,
  ODF,
  ODF_MAP,
} from "../types";
import { LayerFactory } from "./layer-factory";

/**
 * ODF Layer 핵심 클래스
 * 레이어 생성, 관리, 스타일링을 담당
 */
export class Layer {
  map: ODF_MAP;
  odf: ODF;
  constructor(map: ODF_MAP, odf: ODF) {
    this.map = map;
    this.odf = odf;
    console.log(this.map);
    console.log(this.odf);
  }

  /**
   * 중복 레이어 체크
   */
  static checkDuplicateLayer(map: any, options: LayerOptions): string | null {
    if (!map) return null;

    const layerId =
      options.type === "geoserver"
        ? (options as GeoserverLayerOptions).layer
        : options.id;

    try {
      const existingODFLayers = map.getODFLayers();
      const existingODFLayer = existingODFLayers.find((odfLayer: any) => {
        const initialOption = odfLayer.getInitialOption();
        if (options.type === "geoserver") {
          return (
            initialOption?.params?.layer ===
            (options as GeoserverLayerOptions).layer
          );
        }
        return initialOption?.params?.id === layerId;
      });

      return existingODFLayer ? existingODFLayer.getODFId() : null;
    } catch (error) {
      console.error("Failed to check duplicate layers:", error);
      return null;
    }
  }

  /**
   * 레이어 생성 및 지도에 추가
   */
  static createAndAddLayer(map: any, options: LayerOptions): string | null {
    if (!map) {
      throw new Error("Map instance is required");
    }

    // 중복 체크
    const duplicateId = this.checkDuplicateLayer(map, options);
    if (duplicateId) {
      console.log("Duplicate layer found, skipping:", duplicateId);
      return duplicateId;
    }

    try {
      const { type, params } = LayerFactory.convertToODFParams(options);

      // ODF 레이어 생성
      const odfLayer = LayerFactory.createODFLayer(type, params);

      // 스타일 적용
      if (options.renderOptions?.style) {
        applyLayerStyle(
          odfLayer,
          options.renderOptions.style,
          type,
          params.service,
        );
      }

      // 지도에 추가
      odfLayer.setMap(map);

      // 초기 속성 적용
      const layerId = odfLayer.getODFId();
      const initialVisible = options.visible ?? true;
      const initialOpacity = options.opacity ?? 1;
      const initialZIndex = options.zIndex ?? 0;

      odfLayer.setVisible(initialVisible);
      odfLayer.setOpacity(initialOpacity);
      odfLayer.setZIndex(initialZIndex);

      // autoFit 처리
      if (options.autoFit !== false) {
        odfLayer.fit(options.fitDuration || 0);
      }

      return layerId;
    } catch (error) {
      console.error("Failed to add layer:", error);
      throw error;
    }
  }

  /**
   * 레이어 제거
   */
  static removeLayer(map: any, layerId: string): void {
    if (!map) {
      throw new Error("Map instance is required");
    }

    try {
      map.removeLayer(layerId);
    } catch (error) {
      console.error("Failed to remove layer:", error);
      throw error;
    }
  }

  /**
   * 레이어 가시성 설정
   */
  static setLayerVisible(odfLayer: any, visible: boolean): void {
    if (!odfLayer) {
      throw new Error("Layer instance is required");
    }

    try {
      odfLayer.setVisible(visible);
    } catch (error) {
      console.error("Failed to set layer visibility:", error);
      throw error;
    }
  }

  /**
   * 레이어 투명도 설정
   */
  static setLayerOpacity(odfLayer: any, opacity: number): void {
    if (!odfLayer) {
      throw new Error("Layer instance is required");
    }

    try {
      odfLayer.setOpacity(opacity);
    } catch (error) {
      console.error("Failed to set layer opacity:", error);
      throw error;
    }
  }

  /**
   * 레이어 Z-Index 설정
   */
  static setLayerZIndex(odfLayer: any, zIndex: number): void {
    if (!odfLayer) {
      throw new Error("Layer instance is required");
    }

    try {
      odfLayer.setZIndex(zIndex);
    } catch (error) {
      console.error("Failed to set layer z-index:", error);
      throw error;
    }
  }

  /**
   * 레이어 스타일 업데이트
   */
  static updateLayerStyle(
    odfLayer: any,
    style: any,
    layerType: LayerType,
    service?: string,
  ): void {
    if (!odfLayer) {
      throw new Error("Layer instance is required");
    }

    try {
      const parsedStyle = typeof style === "string" ? JSON.parse(style) : style;
      applyLayerStyle(odfLayer, parsedStyle, layerType, service);
    } catch (error) {
      console.error("Failed to update layer style:", error);
      throw error;
    }
  }

  /**
   * 레이어 필터 설정
   */
  static setLayerFilter(odfLayer: any, filterCondition: string): void {
    if (!odfLayer) {
      throw new Error("Layer instance is required");
    }

    try {
      if (!filterCondition || filterCondition.trim() === "") {
        // 필터 초기화
        odfLayer.defineQuery({ condition: null });
      } else {
        // 필터 적용
        odfLayer.defineQuery({ condition: filterCondition });
      }
    } catch (error) {
      console.error("Failed to set layer filter:", error);
      throw error;
    }
  }

  /**
   * 레이어 맞춤
   */
  static fitLayer(odfLayer: any, duration: number = 0): void {
    if (!odfLayer) {
      throw new Error("Layer instance is required");
    }

    try {
      odfLayer.fit(duration);
    } catch (error) {
      console.error("Failed to fit layer:", error);
      throw error;
    }
  }

  /**
   * 레이어 전환 (map.switchLayer API 사용)
   */
  static switchLayer(map: any, layerId: string, visible: boolean): void {
    if (!map) {
      throw new Error("Map instance is required");
    }

    try {
      map.switchLayer(layerId, visible);
    } catch (error) {
      console.error("Failed to switch layer:", error);
      throw error;
    }
  }

  /**
   * 현재 레이어 스타일 객체 가져오기
   * */
  static async getLayerStyleObject(odfLayer: any): Promise<any> {
    if (!odfLayer) {
      throw new Error("Layer instance is required");
    }
    try {
      if (odfLayer.getStyle) {
        // 단일 심볼의 경우

        return odfLayer.getStyle()?.getObject();
      } else if (odfLayer.getSLD) {
        return (
          odfLayer.getSLD()?.getObject() || null
          //(await odfLayer.getDefaultSLD())?.getObject()
        );
      } else {
        console.error("Failed to get layer Style");
        return null;
      }
    } catch (error) {
      console.error("Failed to get layer style:", error);
      throw error;
    }
  }
}
