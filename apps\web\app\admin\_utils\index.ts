import { Author, UserAuthorMgmtRequest } from "@geon-query/model";

export type AdminMenu = {
  /** route 에 표시될 서비스 이름 (중복 불가) */
  id: string;
  title: string;
  items?: Omit<AdminMenu, "items">[];
  [k: string]: any;
};

// 관리자 메뉴 목록
export const ADMIN_MENUS: AdminMenu[] = [
  {
    id: "author",
    title: "권한정보 관리",
    items: [
      { id: "business", title: "업무 담당자 권한 관리" },
      { id: "platform", title: "플랫폼 관리자 권한 관리" },
      { id: "history", title: "권한 부여 이력" },
    ],
  },
  {
    id: "user",
    title: "사용자 정보 관리",
    items: [{ id: "roles", title: "사용자 권한 및 정보 관리" }],
  },
  {
    id: "extra",
    title: "부가 정보 관리",
    items: [{ id: "extraInfo-1", title: "맞춤형 지도 관리" }],
  },
  {
    id: "statistics",
    title: "통계정보 관리",
    items: [
      { id: "statisticsInfo-1", title: "통합 통계정보 조회" },
      { id: "statisticsInfo-2", title: "시스템접속 통계 조회" },
      { id: "statisticsInfo-3", title: "접속패턴 통계 조회" },
    ],
  },
  {
    id: "log",
    title: "로그 관리",
    items: [{ id: "log-1", title: "작업로그 조회" }],
  },
];

export const ADMIN_MENU_IDS = ADMIN_MENUS.map((menu) => menu.id);

// /**
//  * Upsert 요청한 메뉴-권한 조합의 이름을 반환하는 유틸
//  * @param requestBody Upsert 요청의 RequestBody 에 담긴 @see UserMenuAuthorUpsertRequestBody
//  * @param groupingMenuAuthorCombList 메뉴-권한 조합 리스트 중 업무 담당자 권한은 그대로, 플랫폼 관리자 권한들은 하나로 묶은 형태의 리스트
//  * @returns
//  */
// export const findSelectedMenuAuthorComb = (
//   requestBody: UserAuthorMgmtRequest,
//   authorList: Author[],
// ) => {
//   return requestBody
//     .map(({ menuId, authorGroupId }) => {
//       const matchedGroup = authorList.find(
//         (author) =>
//           groupingMenuAuthorComb.menuAuthorCombItems.some(
//             (groupingMenuAuthorComb) =>
//               groupingMenuAuthorComb.menuId === menuId &&
//               groupingMenuAuthorComb.authorGroupId === authorGroupId,
//           ),
//       );
//       return matchedGroup?.displayName ?? `${menuId}/${authorGroupId}`;
//     })
//     .filter((v, i, self) => self.indexOf(v) === i)
//     .join(", ");
// };
