// app/board/dataspace/[id]/edit/page.tsx

"use client";

import {
  type APIResponseType,
  createGeonMagpClient,
  type MagpClient,
  NoticeCUDRequest,
} from "@geon-query/model";
import {
  useAppMutation,
  useAppQuery,
  useAppQueryClient,
} from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import { useParams, useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

import AttachmentReader from "@/app/board/_components/attachment-reader";
import AttachmentUploader from "@/app/board/_components/attachment-uploader";
import { usePaginationRouter } from "@/app/board/_components/hooks/use-pagination-router";

import Edit from "../../_components/tables/edit";
import { FormState } from "../../_components/tables/view";

function ynToBool(v: "Y" | "N") {
  return v === "Y";
}
function boolToYn(v: boolean): string {
  return v ? "Y" : "N";
}

//TODO 수정자 아이디 store에서 호출
const userId = "admin";

export default function Page() {
  const { id } = useParams<{ id: string }>(); // ✅ URL 파라미터 읽기
  const client = createGeonMagpClient();
  const router = useRouter();

  const { data, isLoading, isError } = useAppQuery<
    APIResponseType<MagpClient["dataspace"]["select"]>
  >({
    queryKey: ["magp/dataspace", { nttId: id }],
    queryFn: () => client.dataspace.select({ nttId: id }),
    enabled: Boolean(id),
  });

  const qc = useAppQueryClient();
  const { push } = usePaginationRouter();

  const [form, setForm] = useState<FormState | null>(null);
  const [atchmnflId, setAtchmnflId] = useState<string | null>(null);

  useEffect(() => {
    const item = data && typeof data.result !== "string" ? data.result : null;
    if (item) {
      setForm({
        nttSj: item.nttSj ?? "",
        nttCn: item.nttCn ?? "",
        upperExpsrAt: ynToBool(item.upperExpsrAt),
        smsSndngAt: ynToBool(item.smsSndngAt),
        othbcAt: ynToBool(item.othbcAt),
        pstgBeginDt: item.pstgBeginDt?.slice(0, 16) ?? "",
        linkUrl: item.linkUrl ?? "",
        popupAt: ynToBool(item.popupAt),
        popupBeginDt: item.popupBeginDt?.slice(0, 16) ?? "",
        popupEndDt: item.popupEndDt?.slice(0, 16) ?? "",
        popupPortalExpsrAt: ynToBool(item.popupPortalExpsrAt),
        popupInsttExpsrAt: ynToBool(item.popupInsttExpsrAt),
        registerId: item.registerId,
        updusrId: userId,
        atchmnflId: item.atchmnflId ?? null,
        attachment: item.attachment ?? [], // 상세 조회 시 내려오는 첨부파일 목록
      });

      // 현재 첨부파일 ID 세팅
      setAtchmnflId(item.atchmnflId ?? null);
    }
  }, [data]);

  const updMutation = useAppMutation({
    mutationFn: async (payload: NoticeCUDRequest) =>
      client.dataspace.update(payload),
    onSuccess: () => {
      qc.invalidateQueries({ queryKey: ["magp/dataspace"] });
      router.push(`/board/dataspace/${id}`);
    },
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError || !data || typeof data.result === "string") {
    return <div className="text-red-500">Error loading data</div>;
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!form) return;

    updMutation.mutate({
      nttId: id,
      nttSj: form.nttSj,
      nttCn: form.nttCn,
      popupAt: boolToYn(form.popupAt as boolean) || "N",
      popupBeginDt: form.popupBeginDt
        ? new Date(form.popupBeginDt).toISOString()
        : null,
      popupEndDt: form.popupEndDt
        ? new Date(form.popupEndDt).toISOString()
        : null,
      updusrId: userId,
      // 새 파일 업로드가 있으면 새 ID, 없으면 기존 ID
      atchmnflId: atchmnflId ?? form.atchmnflId ?? null,
    });
  };

  return (
    <div className="mx-auto w-full max-w-5xl space-y-6 p-4">
      <h1 className="text-xl font-semibold">데이터공간 수정</h1>
      {form !== null && (
        <Edit
          form={form}
          setForm={setForm as React.Dispatch<React.SetStateAction<FormState>>}
          onSubmit={handleSubmit}
          isPending={updMutation.isPending}
          onCancel={() => push(`/board/dataspace/${id}`)}
        >
          {/* 기존 첨부파일 보여주기 */}
          {form.attachment && form.attachment.length > 0 && (
            <AttachmentReader
              attachment={form.attachment ?? []}
              editable
              onRemove={() => {
                setForm((prev) =>
                  prev ? { ...prev, attachment: [], atchmnflId: null } : prev,
                );
                setAtchmnflId(null); // ✅ 같이 초기화
              }}
            />
          )}
          {/* 새로운 업로드 → 새로운 atchmnflId 발급 */}
          <AttachmentUploader
            registerId={userId}
            onUploaded={(id: string | null) => setAtchmnflId(id)}
          />
        </Edit>
      )}
    </div>
  );
}
