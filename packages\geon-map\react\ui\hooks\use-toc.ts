import { useLayer } from "@geon-map/react-odf";
import { useCallback, useEffect, useState } from "react";

import { GroupNode, ItemNode, TOCNode, TOCOptions } from "../types";

export const useTOC = (options: TOCOptions = {}) => {
  const {
    data,
    onLayerVisibilityChange,
    onGroupExpandedChange,
    isGroupOpacityEnabled = false,
    isLayerOpacityEnabled = false,
  } = options;
  const [tocNodes, setTOCNodes] = useState<TOCNode[]>(data || []);
  const { setVisible, fitToLayer, setOpacity, getOpacity } = useLayer();

  // ===== 공통 유틸리티 함수들 =====

  /**
   * 트리에서 특정 조건에 맞는 노드를 찾는 범용 함수
   */
  const findTOCNode = useCallback(
    (
      predicate: (node: TOCNode) => boolean,
      nodes: TOCNode[] = tocNodes,
    ): TOCNode | null => {
      for (const node of nodes) {
        if (predicate(node)) {
          return node;
        }
        if (node.type === "group" && node.children) {
          const found = findTOCNode(predicate, node.children);
          if (found) return found;
        }
      }
      return null;
    },
    [tocNodes],
  );

  /**
   * 트리를 순회하며 각 노드를 변환하는 범용 함수
   */
  const transformTree = useCallback(
    (transformer: (node: TOCNode) => TOCNode, nodes: TOCNode[]): TOCNode[] => {
      return nodes.map((node) => {
        const transformedNode = transformer(node);
        if (transformedNode.type === "group") {
          const groupNode = transformedNode as GroupNode;
          return {
            ...groupNode,
            children: groupNode.children
              ? transformTree(transformer, groupNode.children)
              : undefined,
          };
        } else {
          return transformedNode;
        }
      });
    },
    [],
  );

  /**
   * 트리에서 특정 조건에 맞는 모든 노드를 수집하는 범용 함수
   */
  const collectNodes = useCallback(
    (
      predicate: (node: TOCNode) => boolean,
      nodes: TOCNode[] = tocNodes,
    ): TOCNode[] => {
      const result: TOCNode[] = [];

      const collect = (nodeList: TOCNode[]) => {
        for (const node of nodeList) {
          if (predicate(node)) {
            result.push(node);
          }
          if (node.type === "group" && node.children) {
            collect(node.children);
          }
        }
      };

      collect(nodes);
      return result;
    },
    [tocNodes],
  );

  /**
   * 특정 노드의 모든 하위 노드를 수집하는 함수
   */
  const getDescendants = useCallback(
    (
      nodeId: string,
      predicate: (node: TOCNode) => boolean = () => true,
    ): TOCNode[] => {
      const target = findTOCNode((n) => n.id === nodeId);
      // 그룹이 아니거나, 자식이 없으면 빈 배열
      if (!target || target.type !== "group" || !target.children?.length)
        return [];
      // 그룹으로 좁혀졌으니 안전하게 children 사용
      return collectNodes(predicate, target.children);
    },
    [findTOCNode, collectNodes],
  );
  // ===== 초기화 로직 =====

  /**
   * 초기 확장된 그룹들 수집
   */
  const getInitialExpandedGroups = useCallback(
    (layers: TOCNode[]): Set<string> => {
      const expandedNodes = collectNodes(
        (node) => node.type === "group" && node.expanded === true,
        layers,
      );
      return new Set(expandedNodes.map((node) => node.id));
    },
    [collectNodes],
  );

  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(() =>
    getInitialExpandedGroups(data || []),
  );

  useEffect(() => {
    if (data && data.length > 0) {
      // if (!isGroupOpacityEnabled) {
      //   setTOCNodes(data);
      //   return;
      // }

      let updatedData = data;

      // 그룹 투명도 초기화 로직
      const updateDataWithTopLevelOpacity = (layers: TOCNode[]): TOCNode[] => {
        return layers.map((layer) => {
          if (layer.type === "group") {
            const topLevelOpacity = layer.opacity ?? 1;
            const updateAllGroupsOpacity = (
              children: TOCNode[],
              topLevelOpacity: number,
            ): TOCNode[] => {
              return children.map((child) => ({
                ...child,
                opacity: topLevelOpacity,
                children:
                  child.type === "group" && child.children
                    ? updateAllGroupsOpacity(child.children, topLevelOpacity)
                    : undefined,
              }));
            };

            const groupLayer = layer as GroupNode;
            return {
              ...groupLayer,
              children: groupLayer.children
                ? updateAllGroupsOpacity(groupLayer.children, topLevelOpacity)
                : undefined,
            };
          }
          return layer;
        });
      };
      //그룹 투명도 상속 체인 적용
      const applyGroupOpacityInheritance = (
        layers: TOCNode[],
        parentOpacity?: number,
      ) => {
        layers.forEach((layer) => {
          const effectiveOpacity = layer.opacity ?? parentOpacity ?? 1;

          if (layer.type === "layer" && layer.layerId) {
            setOpacity(layer.layerId, effectiveOpacity);
          }

          if (layer.type === "group" && layer.children) {
            const nextInheritedOpacity =
              layer.opacity !== undefined ? layer.opacity : parentOpacity;
            applyGroupOpacityInheritance(layer.children, nextInheritedOpacity);
          }
        });
      };
      // TOCNode의 opacity를 사용하여 초기화하는 함수
      const applyTOCNodeOpacitySettings = (layers: TOCNode[]) => {
        layers.forEach((layer) => {
          if (layer.type === "layer" && layer.layerId) {
            // TOCNode의 opacity 또는 Layer 객체의 기본 opacity 사용
            const layerOpacity =
              layer.opacity ?? getOpacity(layer.layerId) ?? 1;
            setOpacity(layer.layerId, layerOpacity);
          }

          if (layer.type === "group" && layer.children) {
            applyTOCNodeOpacitySettings(layer.children);
          }
        });
      };

      if (isGroupOpacityEnabled) {
        updatedData = updateDataWithTopLevelOpacity(updatedData);
        applyGroupOpacityInheritance(updatedData); // 변경됨
      } else if (isLayerOpacityEnabled) {
        applyTOCNodeOpacitySettings(updatedData); // 변경됨
      }
      setTOCNodes(updatedData);
    }
  }, [
    data,
    setOpacity,
    isGroupOpacityEnabled,
    isLayerOpacityEnabled,
    getOpacity,
  ]);

  useEffect(() => {
    const newExpandedGroups = getInitialExpandedGroups(tocNodes);
    setExpandedGroups(newExpandedGroups);
  }, [getInitialExpandedGroups, tocNodes]);
  // ===== 기존 함수들을 공통 유틸리티로 리팩토링 =====

  /**
   * ID로 노드 찾기
   */
  const findTOCNodeById = useCallback(
    (id: string): TOCNode | null => {
      return findTOCNode((node) => node.id === id);
    },
    [findTOCNode],
  );

  /**
   * 그룹에 포함된 모든 레이어노드 가져오기
   */
  const getTOCNodesInGroup = useCallback(
    (groupId: string): TOCNode[] => {
      return getDescendants(groupId, (node) => node.type === "layer");
    },
    [getDescendants],
  );

  /**
   * 그룹에 포함된 모든 layerId 가져오기
   * */
  const getLayerIdsInGroup = useCallback(
    (groupId: string): string[] => {
      return getTOCNodesInGroup(groupId)
        .filter((node): node is ItemNode => node.type === "layer")
        .filter((node) => node.layerId)
        .map((node) => node.layerId!);
    },
    [getTOCNodesInGroup],
  );

  /**
   * 그룹 투명도 가져오기
   */
  const getGroupOpacity = useCallback(
    (id: string): number => {
      const node = findTOCNode((node) => node.id === id);
      return node?.opacity ?? 1;
    },
    [findTOCNode],
  );

  /**
   * 레이어 투명도 가져오기
   */
  const getLayerOpacity = useCallback(
    (id: string): number => {
      const node = findTOCNode((node) => node.id === id);
      return node?.opacity ?? 1;
    },
    [findTOCNode],
  );

  // ===== 복잡한 업데이트 로직들 =====

  /**
   * 가시성 토글
   */
  const handleToggleVisibility = useCallback(
    (id: string, visible: boolean) => {
      const updateVisibility = (node: TOCNode): TOCNode => {
        if (node.id === id) {
          if (node.type === "group") {
            // 그룹: 모든 하위 요소들을 동일한 상태로 설정
            const updateChildren = (children: TOCNode[]): TOCNode[] => {
              return children.map((child) => {
                if (child.type === "layer" && child.layerId) {
                  setVisible(child.layerId, visible);
                }
                return {
                  ...child,
                  visible,
                  children:
                    child.type === "group" && child.children
                      ? updateChildren(child.children)
                      : undefined,
                };
              });
            };

            const groupNode = node as GroupNode;
            return {
              ...groupNode,
              visible,
              children: groupNode.children
                ? updateChildren(groupNode.children)
                : undefined,
            };
          } else {
            // 개별 레이어
            if (node.type === "layer" && node.layerId) {
              setVisible(node.layerId, visible);
            }
            return { ...node, visible };
          }
        }
        return node;
      };

      // 부모 그룹들의 가시성 자동 업데이트 로직
      const updateParentVisibility = (nodes: TOCNode[]): TOCNode[] => {
        return nodes.map((node) => {
          if (node.type === "group" && node.children) {
            const updatedChildren = updateParentVisibility(node.children);

            // 하위 요소 중 변경된 요소가 있는지 확인
            const hasChangedChild = updatedChildren.some(
              (child) =>
                child.id === id ||
                (child.type === "group" &&
                  child.children &&
                  hasDescendant(child, id)),
            );

            if (hasChangedChild) {
              const anyChildVisible = updatedChildren.some(
                (child) => child.visible,
              );
              return {
                ...node,
                visible: anyChildVisible,
                children: updatedChildren,
              };
            }

            return { ...node, children: updatedChildren };
          }
          return node;
        });
      };

      const hasDescendant = (node: TOCNode, targetId: string): boolean => {
        if (node.type !== "group" || !node.children) return false;
        return node.children.some(
          (child) => child.id === targetId || hasDescendant(child, targetId),
        );
      };

      const updatedLayers = transformTree(updateVisibility, tocNodes);
      const finalLayers = updateParentVisibility(updatedLayers);

      setTOCNodes(finalLayers);
      onLayerVisibilityChange?.(id, visible);
    },
    [tocNodes, onLayerVisibilityChange, setVisible, transformTree],
  );
  /**
   * 확장 상태 토글
   */
  const handleToggleExpanded = useCallback(
    (id: string, expanded: boolean) => {
      setExpandedGroups((prev) => {
        const newSet = new Set(prev);
        if (expanded) {
          newSet.add(id);
        } else {
          newSet.delete(id);
        }
        return newSet;
      });

      const updateExpanded = (node: TOCNode): TOCNode => {
        if (node.id === id && node.type === "group") {
          return { ...node, expanded };
        }
        return node;
      };

      setTOCNodes(transformTree(updateExpanded, tocNodes));
      onGroupExpandedChange?.(id, expanded);
    },
    [tocNodes, onGroupExpandedChange, transformTree],
  );

  // ===== 간단한 CRUD 함수들 =====

  const addTOCNode = useCallback((layer: TOCNode) => {
    setTOCNodes((prev) => [...prev, layer]);
  }, []);

  const removeTOCNode = useCallback((id: string) => {
    const removeNode = (node: TOCNode): TOCNode | null => {
      if (node.id === id) {
        return null; // 이 노드를 제거
      }
      if (node.type === "group") {
        const groupNode = node as GroupNode;
        return {
          ...groupNode,
          children: groupNode.children
            ? (groupNode.children.map(removeNode).filter(Boolean) as TOCNode[])
            : undefined,
        };
      }
      return node;
    };

    setTOCNodes((prev) => prev.map(removeNode).filter(Boolean) as TOCNode[]);
  }, []);

  const updateTOCNode = useCallback(
    (id: string, updates: Partial<TOCNode>) => {
      const updateNode = (node: TOCNode): TOCNode => {
        if (node.id === id) {
          return { ...node, ...updates };
        }
        return node;
      };

      setTOCNodes(transformTree(updateNode, tocNodes));
    },
    [tocNodes, transformTree],
  );

  const zoomToLayer = useCallback(
    (id: string, duration: number = 1): boolean => {
      const node = findTOCNodeById(id);
      if (!node || node.type !== "layer" || !node.layerId) return false;
      return fitToLayer(node.layerId, duration);
    },
    [findTOCNodeById, fitToLayer],
  );

  /**
   * 그룹 투명도 상태값 변경
   */
  const updateGroupOpacityState = useCallback(
    (id: string, opacity: number) => {
      const isTopLevelGroup = tocNodes.some((layer) => layer.id === id);

      const updateOpacity = (node: TOCNode): TOCNode => {
        if (node.id === id) {
          if (isTopLevelGroup) {
            // 최상위 그룹: 모든 하위 요소들의 opacity 변경
            const updateAllChildren = (children: TOCNode[]): TOCNode[] => {
              return children.map((child) => {
                // if (child.type === "layer") {
                //   setOpacity(child.id, opacity);
                // }
                return {
                  ...child,
                  opacity,
                  children:
                    child.type === "group" && child.children
                      ? updateAllChildren(child.children)
                      : undefined,
                };
              });
            };

            const groupNode = node as GroupNode;
            return {
              ...groupNode,
              opacity,
              children: groupNode.children
                ? updateAllChildren(groupNode.children)
                : undefined,
            };
          } else {
            // 중간 그룹: 직속 하위 레이어들만 변경
            const updateDirectChildren = (children: TOCNode[]): TOCNode[] => {
              return children.map((child) => {
                if (child.type === "layer" && child.layerId) {
                  setOpacity(child.layerId, opacity);
                  return { ...child, opacity };
                }
                return child; // 하위 그룹은 변경하지 않음
              });
            };

            const groupNode = node as GroupNode;
            return {
              ...groupNode,
              opacity,
              children: groupNode.children
                ? updateDirectChildren(groupNode.children)
                : undefined,
            };
          }
        }
        return node;
      };

      setTOCNodes(transformTree(updateOpacity, tocNodes));
    },
    [tocNodes, setOpacity, transformTree],
  );

  /**
   * 레이어 투명도 상태값 변경
   */
  const updateLayerOpacityState = useCallback(
    (id: string, opacity: number) => {
      const updateOpacity = (node: TOCNode): TOCNode => {
        if (node.id === id && node.type === "layer") {
          return { ...node, opacity };
        }
        return node;
      };

      setTOCNodes(transformTree(updateOpacity, tocNodes));
    },
    [tocNodes, setOpacity, transformTree],
  );

  return {
    tocNodes,
    expandedGroups,
    handleToggleVisibility,
    handleToggleExpanded,
    addTOCNode,
    removeTOCNode,
    updateTOCNode,
    findTOCNode,
    findTOCNodeById,
    zoomToLayer,
    getGroupOpacity,
    updateGroupOpacityState,
    updateLayerOpacityState,
    getLayerOpacity,
    getTOCNodesInGroup,
    getLayerIdsInGroup,
  };
};
