import type {TOCNode} from "@geon-map/react-ui/types";

export const TOC_DATA: TOCNode[] = [
  {
    "id": "service-road",
    "name": "도로 관리 시스템",
    "type": "group",
    "visible": true,
    "children": [
      {
        "id": "service-road-managed",
        "name": "관리 대상 레이어",
        "type": "group",
        "visible": true,
        "children": [

        ]
      },
      {
        "id": "service-road-view",
        "name": "조회 대상 레이어",
        "type": "group",
        "visible": true,
        "children": [

        ]
      }
    ]
  }
]
