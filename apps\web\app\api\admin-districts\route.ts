import { NextRequest, NextResponse } from "next/server";
import { BASE_URL, createGeonAddrgeoClient, crtfckey } from "@geon-query/model";

const MUAN_SIGUNGU_CODE = "46840"; // 전라남도 무안군 코드

const apiClient = createGeonAddrgeoClient({
  baseUrl: BASE_URL,
  crtfckey: crtfckey,
});

// 공통 에러 응답 생성 함수
const createErrorResponse = (message: string, status: number = 500) => {
  return NextResponse.json({ error: message }, { status });
};

// 공통 API 응답 검증 함수
const validateApiResponse = (response: any) => {
  return response && response.code === 200 && response.result && response.result.length > 0;
};

export async function GET(request: NextRequest) {
  try {
    // 읍면동 목록 조회
    const emdResponse = await apiClient.administ.emdList({
      retGeom: false,
      targetSrid: "5186",
      sigCd: MUAN_SIGUNGU_CODE,
    });

    if (!validateApiResponse(emdResponse)) {
      throw new Error("Failed to fetch admin districts list");
    }

    // 읍면동 목록 변환
    const emdList = emdResponse.result.map(({ cd, korNm, geom }) => ({
      value: cd,
      label: korNm,
      geom,
    }));

    return NextResponse.json(emdList);
  } catch (error) {
    console.error("Error fetching admin districts:", error);
    return createErrorResponse("Internal server error");
  }
}

// 특정 행정구역 상세 정보 조회
export async function POST(request: NextRequest) {
  try {
    const { code } = await request.json();

    // 요청 타입에 따른 API 호출
    const response = await (code ?
      // 특정 읍면동 조회
      apiClient.administ.emd({
        retGeom: true,
        targetSrid: "5186",
        emdCd: code,
      }) :
      // 전체 시군구 조회 (무안군)
      apiClient.administ.sgg({
        retGeom: true,
        targetSrid: "5186",
        sigCd: MUAN_SIGUNGU_CODE,
      })
    );

    if (!validateApiResponse(response)) {
      return createErrorResponse(`Admin district not found: ${code || 'ALL'}`, 404);
    }

    const district = response.result[0];
    if (!district) {
      return createErrorResponse(`Admin district data is empty: ${code || 'ALL'}`, 404);
    }

    // 응답 데이터 구성
    const detail = {
      code: district.cd,
      name: district.korNm,
      fullName: code ? `무안군 ${district.korNm}` : "무안군 전체",
      coordinates: {
        lat: parseFloat(district.y || "0"),
        lng: parseFloat(district.x || "0"),
      },
      geom: district.geom,
    };

    return NextResponse.json(detail);
  } catch (error) {
    console.error("Error fetching admin district detail:", error);
    return createErrorResponse("Internal server error");
  }
}
