/**
 * Geon Map UI Color System
 * Semantic color tokens for the design system
 * Business variables are defined in preset.css
 */

@theme {
  /* Geon Deeper Blue Theme System - Refined for better harmony and vibrancy */
  --color-geon-primary: oklch(0.62 0.18 240);
  --color-geon-primary-foreground: oklch(0.99 0.02 240);
  --color-geon-secondary: oklch(0.92 0.07 240);
  --color-geon-secondary-foreground: oklch(0.28 0.14 240);
  --color-geon-accent: oklch(0.95 0.05 240);
  --color-geon-accent-foreground: oklch(0.22 0.17 240);
  --color-geon-background: oklch(0.98 0.04 240);
  --color-geon-foreground: oklch(0.18 0.14 240);
  --color-geon-muted: oklch(0.94 0.05 240);
  --color-geon-muted-foreground: oklch(0.48 0.09 240);
  --color-geon-border: oklch(0.88 0.07 240);
  --color-geon-input: oklch(0.9 0.06 240);
  --color-geon-popover: oklch(0.99 0.03 240);
  --color-geon-popover-foreground: oklch(0.14 0.17 240);
  --color-geon-card: oklch(0.98 0.04 240);
  --color-geon-card-foreground: oklch(0.18 0.14 240);
  --color-geon-destructive: oklch(0.52 0.24 20);
  --color-geon-destructive-foreground: oklch(0.99 0.02 20);
  --color-geon-ring: oklch(0.62 0.18 240);

  /* Toolbar Specific Colors - Refined for subtlety */
  --color-geon-toolbar: oklch(0.99 0.02 240 / 0.96);
  --color-geon-toolbar-border: oklch(0.84 0.09 240 / 0.3);
  --color-geon-toolbar-shadow: oklch(0.18 0.14 240 / 0.12);

  /* Refined Interactive States - Smoother transitions */
  --color-geon-hover: oklch(0.9 0.09 240);
  --color-geon-hover-foreground: oklch(0.18 0.17 240);
  --color-geon-active: oklch(0.84 0.13 240);
  --color-geon-active-foreground: oklch(0.12 0.2 240);
  --color-geon-pressed: oklch(0.8 0.16 240);
  --color-geon-pressed-foreground: oklch(0.08 0.22 240);
}

.dark {
  /* Geon Dark Deeper Blue Theme - Improved depth and readability */
  --color-geon-primary: oklch(0.75 0.2 240);
  --color-geon-primary-foreground: oklch(0.04 0.06 240);
  --color-geon-secondary: oklch(0.25 0.09 240);
  --color-geon-secondary-foreground: oklch(0.9 0.05 240);
  --color-geon-accent: oklch(0.2 0.09 240);
  --color-geon-accent-foreground: oklch(0.94 0.04 240);
  --color-geon-background: oklch(0.08 0.06 240);
  --color-geon-foreground: oklch(0.94 0.04 240);
  --color-geon-muted: oklch(0.15 0.09 240);
  --color-geon-muted-foreground: oklch(0.68 0.07 240);
  --color-geon-border: oklch(0.25 0.11 240);
  --color-geon-input: oklch(0.2 0.09 240);
  --color-geon-popover: oklch(0.11 0.09 240);
  --color-geon-popover-foreground: oklch(0.97 0.03 240);
  --color-geon-card: oklch(0.1 0.07 240);
  --color-geon-card-foreground: oklch(0.94 0.04 240);
  --color-geon-destructive: oklch(0.6 0.22 20);
  --color-geon-destructive-foreground: oklch(0.96 0.03 20);
  --color-geon-ring: oklch(0.75 0.2 240);

  /* Dark Toolbar Specific Colors - Softer shadows */
  --color-geon-toolbar: oklch(0.11 0.09 240 / 0.96);
  --color-geon-toolbar-border: oklch(0.3 0.13 240 / 0.4);
  --color-geon-toolbar-shadow: oklch(0.04 0.03 240 / 0.5);

  /* Dark Refined Interactive States - Enhanced feedback */
  --color-geon-hover: oklch(0.25 0.11 240);
  --color-geon-hover-foreground: oklch(0.97 0.03 240);
  --color-geon-active: oklch(0.35 0.16 240);
  --color-geon-active-foreground: oklch(0.99 0.02 240);
  --color-geon-pressed: oklch(0.4 0.19 240);
  --color-geon-pressed-foreground: oklch(1 0 0);
}
