"use client";

import { useMap } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Minus, Plus } from "lucide-react";
import * as React from "react";

import { Button } from "../toolbar/base/button";

export interface ZoomControlProps extends React.HTMLAttributes<HTMLDivElement> {
  /** 줌 단계 */
  zoomStep?: number;
  /** 최소 줌 레벨 */
  minZoom?: number;
  /** 최대 줌 레벨 */
  maxZoom?: number;
  /** 스케일 표시 여부 */
  showScale?: boolean;
}

export const ZoomControl = React.forwardRef<HTMLDivElement, ZoomControlProps>(
  (
    {
      zoomStep = 1,
      minZoom = 1,
      maxZoom = 20,
      showScale = true,
      className,
      ...props
    },
    ref,
  ) => {
    const { map } = useMap();
    const [currentZoom, setCurrentZoom] = React.useState<number>(10);
    const [currentScale, setCurrentScale] = React.useState<string>("");

    // 줌 레벨 및 스케일 모니터링
    React.useEffect(() => {
      if (!map) return;

      const updateZoomAndScale = () => {
        const zoom = map.getView().getZoom();
        const resolution = map.getView().getResolution();

        if (zoom !== undefined) {
          setCurrentZoom(zoom);
        }

        // 스케일 계산 (scale-widget의 로직 사용)
        if (resolution) {
          const scale = (resolution * 100) / 1000;
          const scaleText = `${scale.toFixed(2)}km`;
          setCurrentScale(scaleText);
        }
      };

      // 초기 값 설정
      updateZoomAndScale();

      // 해상도 변경 이벤트 리스너
      map.getView().on("change:resolution", updateZoomAndScale);

      return () => {
        map.getView().un("change:resolution", updateZoomAndScale);
      };
    }, [map]);

    // 줌 인
    const handleZoomIn = React.useCallback(() => {
      if (!map) return;
      const view = map.getView();
      const currentZoom = view.getZoom();
      if (currentZoom !== undefined && currentZoom < maxZoom) {
        view.animate({
          zoom: Math.min(currentZoom + zoomStep, maxZoom),
          duration: 250,
        });
      }
    }, [map, zoomStep, maxZoom]);

    // 줌 아웃
    const handleZoomOut = React.useCallback(() => {
      if (!map) return;
      const view = map.getView();
      const currentZoom = view.getZoom();
      if (currentZoom !== undefined && currentZoom > minZoom) {
        view.animate({
          zoom: Math.max(currentZoom - zoomStep, minZoom),
          duration: 250,
        });
      }
    }, [map, zoomStep, minZoom]);

    const canZoomIn = currentZoom < maxZoom;
    const canZoomOut = currentZoom > minZoom;

    return (
      <div
        ref={ref}
        className={cn(
          "absolute bottom-4 left-4 z-50 flex items-center gap-1",
          "bg-geon-background/30 backdrop-blur-sm border border-geon-border rounded-lg shadow-md p-1",
          className,
        )}
        {...props}
      >
        {/* 줌 아웃 버튼 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleZoomOut}
          disabled={!canZoomOut}
          className="h-8 w-8 p-0 cursor-pointer"
          title="축소"
        >
          <Minus className="h-4 w-4" />
        </Button>

        {/* 스케일 표시 */}
        {showScale && currentScale && (
          <div className="px-2 py-1 text-xs font-medium text-geon-foreground min-w-[3rem] text-center">
            {currentScale}
          </div>
        )}

        {/* 줌 인 버튼 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleZoomIn}
          disabled={!canZoomIn}
          className="h-8 w-8 p-0 cursor-pointer"
          title="확대"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>
    );
  },
);

ZoomControl.displayName = "ZoomControl";
