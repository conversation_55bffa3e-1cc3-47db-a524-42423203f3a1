"use client";

import React, { useEffect } from "react";

import { useControlsConfig } from "../../contexts/controls-config-context";

/**
 * BaseLayerProvider 설정 옵션
 */
export interface BaseLayerProviderOptions {
  /** BaseLayer Control 초기화 옵션 */
  baseLayerOptions?: {
    baseLayers?: any;
    activeLayerId?: string;
    isLoading?: boolean;
    // 기존 호환성용 (deprecated)
    basemapList?: any;
    urls?: any;
  };
  /** 자동 초기화 여부 (기본: true) */
  autoInitialize?: boolean;
  /** 에러 발생 시 콜백 */
  onError?: (error: Error) => void;
}

/**
 * 🎯 BaseLayerProvider (Basemap Control 설정 전용)
 *
 * Basemap Control 설정을 ControlsProvider에 전달하는 Config Provider입니다.
 * 실제 초기화는 ControlsProvider에서 수행됩니다.
 *
 * @example
 * ```tsx
 * <MapProvider>
 *   <ControlsProvider>
 *     <BaseLayerProvider baseLayerOptions={{ baseLayers: [...] }}>
 *       <BasemapSwitcher />
 *     </BaseLayerProvider>
 *   </ControlsProvider>
 * </MapProvider>
 * ```
 */
export function BaseLayerProvider({
  children,
  baseLayerOptions = {},
  autoInitialize = true,
  onError,
}: React.PropsWithChildren<BaseLayerProviderOptions>) {
  const { updateConfig } = useControlsConfig();

  useEffect(() => {
    // Controls Config에 BaseLayer 설정 등록
    updateConfig({
      baseLayerOptions,
      autoInitialize,
      onError,
    });
  }, [baseLayerOptions, autoInitialize, onError, updateConfig]);

  return <>{children}</>;
}
