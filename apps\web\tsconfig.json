{"extends": "@config/typescript/nextjs.json", "compilerOptions": {"plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"]}, "customConditions": ["development"], "allowArbitraryExtensions": true}, "include": ["**/*.ts", "**/*.tsx", "next-env.d.ts", "next.config.js", ".next/types/**/*.ts", ".storybook", "components/widget/toc-widget-package.tsx.backup", "app/service/_components/widgets/toc-widget-service.tsx.backup"], "exclude": ["node_modules", ".next"]}