"use client";

import { DrawingMode } from "@geon-map/core";
import { useDraw } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Popover, PopoverTrigger } from "@geon-ui/react/primitives/popover";
import {
  Circle,
  Edit3,
  MapPin,
  Minus,
  Square,
  Triangle,
  X,
} from "lucide-react";
import * as React from "react";

import { Button } from "./base/button";
import {
  ToolbarContent,
  ToolbarItem,
  ToolbarTrigger,
} from "./base/toolbar-item";

// Drawing Mode 타입 (DrawingMode 확장)
export type DrawMode = DrawingMode;

// 그리기 도구 설정
const DRAWING_TOOLS = [
  {
    id: "point",
    mode: "point" as DrawMode,
    label: "포인트",
    icon: MapPin,
    description: "지점을 표시합니다",
    color: "text-blue-500",
  },
  {
    id: "lineString",
    mode: "lineString" as DrawMode,
    label: "선",
    icon: Minus,
    description: "선을 그립니다",
    color: "text-green-500",
  },
  {
    id: "polygon",
    mode: "polygon" as DrawMode,
    label: "다각형",
    icon: Triangle,
    description: "다각형을 그립니다",
    color: "text-purple-500",
  },
  {
    id: "box",
    mode: "box" as DrawMode,
    label: "사각형",
    icon: Square,
    description: "사각형을 그립니다",
    color: "text-orange-500",
  },
  {
    id: "circle",
    mode: "circle" as DrawMode,
    label: "원",
    icon: Circle,
    description: "원을 그립니다",
    color: "text-pink-500",
  },
] as const;

// Context for ToolbarDraw
interface DrawingContextValue {
  startDrawing: (mode: DrawMode) => any;
  stopDrawing: () => void;
  mode: DrawMode | null;
  isDrawing: boolean;
}

const DrawingContext = React.createContext<DrawingContextValue | null>(null);

const useDrawingContext = () => {
  const context = React.useContext(DrawingContext);
  if (!context) {
    throw new Error("ToolbarDraw components must be used within ToolbarDraw");
  }
  return context;
};

// Props for compound components
export interface ToolbarDrawProps
  extends React.HTMLAttributes<HTMLDivElement> {}

export interface ToolbarDrawTriggerProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 툴팁 텍스트 */
  tooltip?: string;
  /** 버튼 크기 */
  size?: "sm" | "lg" | "default" | "icon";
}

export interface ToolbarDrawContentProps
  extends React.HTMLAttributes<HTMLDivElement> {}

export interface ToolbarDrawToolProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 그리기 모드 */
  mode: DrawMode;
}

export interface ToolbarDrawActionProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 액션 타입 */
  action: "stop";
}

// Main ToolbarDraw Container (Context Provider)
export const ToolbarDraw = React.forwardRef<HTMLDivElement, ToolbarDrawProps>(
  ({ className, children, ...props }, ref) => {
    const { startDrawing, stopDrawing, mode, isDrawing } = useDraw();

    const contextValue = React.useMemo(
      () => ({
        startDrawing,
        stopDrawing,
        mode,
        isDrawing,
      }),
      [startDrawing, stopDrawing, mode, isDrawing],
    );

    return (
      <DrawingContext.Provider value={contextValue}>
        <ToolbarItem ref={ref} className={className} {...props}>
          <Popover>
            <PopoverTrigger asChild>
              {React.Children.toArray(children).find(
                (child) =>
                  React.isValidElement(child) &&
                  child.type === ToolbarDrawTrigger,
              )}
            </PopoverTrigger>
            {React.Children.toArray(children).find(
              (child) =>
                React.isValidElement(child) &&
                child.type === ToolbarDrawContent,
            )}
          </Popover>
        </ToolbarItem>
      </DrawingContext.Provider>
    );
  },
);

ToolbarDraw.displayName = "ToolbarDraw";

// ToolbarDrawTrigger Component
export const ToolbarDrawTrigger = React.forwardRef<
  HTMLButtonElement,
  ToolbarDrawTriggerProps
>(
  (
    {
      tooltip = "그리기 도구",
      size = "default",
      className,
      children,
      ...props
    },
    ref,
  ) => {
    const { mode, isDrawing } = useDrawingContext();

    // 현재 활성 도구 찾기
    const activeTool = DRAWING_TOOLS.find((tool) => tool.mode === mode);

    // 기본 아이콘 (children이 없을 때)
    const CurrentIcon = activeTool?.icon || Edit3;

    return (
      <ToolbarTrigger
        ref={ref}
        tooltip={tooltip}
        size={size}
        active={isDrawing || !!activeTool}
        className={className}
        {...props}
      >
        {children || <CurrentIcon className="h-4 w-4" />}
      </ToolbarTrigger>
    );
  },
);

ToolbarDrawTrigger.displayName = "ToolbarDrawTrigger";

// ToolbarDrawContent Component
export const ToolbarDrawContent = React.forwardRef<
  HTMLDivElement,
  ToolbarDrawContentProps
>(({ className, children, ...props }, ref) => {
  return (
    <ToolbarContent
      ref={ref}
      align="center"
      sideOffset={16}
      className={cn("w-fit flex flex-col gap-3 p-4", className)}
      {...props}
    >
      {children}
    </ToolbarContent>
  );
});

ToolbarDrawContent.displayName = "ToolbarDrawContent";

// ToolbarDrawTool Component (개별 그리기 도구)
export const ToolbarDrawTool = React.forwardRef<
  HTMLButtonElement,
  ToolbarDrawToolProps
>(({ mode, className, children, ...props }, ref) => {
  const { startDrawing, mode: currentMode, stopDrawing } = useDrawingContext();

  const toolConfig = DRAWING_TOOLS.find((tool) => tool.mode === mode);

  if (!toolConfig) return null;

  const Icon = toolConfig.icon;
  const isActive = currentMode === mode;

  const handleClick = () => {
    if (isActive) {
      stopDrawing();
    } else {
      const { drawend } = startDrawing(mode);
      drawend((feature: any) => {
        console.log("Draw end:", feature);
      });
    }
  };

  return (
    <Button
      ref={ref}
      variant="ghost"
      size="sm"
      active={isActive}
      onClick={handleClick}
      className={cn(
        "flex items-center justify-start gap-2 h-8 px-3 min-w-32",
        className,
      )}
      title={toolConfig.description}
      {...props}
    >
      {children || (
        <>
          <Icon className={cn("h-4 w-4", toolConfig.color)} />
          <span className="text-sm">{toolConfig.label}</span>
        </>
      )}
    </Button>
  );
});

ToolbarDrawTool.displayName = "ToolbarDrawTool";

// ToolbarDrawAction Component (액션 버튼들)
export const ToolbarDrawAction = React.forwardRef<
  HTMLButtonElement,
  ToolbarDrawActionProps
>(({ action, className, children, ...props }, ref) => {
  const { stopDrawing, isDrawing } = useDrawingContext();

  const handleAction = () => {
    if (action === "stop") {
      stopDrawing();
    }
  };

  if (action === "stop" && !isDrawing) return null;

  return (
    <Button
      ref={ref}
      variant="destructive"
      size="sm"
      onClick={handleAction}
      className={cn("h-6 w-6 p-0", className)}
      title="중단"
      {...props}
    >
      {children || <X className="h-3 w-3" />}
    </Button>
  );
});

ToolbarDrawAction.displayName = "ToolbarDrawAction";
