// hooks/usePaginationRouter.ts
import { useRouter } from "next/navigation";

import { usePaginationParams } from "./use-pagination-params";

export function withPaginationUrl(
  base: string,
  { page, size }: { page: number; size: number },
) {
  return `${base}?page=${page}&size=${size}`;
}

export function usePaginationRouter() {
  const router = useRouter();
  const { page, size } = usePaginationParams();

  const push = (base: string) => {
    router.push(withPaginationUrl(base, { page, size }));
  };

  const replace = (base: string) => {
    router.replace(withPaginationUrl(base, { page, size }));
  };

  return { page, size, push, replace };
}
