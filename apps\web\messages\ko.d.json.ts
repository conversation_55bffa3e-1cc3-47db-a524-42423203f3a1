// This file is auto-generated by next-intl, do not edit directly.
// See: https://next-intl.dev/docs/workflows/typescript#messages-arguments

declare const messages: {
  "board": {
    "announce": "공지사항",
    "dataSpace": "데이터 공간",
    "qna": "문의사항",
    "register": "등록",
    "edit": "수정",
    "delete": "삭제"
  },
  "pagination": {
    "resultStatus": "총 {totalCount, number} 개 중 {start, number}부터 {end, number}까지 표시 중",
    "pageStatus": "{currentPage, number} / {totalPages, number}",
    "rowsPerPage": "행"
  },
  "copy": {
    "types": {
      "coord": "경위도",
      "juso": "지번 주소",
      "pnu": "PNU"
    },
    "fail": "{type}을(를) 불러올 수 없습니다.",
    "success": "{type}이(가) 복사되었습니다."
  },
  "dialog": {
    "close": "닫기",
    "cancel": "취소",
    "save": "저장",
    "confirm": "확인"
  },
  "user": {
    "login": "로그인",
    "logout": "로그아웃",
    "notification": "알림",
    "myPage": "마이 페이지"
  },
  "estate": {
    "land": {
      "basic": {
        "pnu": "필지 고유번호",
        "ldCodeNm": "법정동",
        "mnnmSlno": "지번",
        "regstrSeCodeNm": "대장 구분",
        "lndcgrCodeNm": "지목",
        "lndpclAr": "면적(㎡)",
        "posesnSeCodeNm": "소유 구분",
        "cnrsPsnCo": "소유(공유)인 수(명)",
        "ladFrtlScNm": "축척 구분"
      },
      "history": {
        "pnu": "필지 고유번호",
        "ldCodeNm": "법정동",
        "mnnmSlno": "지번",
        "regstrSeCodeNm": "대장 구분",
        "ladMvmnHistSn": "토지 이동 이력 순번",
        "clsSn": "폐쇄 순번",
        "lndcgrCodeNm": "지목",
        "lndpclAr": "면적(㎡)",
        "ladMvmnPrvonshCodeNm": "토지 이동 사유",
        "ladMvmnDe": "토지 이동 일자",
        "ladMvmnErsrDe": "토지 이동 말소 일자",
        "ladHistSn": "토지 이력 순번",
        "lastUpdtDt": "데이터 기준 일자"
      },
      "useplan": {
        "manageNo": "도면 번호",
        "regstrSeCodeNm": "대장 구분",
        "cnflcAtNm": "저촉 여부",
        "prposAreaDstrcCodeNm": "용도지역지구",
        "lastUpdtDt": "데이터 기준 일자"
      }
    },
    "building": {
      "ho": {
        "buldNm": "건물 이름",
        "buldDongNm": "동",
        "buldFloorNm": "층",
        "buldHoNm": "호실",
        "regstrSeCodeNm": "대장 구분",
        "ldaQotaRate": "대지권 비율"
      }
    },
    "registry": {
      "headings": {
        "mainPurpsCdNm": "주용도",
        "etcPurps": "기타용도",
        "roofCdNm": "지붕",
        "etcRoof": "기타지붕",
        "hhldCnt": "세대수",
        "fmlyCnt": "가구수",
        "heit": "높이(m)",
        "grndFlrCnt": "지상층수",
        "ugrndFlrCnt": "지하층수",
        "rideUseElvtCnt": "승용승강기수",
        "emgenUseElvtCnt": "비상용승강기수",
        "atchBldCnt": "부속건축물수",
        "atchBldArea": "부속건축물면적(㎡)",
        "totDongTotArea": "총동연면적(㎡)",
        "indrMechUtcnt": "옥내기계식대수(대)",
        "indrMechArea": "옥내기계식면적(㎡)",
        "oudrMechUtcnt": "옥외기계식대수(대)",
        "oudrMechArea": "옥외기계식면적(㎡)",
        "indrAutoUtcnt": "옥내자주식대수(대)",
        "indrAutoArea": "옥내자주식면적(㎡)",
        "oudrAutoUtcnt": "옥외자주식대수(대)",
        "oudrAutoArea": "옥외자주식면적(㎡)",
        "pmsDay": "허가일",
        "stcnsDay": "착공일",
        "useAprDay": "사용승인일",
        "pmsnoYear": "허가번호년",
        "pmsnoKikCdNm": "허가번호기관",
        "pmsnoGbCdNm": "허가번호구분",
        "hoCnt": "호수",
        "engrGrade": "에너지효율등급",
        "engrRat": "에너지절감율",
        "engrEpi": "EPI점수",
        "gnBldGrade": "친환경건축물등급",
        "gnBldCert": "친환경건축물인증점수",
        "itgBldGrade": "지능형건축물등급",
        "itgBldCert": "지능형건축물인증점수",
        "crtnDay": "생성일자",
        "rnum": "순번",
        "platPlc": "대지위치",
        "sigunguCd": "시군구코드",
        "bjdongCd": "법정동코드",
        "platGbCd": "대지구분코드",
        "bun": "번",
        "ji": "지",
        "mgmBldrgstPk": "관리건축물대장PK",
        "regstrGbCdNm": "대장구분",
        "regstrKindCdNm": "대장종류",
        "newPlatPlc": "도로명대지위치",
        "bldNm": "건물명",
        "splotNm": "특수지명",
        "block": "블록",
        "lot": "로트",
        "bylotCnt": "외필지수",
        "naRoadCd": "새주소도로코드",
        "naBjdongCd": "새주소법정동코드",
        "naUgrndCd": "새주소지상지하코드",
        "naMainBun": "새주소본번",
        "naSubBun": "새주소부번",
        "dongNm": "동명칭",
        "mainAtchGbCdNm": "주부속구분",
        "platArea": "대지면적(㎡)",
        "archArea": "건축면적(㎡)",
        "bcRat": "건폐율(%)",
        "totArea": "연면적(㎡)",
        "vlRatEstmTotArea": "용적률산정연면적(㎡)",
        "vlRat": "용적률(%)",
        "strctCd": "구조코드",
        "strctCdNm": "주구조",
        "etcStrct": "기타구조",
        "rserthqkDsgnApplyYn": "내진설계적용여부",
        "rserthqkAblty": "내진능력"
      }
    },
    "price": {
      "ind": {
        "pnu": "필지 고유번호",
        "ldCodeNm": "법정동",
        "mnnmSlno": "지번",
        "regstrSeCodeNm": "특수지 구분",
        "stdrYear": "기준연도",
        "stdrMt": "기준월",
        "ladRegstrAr": "토지대장 면적(㎡)",
        "calcPlotAr": "산정 대지 면적(㎡)",
        "buldAllTotAr": "건물 전체 연면적(㎡)",
        "buldCalcTotAr": "건물 산정 연면적(㎡)",
        "housePc": "주택 가격(원)"
      },
      "pclnd": {
        "pnu": "필지 고유번호",
        "ldCodeNm": "법정동",
        "mnnmSlno": "지번",
        "regstrSeCodeNm": "특수지 구분",
        "stdrYear": "기준연도",
        "stdrMt": "기준월",
        "pblntfPclnd": "공시지가(원/㎡)",
        "pblntfDe": "공시일자"
      }
    }
  },
  "admin": {
    "author": {
      "business": {
        "userNm": "이름",
        "deptNm": "부서",
        "userId": "아이디",
        "mbtlnumEncpt": "전화번호",
        "emailaddrEncpt": "이메일",
        "menuAuthorList": "권한 목록",
        "editInfo": "정보 수정"
      }
    }
  }
};
export default messages;