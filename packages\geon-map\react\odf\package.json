{"name": "@geon-map/react-odf", "version": "0.0.0", "type": "module", "exports": {".": {"development": {"types": "./src/index.ts", "default": "./src/index.ts"}, "types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs", "default": "./dist/index.js"}}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "sideEffects": false, "files": ["dist/**", "src/**"], "scripts": {"build": "tsup", "dev:prod": "tsup --watch", "type-check": "tsc --noEmit", "test": "vitest", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist", "lint": "eslint . --max-warnings 0"}, "dependencies": {"@geon-map/core": "workspace:*", "clsx": "^2.1.1", "tailwind-merge": "^3.3.1", "zustand": "^5.0.8"}, "devDependencies": {"@config/eslint": "workspace:*", "@config/typescript": "workspace:*", "@geon-query/react-query": "workspace:*", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/node": "^22.18.3", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "eslint": "^9.35.0", "react": "^19.1.1", "tsup": "^8.5.0", "typescript": "^5.9.2", "vitest": "^3.2.4"}, "peerDependencies": {"react": "^19.0.0"}, "publishConfig": {"access": "restricted"}}