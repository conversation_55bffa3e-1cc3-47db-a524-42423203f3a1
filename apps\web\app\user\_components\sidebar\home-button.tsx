"use client";

import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@geon-ui/react/primitives/sidebar";
import { GalleryVerticalEnd } from "lucide-react";
import Link from "next/link";
import * as React from "react";

export default function HomeButton() {
  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <SidebarMenuButton
          asChild
          size="lg"
          className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
        >
          <Link href="/">
            <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
              <GalleryVerticalEnd className="size-4" />
            </div>
            <h1 className="flex flex-col gap-0.5 leading-none">
              <span className="font-medium">MAGP</span>
              <span className="">무안 공간정보 통합플랫폼</span>
            </h1>
          </Link>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
