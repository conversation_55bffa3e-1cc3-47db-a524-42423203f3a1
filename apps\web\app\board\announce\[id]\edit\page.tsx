// app/board/announce/[id]/edit/page.tsx

"use client";

import {
  type APIResponseType,
  createGeonMagpClient,
  type MagpClient,
  NoticeCUDRequest,
} from "@geon-query/model";
import {
  useAppMutation,
  useAppQuery,
  useAppQueryClient,
} from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import React, { useEffect, useState } from "react";

import { usePaginationRouter } from "@/app/board/_components/hooks/use-pagination-router";

import Edit from "../../_components/tables/edit";
import { FormState } from "../../_components/tables/view";

function ynToBool(v: "Y" | "N") {
  return v === "Y";
}
function boolToYn(v: boolean): string {
  return v ? "Y" : "N";
}

//TODO 수정자 아이디 store에서 호출
const userId = "admin";
export default function Page() {
  const { id } = useParams<{ id: string }>(); // ✅ URL 파라미터 읽기
  const client = createGeonMagpClient();
  const router = useRouter();
  const searchParams = useSearchParams();
  const page = Number(searchParams.get("page") ?? 1);
  const size = Number(searchParams.get("size") ?? 10);

  const { data, isLoading, isError } = useAppQuery<
    APIResponseType<MagpClient["notice"]["select"]>
  >({
    queryKey: ["magp/notice", { nttId: id }],
    queryFn: () => client.notice.select({ nttId: id }),
    enabled: Boolean(id),
  });
  const qc = useAppQueryClient(); // ✅ 래핑된 훅 사용
  const { push } = usePaginationRouter();
  const updMutation = useAppMutation({
    mutationFn: async (payload: NoticeCUDRequest) =>
      client.notice.update(payload),
    onSuccess: () => {
      qc.invalidateQueries({ queryKey: ["magp/notice"] });
      //상세창으로
      router.push(`/board/announce/${id}?page=${page}&size=${size}`);
    },
  });

  const [form, setForm] = useState<FormState | null>(null);
  useEffect(() => {
    const item = data && typeof data.result !== "string" ? data.result : null;
    if (item) {
      setForm({
        nttSj: item.nttSj ?? "",
        nttCn: item.nttCn ?? "",
        upperExpsrAt: ynToBool(item.upperExpsrAt),
        smsSndngAt: ynToBool(item.smsSndngAt),
        othbcAt: ynToBool(item.othbcAt),
        pstgBeginDt: item.pstgBeginDt?.slice(0, 16) ?? "",
        linkUrl: item.linkUrl ?? "",
        popupAt: ynToBool(item.popupAt),
        popupBeginDt: item.popupBeginDt?.slice(0, 16) ?? "",
        popupEndDt: item.popupEndDt?.slice(0, 16) ?? "",
        popupPortalExpsrAt: ynToBool(item.popupPortalExpsrAt),
        popupInsttExpsrAt: ynToBool(item.popupInsttExpsrAt),
        registerId: item.registerId,
        updusrId: userId,
      });
    }
  }, [data]);
  if (isLoading) return <Skeleton className="size-full" />;
  if (isError || !data || typeof data.result === "string") {
    return <div className="text-red-500">Error loading data</div>;
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updMutation.mutate({
      nttId: id,
      nttSj: form!.nttSj,
      nttCn: form!.nttCn,
      popupAt: boolToYn(form!.popupAt as boolean) || "N",
      popupBeginDt: form!.popupBeginDt
        ? new Date(form!.popupBeginDt).toISOString()
        : null,
      popupEndDt: form!.popupEndDt
        ? new Date(form!.popupEndDt).toISOString()
        : null,
      updusrId: userId,
    });
  };

  return (
    <div className="mx-auto w-full max-w-5xl space-y-6 p-4">
      <h1 className="text-xl font-semibold">공지 수정</h1>
      {form !== null && (
        <Edit
          form={form}
          setForm={setForm as React.Dispatch<React.SetStateAction<FormState>>}
          onSubmit={handleSubmit}
          isPending={updMutation.isPending}
          onCancel={() => push(`/board/announce/${id}`)}
        />
      )}
    </div>
  );
}
