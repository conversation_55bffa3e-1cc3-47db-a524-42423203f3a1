"use client";

import { useDraw } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { RotateCw } from "lucide-react";
import * as React from "react";

import { ToolbarItem, ToolbarTrigger } from "./base/toolbar-item";

// Props for clear components
export interface ToolbarClearProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 툴팁 텍스트 */
  tooltip?: string;
  /** 버튼 크기 */
  size?: "sm" | "lg" | "default" | "icon";
  /** 확인 없이 바로 삭제 */
  immediate?: boolean;
  /** 툴바 위치 (ToolbarContainer에서 전달) */
  position?:
    | "top-left"
    | "top-center"
    | "top-right"
    | "center-left"
    | "center-right"
    | "bottom-left"
    | "bottom-center"
    | "bottom-right";
}

// ToolbarClear Component
export const ToolbarClear = React.forwardRef<
  HTMLButtonElement,
  ToolbarClearProps
>(
  (
    {
      tooltip = "모든 그리기 삭제",
      size = "default",
      immediate = false,
      position,
      className,
      children,
      onClick,
      ...props
    },
    ref,
  ) => {
    const { clearAll } = useDraw();

    const handleClear = React.useCallback(
      async (e: React.MouseEvent<HTMLButtonElement>) => {
        if (!immediate) {
          const confirmed = window.confirm(
            "모든 그리기 데이터를 삭제하시겠습니까?",
          );
          if (!confirmed) return;
        }

        try {
          await clearAll();
        } catch (error) {
          console.error("데이터 삭제 실패:", error);
        }

        onClick?.(e);
      },
      [clearAll, immediate, onClick],
    );

    return (
      <ToolbarItem position={position}>
        <ToolbarTrigger
          ref={ref}
          tooltip={tooltip}
          size={size}
          className={cn(
            "text-geon-destructive hover:text-geon-destructive-foreground",
            className,
          )}
          onClick={handleClear}
          {...props}
        >
          {children || <RotateCw className="h-4 w-4" />}
        </ToolbarTrigger>
      </ToolbarItem>
    );
  },
);

ToolbarClear.displayName = "ToolbarClear";
