"use client";

import { useBaseLayer } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import { Card } from "@geon-ui/react/primitives/card";
import { Checkbox } from "@geon-ui/react/primitives/checkbox";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@geon-ui/react/primitives/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@geon-ui/react/primitives/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@geon-ui/react/primitives/select";
import { MapIcon } from "lucide-react";
import { useEffect, useState } from "react";

/**
 * 🎯 @geon-ui/react/primitives 기반 배경지도 위젯
 */
export function CustomBaseLayerWidget({ className }: { className?: string }) {
  const {
    currentBaseLayerId,
    categories,
    getBaseLayersByCategory,
    switchBaseLayer,
    overlayCategories,
    getOverlayLayersByCategory,
    toggleOverlayLayer,
    isOverlayLayerActive,
    availableOverlayLayers,
  } = useBaseLayer();

  const [selectedGroup, setSelectedGroup] = useState("");
  const [hybridEnabled, setHybridEnabled] = useState(true);

  // 초기 카테고리 선택
  useEffect(() => {
    if (categories.length > 0 && !selectedGroup) {
      setSelectedGroup(String(categories[0]));
    }
  }, [categories, selectedGroup]);

  const currentBaseItems = getBaseLayersByCategory(selectedGroup);
  const currentOverlayItems = getOverlayLayersByCategory(selectedGroup);
  const allCategories = [...new Set([...categories, ...overlayCategories])];

  const handleBaseLayerSelect = async (baseLayerId: string) => {
    await switchBaseLayer(baseLayerId);

    if (hybridEnabled) {
      // 1. 먼저 모든 활성화된 하이브리드 레이어를 끔
      for (const layer of availableOverlayLayers) {
        if (layer.layerType === "hybrid" && isOverlayLayerActive(layer.id)) {
          await toggleOverlayLayer(layer.id);
        }
      }

      // 2. 현재 카테고리의 하이브리드 레이어를 켬
      for (const layer of currentOverlayItems) {
        if (layer.layerType === "hybrid") {
          await toggleOverlayLayer(layer.id);
        }
      }
    }
  };

  const handleHybridToggle = async (checked: boolean) => {
    setHybridEnabled(checked);
    for (const layer of currentOverlayItems) {
      if (layer.layerType === "hybrid") {
        const isActive = isOverlayLayerActive(layer.id);
        if (checked && !isActive) {
          await toggleOverlayLayer(layer.id);
        } else if (!checked && isActive) {
          await toggleOverlayLayer(layer.id);
        }
      }
    }
  };

  return (
    <div className={cn("flex flex-col gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="icon"
            className="bg-white/70 hover:bg-white dark:bg-zinc-800/90 dark:hover:bg-zinc-800"
          >
            <MapIcon className="h-5 w-5" />
          </Button>
        </PopoverTrigger>

        <PopoverContent className="w-80 p-2" side="left">
          {/* 헤더 */}
          <div className="mb-2 flex items-center gap-2">
            <Select
              value={selectedGroup}
              onValueChange={(value) => setSelectedGroup(value)}
              disabled={allCategories.length === 0}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="카테고리 선택" />
              </SelectTrigger>
              <SelectContent>
                {allCategories.map((key) => (
                  <SelectItem key={String(key)} value={String(key)}>
                    {String(key) || "기타"}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {currentOverlayItems.length > 0 && (
              <label className="flex items-center gap-1 text-xs text-zinc-700 dark:text-zinc-300">
                <Checkbox
                  checked={hybridEnabled}
                  onCheckedChange={(val) => handleHybridToggle(!!val)}
                />
                하이브리드
              </label>
            )}
          </div>

          {/* 검색형 배경지도 리스트 */}
          <Command className="rounded-md border">
            <CommandInput placeholder="배경지도 검색..." />
            <CommandList>
              <CommandEmpty>검색 결과 없음</CommandEmpty>
              <CommandGroup heading="배경지도">
                {currentBaseItems.map((layer: any) => (
                  <CommandItem
                    key={layer.id}
                    value={layer.name}
                    onSelect={() => handleBaseLayerSelect(layer.id)}
                  >
                    <Card
                      className={cn(
                        "flex w-full cursor-pointer flex-row items-center gap-3 p-2",
                        currentBaseLayerId === layer.id &&
                          "border-blue-500 bg-blue-50",
                      )}
                    >
                      <div className="flex h-10 w-10 items-center justify-center overflow-hidden rounded bg-zinc-100 dark:bg-zinc-700">
                        {layer.thumbnail ? (
                          <img
                            src={layer.thumbnail}
                            alt={layer.name}
                            className="h-full w-full object-cover"
                          />
                        ) : (
                          <span className="text-xs text-zinc-500">IMG</span>
                        )}
                      </div>
                      <div className="flex flex-1 flex-col overflow-hidden">
                        <span className="truncate text-sm font-medium">
                          {layer.name}
                        </span>
                        <span className="text-muted-foreground truncate text-xs">
                          {layer.category}
                        </span>
                      </div>
                    </Card>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>

          {/* 오버레이 */}
          {hybridEnabled && currentOverlayItems.length > 0 && (
            <div className="mt-3 border-t pt-2">
              <div className="text-muted-foreground mb-1 text-xs font-medium">
                오버레이
              </div>
              {currentOverlayItems.map((layer: any) => {
                const isActive = isOverlayLayerActive(layer.id);
                return (
                  <div
                    key={layer.id}
                    className="hover:bg-accent/30 flex items-center gap-2 rounded p-1"
                  >
                    <Checkbox
                      checked={isActive}
                      onCheckedChange={() => toggleOverlayLayer(layer.id)}
                    />
                    <span className="text-xs">{layer.name}</span>
                  </div>
                );
              })}
            </div>
          )}
        </PopoverContent>
      </Popover>
    </div>
  );
}
