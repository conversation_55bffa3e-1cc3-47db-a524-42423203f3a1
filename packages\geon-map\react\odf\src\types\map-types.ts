import { BasemapInfo, ODF, ODF_MAP } from "@geon-map/core";

import { DrawId } from "./draw-types";
import { Layer, LayerConfig, LayerInfo } from "./layer-types";

export interface MapContextType {
  map: ODF_MAP | null;
  setMap: (map: ODF_MAP | null) => void;
  odf: ODF | null;
  setOdf: (odf: ODF | null) => void;
}

export interface MapInfo {
  id: string;
  name: string;
  activeUsers: number;
  createdAt: string;
  updatedAt: string;
  isPublic: boolean;
}

export interface MapView {
  center: [number, number];
  zoom: number;
  basemap: string;
}

export interface LayerParams {
  method: string;
  server: string;
  layer: string;
  service: string;
  bbox: boolean;
  matrixSet: string | null;
  crtfckey: string;
  projection: string;
  serviceTy: string;
  geometryType: string;
  [key: string]: any;
}

export interface MapState {
  map: any | null; // ODF Map instance
  info: MapInfo;
  view: MapView;
  layers: Layer[];
}

export interface MapActions {
  // View actions
  setCenter: (center: [number, number]) => void;
  setZoom: (zoom: number) => void;
  setBasemap: (basemap: string) => void;
  setBasemapUrl: (basemap: BasemapInfo) => void;
  // Layer actions
  addLayer: (layer: Partial<Layer>) => Layer | null;
  removeLayer: (layerId: string) => void;
  toggleLayerVisibility: (layerId: string) => void;
}

export interface MapHookResult extends MapState, MapActions {
  initialize: (container: HTMLDivElement) => Promise<void>;
  destroy: () => void;
}

export interface BasemapConfig {
  baroEMap?: string[];
  [key: string]: any;
}

export interface MapInitializeOptions {
  center?: [number, number];
  zoom?: number;
  projection?: string;
  basemap?: BasemapConfig;
  baroEMapURL?: string;
  baroEMapAirURL?: string;
  optimization?: boolean;
  onMapInit?: (mapState: any) => void;
}

export interface UseMapOptions extends MapInitializeOptions {
  autoInit?: boolean;
  baroEMapURL?: string;
  baroEMapAirURL?: string;
  optimization?: boolean;
}

export interface MapProps extends UseMapOptions {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  onMapInit?: (mapState: UseMapReturn) => void;
}

export interface UseMapReturn {
  odf: ODF | null;
  map: ODF_MAP | null;
  view: {
    setCenter: (center: [number, number]) => void;
    setZoom: (zoom: number) => void;
    setBasemap: (basemapId: string) => void;
    getCenter: () => [number, number];
    getZoom: () => number;
    moveToCurrentLocation: (zoom: number) => void;
  } | null;
  isLoading: boolean;
  error: string | null;
  layer: {
    add: (
      layerInfo: LayerInfo,
      layerConfig: LayerConfig,
      style?: any,
    ) => Promise<Layer | null>;
    remove: (layerId: string) => void;
    toggle: (layerId: string) => void;
    getAll: () => Layer[];
    updateStyle: (layerId: string, newStyle: any) => Promise<void>;
    updateFilter?: (layerId: string, filterCondition: string) => Promise<void>;
  } | null;
  control: {
    draw: (draw: DrawId) => void;
  } | null;
}
export type BasemapId = "eMapBasic" | "eMapAIR" | "eMapColor" | "eMapWhite";
