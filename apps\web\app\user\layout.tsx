import { QueryProvider } from "@geon-query/react-query";
import { SidebarProvider } from "@geon-ui/react/primitives/sidebar";
import React from "react";

import { ServiceSidebarProvider } from "../service/_contexts/sidebar";
import OuterSidebar from "./_components/sidebar/outer";

export default function UserLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <SidebarProvider>
      <ServiceSidebarProvider>
        <OuterSidebar />
        <QueryProvider>
          <section>
            <h1>사용자정보</h1>
            <div>{children}</div>
          </section>
        </QueryProvider>
      </ServiceSidebarProvider>
    </SidebarProvider>
  );
}
