"use client";

import { cn } from "@geon-ui/react/lib/utils";
import * as React from "react";

// 🎯 8분위 position 타입 정의
export type ToolbarPosition =
  | "top-left"
  | "top-center"
  | "top-right"
  | "center-left"
  | "center-right"
  | "bottom-left"
  | "bottom-center"
  | "bottom-right";

export type ToolbarOrientation = "horizontal" | "vertical" | "auto";
export type ToolbarStyle = "minimal" | "glass" | "solid";
export type ToolbarButtonVariant = "default" | "compact" | "separated";
export type ToolbarSize = "sm" | "md" | "lg";
export type ToolbarSpacing = "compact" | "normal" | "relaxed";

export interface ToolbarResponsiveOptions {
  mobile?: {
    position?: ToolbarPosition;
    orientation?: ToolbarOrientation;
    hidden?: boolean;
  };
  tablet?: {
    position?: ToolbarPosition;
    orientation?: ToolbarOrientation;
    hidden?: boolean;
  };
}

export interface ToolbarContainerProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, "style"> {
  /** 툴바 위치 (8분위) */
  position: ToolbarPosition;
  /** 툴바 방향 */
  orientation?: ToolbarOrientation;
  /** 툴바 테마 스타일 */
  variant?: ToolbarStyle;
  /** 버튼 스타일 변형 */
  buttonVariant?: ToolbarButtonVariant;
  /** 툴바 크기 */
  size?: ToolbarSize;
  /** 아이템 간격 */
  spacing?: ToolbarSpacing;
  /** 반응형 옵션 */
  responsive?: ToolbarResponsiveOptions;
  /** 툴바 숨김 여부 */
  hidden?: boolean;
  /** 접기 가능 여부 */
  collapsible?: boolean;
  /** 기본 접힘 상태 */
  defaultCollapsed?: boolean;
  /** CSS 스타일 (HTML 기본 속성) */
  style?: React.CSSProperties;
}

// 🎯 Position별 CSS 클래스 매핑
const getPositionClasses = (position: ToolbarPosition): string => {
  const baseClasses = "absolute z-40";

  switch (position) {
    case "top-left":
      return `${baseClasses} top-4 left-4`;
    case "top-center":
      return `${baseClasses} top-4 left-1/2 -translate-x-1/2`;
    case "top-right":
      return `${baseClasses} top-4 right-4`;
    case "center-left":
      return `${baseClasses} top-1/2 -translate-y-1/2 left-4`;
    case "center-right":
      return `${baseClasses} top-1/2 -translate-y-1/2 right-4`;
    case "bottom-left":
      return `${baseClasses} bottom-4 left-4`;
    case "bottom-center":
      return `${baseClasses} bottom-4 left-1/2 -translate-x-1/2`;
    case "bottom-right":
      return `${baseClasses} bottom-4 right-4`;
    default:
      return `${baseClasses} top-4 right-4`; // 기본값
  }
};

// 🎯 Orientation별 CSS 클래스
const getOrientationClasses = (
  orientation: ToolbarOrientation,
  position: ToolbarPosition,
): string => {
  if (orientation === "auto") {
    // position에 따라 자동 결정
    if (position.includes("top-") || position.includes("bottom-")) {
      return "flex-row"; // 가로
    } else {
      return "flex-col"; // 세로
    }
  }

  return orientation === "horizontal" ? "flex-row" : "flex-col";
};

// 🎯 Style별 CSS 클래스
const getStyleClasses = (style: ToolbarStyle): string => {
  switch (style) {
    case "minimal":
      return "bg-transparent";
    case "glass":
      return "bg-geon-toolbar/30 backdrop-blur-md border border-geon-toolbar-border shadow-[0_4px_20px_var(--color-geon-toolbar-shadow)]";
    case "solid":
      return "bg-geon-background border border-geon-border shadow-md";
    default:
      return "bg-geon-toolbar backdrop-blur-md border border-geon-toolbar-border shadow-[0_4px_20px_var(--color-geon-toolbar-shadow)]";
  }
};

// 🎯 Button Variant별 CSS 변수 설정
const getButtonVariantClasses = (
  buttonVariant: ToolbarButtonVariant,
): string => {
  switch (buttonVariant) {
    case "compact":
      return "[--geon-toolbar-radius:var(--geon-toolbar-compact-radius)] [--geon-toolbar-spacing:var(--geon-toolbar-compact-spacing)] [--geon-toolbar-border-width:var(--geon-toolbar-compact-border-width)]";
    case "separated":
      return "[--geon-toolbar-radius:var(--geon-toolbar-separated-radius)] [--geon-toolbar-spacing:var(--geon-toolbar-separated-spacing)] [--geon-toolbar-border-width:var(--geon-toolbar-separated-border-width)] [box-shadow:var(--geon-toolbar-separated-shadow)]";
    case "default":
    default:
      return "[--geon-toolbar-radius:var(--geon-toolbar-radius)] [--geon-toolbar-spacing:var(--geon-toolbar-spacing)] [--geon-toolbar-border-width:var(--geon-toolbar-border-width)]";
  }
};

// 🎯 Size별 CSS 클래스
const getSizeClasses = (size: ToolbarSize): string => {
  switch (size) {
    case "sm":
      return "p-1";
    case "md":
      return "p-2";
    case "lg":
      return "p-3";
    default:
      return "p-2";
  }
};

// 🎯 Spacing별 CSS 클래스
const getSpacingClasses = (spacing: ToolbarSpacing): string => {
  switch (spacing) {
    case "compact":
      return "gap-1";
    case "normal":
      return "gap-2";
    case "relaxed":
      return "gap-3";
    default:
      return "gap-2";
  }
};

export const ToolbarContainer = React.forwardRef<
  HTMLDivElement,
  ToolbarContainerProps
>(
  (
    {
      position,
      orientation = "auto",
      variant = "glass",
      buttonVariant = "default",
      size = "md",
      spacing = "normal",
      responsive,
      hidden = false,
      collapsible = false,
      defaultCollapsed = false,
      className,
      style,
      children,
      ...props
    },
    ref,
  ) => {
    const [isCollapsed, setIsCollapsed] = React.useState(defaultCollapsed);

    if (hidden) return null;

    const positionClasses = getPositionClasses(position);
    const orientationClasses = getOrientationClasses(orientation, position);
    const styleClasses = getStyleClasses(variant);
    const buttonVariantClasses = getButtonVariantClasses(buttonVariant);
    const sizeClasses = getSizeClasses(size);
    const spacingClasses = getSpacingClasses(spacing);

    const containerClasses = cn(
      // 기본 클래스
      "flex items-center rounded-lg",
      // Position
      positionClasses,
      // Orientation & Spacing
      orientationClasses,
      spacingClasses,
      // Style & Size
      styleClasses,
      sizeClasses,
      // Button Variant CSS 변수
      buttonVariantClasses,
      // 반응형 처리
      responsive?.mobile?.hidden && "hidden md:flex",
      responsive?.tablet?.hidden && "hidden lg:flex",
      // 접힘 상태
      collapsible && isCollapsed && "opacity-50 scale-95",
      className,
    );

    return (
      <div
        ref={ref}
        className={containerClasses}
        style={style}
        data-toolbar-position={position}
        data-toolbar-orientation={orientation}
        {...props}
      >
        {collapsible && (
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="flex-shrink-0 w-6 h-6 rounded hover:bg-black/10 flex items-center justify-center"
            aria-label={isCollapsed ? "툴바 펼치기" : "툴바 접기"}
          >
            {isCollapsed ? "+" : "-"}
          </button>
        )}

        {(!collapsible || !isCollapsed) && (
          <div className={cn("flex", orientationClasses, spacingClasses)}>
            {React.Children.map(children, (child) => {
              if (React.isValidElement(child)) {
                // ToolbarItem에 position 전달
                return React.cloneElement(child, {
                  position: position,
                  ...child.props,
                });
              }
              return child;
            })}
          </div>
        )}
      </div>
    );
  },
);

ToolbarContainer.displayName = "ToolbarContainer";
