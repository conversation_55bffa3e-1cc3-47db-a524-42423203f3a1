import {
  Sidebar,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>barHeader,
} from "@geon-ui/react/primitives/sidebar";
import React from "react";

import HomeButton from "@/components/sidebar/home-button";
import NavUser from "@/components/sidebar/nav-user";

import NavMap from "./nav-map";

// This is sample data.
const data = {
  user: {
    name: "홍길동",
    department: "민원지적과",
  },
};

export default function OuterSidebar({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar {...props} collapsible="icon" className="z-50">
      <SidebarHeader>
        <HomeButton />
      </SidebarHeader>
      <SidebarContent>
        <NavMap />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  );
}
