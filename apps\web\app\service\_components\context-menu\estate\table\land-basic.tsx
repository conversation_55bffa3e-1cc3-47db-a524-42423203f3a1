"use client";

import type {
  APIRequestType,
  APIResponseType,
  EstateClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import { createColumnHelper } from "@tanstack/react-table";
import { useFormatter, useTranslations } from "next-intl";
import React from "react";

import ViewTable from "@/components/table/view";

export default function LandBasic({
  client,
  ...props
}: APIRequestType<EstateClient["land"]["basic"]> & {
  client: EstateClient;
}) {
  // message handler
  const t = useTranslations("estate.land.basic");
  const f = useFormatter();

  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<EstateClient["land"]["basic"]>
  >({
    queryKey: ["land/basic", { ...props }],
    queryFn: () => client.land.basic({ ...props }),
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError || !data || typeof data.result === "string")
    return (
      <div className="text-destructive flex justify-center align-middle">
        Error loading parcel data: {error as string}
        {data && `, ${data?.result as unknown as string}`}
      </div>
    );

  const helper = createColumnHelper<(typeof data.result.resultList)[0]>();
  const columns = [
    helper.accessor("lndcgrCodeNm", {
      cell: (info) => info.getValue(),
      header: t("lndcgrCodeNm"),
    }),
    helper.accessor("posesnSeCodeNm", {
      cell: (info) => info.getValue(),
      header: t("posesnSeCodeNm"),
    }),
    helper.accessor("ladFrtlScNm", {
      cell: (info) => info.getValue(),
      header: t("ladFrtlScNm"),
    }),
    helper.accessor("lndpclAr", {
      cell: (info) => f.number(Number(info.getValue())),
      header: t("lndpclAr"),
    }),
  ];

  return (
    <div className="flex w-full shrink-0 flex-col overflow-hidden overflow-y-auto">
      <ViewTable data={data.result.resultList} columns={columns} pinHeader />
    </div>
  );
}
