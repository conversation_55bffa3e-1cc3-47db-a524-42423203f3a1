// hooks/use-service-search-client.ts
"use client";

import { createGeonMagpClient, type MagpClient } from "@geon-query/model";
import { useMemo } from "react";

type IntegratedResultRow = {
  serial: number | string;
  name: string;
  location?: string;
  status?: string;
  _facilityId: string;
  _facilityType: string;
  [key: string]: any;
};

type ServiceQueryArgs = {
  client: MagpClient;
  form: Record<string, unknown>;
  pageSize: number;
  pageNo: number;
  resource?: string | null;
};

type ServiceQueryResult = {
  rows: IntegratedResultRow[];
  totalCount: number;
  raw: unknown;
};

type ServiceQueryConfig = {
  key: string;
  fetcher: (args: ServiceQueryArgs) => Promise<ServiceQueryResult>;
};

const SERVICE_QUERY_CONFIGS: Record<string, ServiceQueryConfig> = {
  water: {
    key: "water/list",
    fetcher: async ({ client, form, pageSize, pageNo }) => {
      const payload = {
        ...form,
        pageSize,
        pageIndex: pageNo,
      };
      const response = await client.water.list(payload);
      const resultList = response.result?.resultList ?? [];
      const rows = resultList;
      return {
        rows,
        totalCount: response.result?.pageInfo?.totalCount ?? rows.length,
        raw: response,
      };
    },
  },
  road: {
    key: "road/list",
    fetcher: async ({ form, pageSize, pageNo }) => {
      const params = new URLSearchParams({
        ...form,
        pageSize: String(pageSize),
        pageIndex: String(pageNo),
      });
      const response = await fetch(`/api/dynamic-search/road?${params}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch road results: ${response.status}`);
      }
      const json = await response.json();
      const rows = (json.data ?? []).map((item: any, index: number) => ({
        ...item,
        serial: item.serial ?? item.gid ?? index + 1,
        name: item.name ?? "",
        _facilityId: String(item.gid ?? item.serial ?? index + 1),
        _facilityType: "road",
      }));
      return {
        rows,
        totalCount: json.totalCount ?? rows.length,
        raw: json,
      };
    },
  },
};

function getServiceQueryConfig(serviceId?: string | null) {
  if (!serviceId) return null;
  return SERVICE_QUERY_CONFIGS[serviceId] ?? null;
}

export function useServiceSearchClient(serviceId?: string | null) {
  const client = useMemo(() => createGeonMagpClient(), []);
  const config = useMemo(() => getServiceQueryConfig(serviceId), [serviceId]);

  return { client, config };
}

export type { IntegratedResultRow, ServiceQueryConfig, ServiceQueryResult };
