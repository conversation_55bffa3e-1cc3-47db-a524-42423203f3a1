"use client";

import { useDraw } from "@geon-map/react-odf";
import { useHighlightLayer } from "@geon-map/react-ui/hooks";
import { SourceTable } from "@geon-query/model";
import { useCallback, useState } from "react";

import type {
  FacilityDetailData,
  FacilityModalMode,
} from "../../_types/facility-detail";
import { createCQLFilterFromSearchParams } from "../../_utils/cql-filter";
import { useFacilitySearch } from "../../_contexts/facility-search";
import type { IntegratedResultRow } from "./use-service-search-client";

export function useServiceSearchHandle(selectedFacilityIds: string[]) {
  const { highlightWKT, isReady } = useHighlightLayer();
  const { clearAll } = useDraw();
  const { setAppliedCQLFilter } = useFacilitySearch();

  // 검색 관련 상태
  const [pageSize, setPageSize] = useState(10);
  const [pageNo, setPageNo] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [form, setForm] = useState<Record<string, unknown>>({});
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  // 모달 상태
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<FacilityModalMode>("detail");
  const [selectedFacilityData, setSelectedFacilityData] =
    useState<FacilityDetailData>();

  // CQL 필터 상태
  const [currentCQLFilter, setCurrentCQLFilter] = useState<string | null>(null);

  // 🔍 검색 실행
  const handleSearch = useCallback(
    async (searchParams: Record<string, any>) => {
      if (selectedFacilityIds.length === 0) {
        alert("검색할 시설물을 선택해주세요");
        return;
      }

      setIsSearching(true);
      try {
        const { spatialSearch, adminDistrict, ...rest } = searchParams;
        const payload = {
          ...rest,
          ...(spatialSearch?.wkt ? { spatialWkt: spatialSearch.wkt } : {}),
          emdCd: adminDistrict || undefined,
          sourceTables: selectedFacilityIds as SourceTable[],
        };

        // CQL 필터 생성
        const cqlFilter = createCQLFilterFromSearchParams(payload, spatialSearch);
        console.log("[useServiceSearchHandle] Generated CQL filter for search", {
          payload,
          spatialSearch,
          cqlFilter,
          sourceTables: selectedFacilityIds,
          note: "이 CQL 필터가 각 sourceTables 레이어에 적용됩니다"
        });

        // CQL 필터 상태 업데이트 (로컬 + 컨텍스트)
        setCurrentCQLFilter(cqlFilter);
        setAppliedCQLFilter(cqlFilter);
        setForm(payload);
        setPageNo(1);
        setHasSearched(true);
      } catch (error) {
        console.error("통합 검색 오류:", error);
        alert("검색 중 오류가 발생했습니다");
      } finally {
        setIsSearching(false);
      }
    },
    [selectedFacilityIds],
  );

  // 📍 위치 보기
  const handleLocationView = useCallback((row: IntegratedResultRow) => {
    console.log("위치 조회:", row);
    alert(`${row._facilityType}의 위치로 이동합니다`);
  }, []);

  // 📑 상세보기
  const handleDetailView = useCallback((row: IntegratedResultRow) => {
    const facilityData: FacilityDetailData = {
      facilityId: row._facilityId,
      facilityType: row._facilityType,
      facilityName: row.name,
      registryType: row.registryType || "",
      featureCode: row.featureCode || "",
      managementNumber: row.managementNumber || row.serial?.toString() || "",
      adminDistrict: row.location || "",
      mapSheetNumber: row.mapSheetNumber || "",
      managementAgency: row.managementAgency || "",
      installStartDate: row.installStartDate || "",
      installEndDate: row.installEndDate || "",
      remarks: row.remarks || "",
      areaInfo: row.areaInfo || "",
    };
    setSelectedFacilityData(facilityData);
    setModalMode("detail");
    setModalOpen(true);
  }, []);

  const handleModalClose = useCallback(() => {
    setModalOpen(false);
    setSelectedFacilityData(undefined);
  }, []);

  const handleRegistered = useCallback((data: FacilityDetailData) => {
    console.log("시설물 등록 완료:", data);
    alert(`${data.facilityName || "시설물"}이 등록되었습니다.`);
  }, []);

  const handleRowClick = useCallback(
    (row: IntegratedResultRow) => {
      handleDetailView(row);
    },
    [handleDetailView],
  );

  const handleAdminDistrictChange = useCallback(
    async (districtCode: string) => {
      try {
        if (isReady) {
          const response = await fetch("/api/admin-districts", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ code: districtCode }),
          });
          if (!response.ok) throw new Error(`HTTP ${response.status}`);
          const districtDetail = await response.json();

          const style = districtCode
            ? {
                "fill-color": [220, 60, 34, 0],
                "stroke-color": [30, 144, 255, 0.8],
                "stroke-width": 5,
              }
            : {
                "fill-color": [220, 60, 34, 0],
                "stroke-color": [0, 200, 180, 0],
                "stroke-width": 0,
              };

          highlightWKT(districtDetail.geom, {
            isFitToLayer: true,
            clear: true,
            style,
          });
        }
      } catch (error) {
        console.error("행정구역 변경 처리 오류:", error);
      }
    },
    [isReady, highlightWKT],
  );

  const handleResetDynamicSearch = useCallback(() => {
    clearAll();
  }, [clearAll]);

  return {
    // 상태
    pageSize,
    setPageSize,
    pageNo,
    setPageNo,
    totalCount,
    setTotalCount,
    form,
    setForm,
    isSearching,
    setIsSearching,
    hasSearched,
    setHasSearched,

    // CQL 필터 상태
    currentCQLFilter,
    setCurrentCQLFilter,

    // 모달 상태
    modalOpen,
    modalMode,
    selectedFacilityData,

    // 핸들러
    handleSearch,
    handleLocationView,
    handleDetailView,
    handleModalClose,
    handleRegistered,
    handleRowClick,
    handleAdminDistrictChange,
    handleResetDynamicSearch,
  };
}
