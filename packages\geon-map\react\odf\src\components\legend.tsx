import { CommonStyleSpec, Symbolizer } from "@geon-map/core";
import { useEffect, useState } from "react";

import { useLayer } from "../hooks/use-layer";

type LegendVariant = "icon" | "icon-label";

export interface LegendProps extends React.HTMLAttributes<HTMLDivElement> {
  layerId?: string;
  styleSpec?: CommonStyleSpec;
  variant?: LegendVariant;
}

// Symbolizer를 SVG 아이콘으로 렌더링하는 컴포넌트
function SymbolizerIcon({
  symbolizer,
  size = 20,
}: {
  symbolizer: Symbolizer;
  size?: number;
}) {
  const renderSymbolizer = () => {
    switch (symbolizer.kind) {
      case "polygon":
        return (
          <svg width={size} height={size} viewBox="0 0 20 20">
            <rect
              x="2"
              y="2"
              width="16"
              height="16"
              fill={
                symbolizer.fill?.color
                  ? `rgba(${symbolizer.fill.color.join(",")})`
                  : "transparent"
              }
              stroke={
                symbolizer.stroke?.color
                  ? `rgba(${symbolizer.stroke.color.join(",")})`
                  : "none"
              }
              strokeWidth={symbolizer.stroke?.width || 1}
              strokeDasharray={symbolizer.stroke?.lineDash?.join(",") || "none"}
            />
          </svg>
        );

      case "line":
        return (
          <svg width={size} height={size} viewBox="0 0 20 20">
            <line
              x1="2"
              y1="10"
              x2="18"
              y2="10"
              stroke={
                symbolizer.stroke?.color
                  ? `rgba(${symbolizer.stroke.color.join(",")})`
                  : "#000"
              }
              strokeWidth={symbolizer.stroke?.width || 2}
              strokeDasharray={symbolizer.stroke?.lineDash?.join(",") || "none"}
              strokeLinecap={symbolizer.stroke?.lineCap || "round"}
            />
          </svg>
        );

      case "point": {
        const radius = symbolizer.mark?.radius || 5;
        const shape = symbolizer.mark?.shape || "Circle";

        return (
          <svg width={size} height={size} viewBox="0 0 20 20">
            {shape === "Circle" && (
              <circle
                cx="10"
                cy="10"
                r={radius}
                fill={
                  symbolizer.fill?.color
                    ? `rgba(${symbolizer.fill.color.join(",")})`
                    : "transparent"
                }
                stroke={
                  symbolizer.stroke?.color
                    ? `rgba(${symbolizer.stroke.color.join(",")})`
                    : "none"
                }
                strokeWidth={symbolizer.stroke?.width || 1}
              />
            )}
            {shape === "Square" && (
              <rect
                x={10 - radius}
                y={10 - radius}
                width={radius * 2}
                height={radius * 2}
                fill={
                  symbolizer.fill?.color
                    ? `rgba(${symbolizer.fill.color.join(",")})`
                    : "transparent"
                }
                stroke={
                  symbolizer.stroke?.color
                    ? `rgba(${symbolizer.stroke.color.join(",")})`
                    : "none"
                }
                strokeWidth={symbolizer.stroke?.width || 1}
              />
            )}
            {shape === "Triangle" && (
              <polygon
                points={`10,${10 - radius} ${10 - radius},${10 + radius} ${10 + radius},${10 + radius}`}
                fill={
                  symbolizer.fill?.color
                    ? `rgba(${symbolizer.fill.color.join(",")})`
                    : "transparent"
                }
                stroke={
                  symbolizer.stroke?.color
                    ? `rgba(${symbolizer.stroke.color.join(",")})`
                    : "none"
                }
                strokeWidth={symbolizer.stroke?.width || 1}
              />
            )}
          </svg>
        );
      }
      default:
        return (
          <svg width={size} height={size} viewBox="0 0 20 20">
            <rect
              x="2"
              y="2"
              width="16"
              height="16"
              fill="#ddd"
              stroke="#999"
              strokeWidth="1"
            />
          </svg>
        );
    }
  };

  return (
    <div className="inline-flex items-center justify-center">
      {renderSymbolizer()}
    </div>
  );
}

export function Legend({
  layerId,
  styleSpec,
  variant = "icon-label",
  className,
  ...props
}: LegendProps) {
  const { findLayer, getLegendUrl } = useLayer();
  const [commonStyleSpec, setCommonStyleSpec] =
    useState<CommonStyleSpec | null>(styleSpec || null);
  const [loading, setLoading] = useState(true);
  const [legendImageUrl, setLegendImageUrl] = useState<string | null>(null);
  const [imageLoadError, setImageLoadError] = useState(false);

  // 레이어 객체를 직접 가져와서 의존성으로 사용
  const layer = layerId ? findLayer(layerId) : null;
  const layerStyleSpec = layer?.commonStyleSpec;

  useEffect(() => {
    // styleSpec이 props로 전달된 경우 layerId 무시
    if (styleSpec) {
      setCommonStyleSpec(styleSpec);
      setLegendImageUrl(null);
      setImageLoadError(false);
      setLoading(false);
      return;
    }

    // layerId가 없으면 early return
    if (!layerId) {
      setCommonStyleSpec(null);
      setLegendImageUrl(null);
      setImageLoadError(false);
      setLoading(false);
      return;
    }

    // 레이어의 commonStyleSpec 사용
    setCommonStyleSpec(layerStyleSpec || null);

    // commonStyleSpec이 "Style Not Found"이거나 items가 비어있는 경우 getLegendUrl 사용
    const isStyleNotFound =
      layerStyleSpec?.name === "Style Not Found" ||
      !layerStyleSpec?.items?.length;

    if (isStyleNotFound) {
      const legendUrl = getLegendUrl(layerId, {
        legend_options: {
          forceLabels: variant === "icon-label" ? "on" : "off",
          bgColor: "0xFFFFFF",
          transparent: true,
          fontAntiAliasing: true,
        },
      });
      setLegendImageUrl(legendUrl);
      setImageLoadError(false);
    } else {
      setLegendImageUrl(null);
      setImageLoadError(false);
    }

    setLoading(false);
  }, [layerId, styleSpec, layerStyleSpec, getLegendUrl]);

  if (loading)
    return <div className="text-xs text-gray-500">Loading legend...</div>;

  // 범례 이미지 URL이 있고 로드 에러가 없는 경우 이미지로 표시
  if (legendImageUrl && !imageLoadError) {
    return (
      <div
        className={`flex flex-col items-start gap-1 ${className || ""}`}
        {...props}
      >
        <img
          src={legendImageUrl}
          alt="Legend"
          className="max-w-full h-auto"
          onError={() => {
            console.error("Failed to load legend image:", legendImageUrl);
            setImageLoadError(true);
          }}
          onLoad={() => setImageLoadError(false)}
        />
      </div>
    );
  }

  // commonStyleSpec이 없거나 items가 없는 경우 null 반환
  if (!commonStyleSpec || !commonStyleSpec.items?.length) return null;

  // 기존 심볼라이저 기반 범례 표시
  return (
    <div
      className={`flex flex-col items-start gap-1 ${className || ""}`}
      {...props}
    >
      {commonStyleSpec.items.map((item, itemIndex) =>
        item.style.symbolizers
          .filter((symbolizer) => symbolizer.kind !== "text") // ← text 심볼라이저 제외
          .map((symbolizer, symIndex) => (
            <div
              key={"symbolizer_" + symIndex}
              className="flex items-center gap-1"
            >
              <SymbolizerIcon
                key={`${itemIndex}-${symIndex}`}
                symbolizer={symbolizer}
                size={16}
              />
              {variant === "icon-label" && (
                <span
                  className="text-xs truncate max-w-20"
                  title={item.style.legendLabel}
                >
                  {item.style.legendLabel}
                </span>
              )}
            </div>
          )),
      )}
    </div>
  );
}

Legend.displayName = "Legend";
