import type { FacilityType, ServiceFacilityGroup } from "../_types/facility";

// 서비스별 시설물 유형 구성 데이터
export const SERVICE_FACILITIES: ServiceFacilityGroup[] = [
  {
    serviceId: "road",
    title: "도로",
    facilityTypes: [
      // id 는 실제 layer id 값이 되어야 함.
      // 구성 데이터를 서버단에서 동적으로 가져올 지, 클라이언트에서 상수로 관리할지 정해야 함.
      {
        id: "wtl_pipe_lm",
        title: "가로등",
        description: "도로 가로등 시설물",
        serviceId: "wtl_pipe_lm",
        enabled: true,
        category: "구조물",
        order: 1,
        color: "#3B82F6",
      },
      // {
      //   id: "road-tunnel",
      //   title: "가로수",
      //   description: "도로 가로수 시설물",
      //   serviceId: "road-tunnel",
      //   enabled: true,
      //   category: "구조물",
      //   order: 2,
      //   color: "#8B5CF6",
      // },
      // {
      //   id: "road-safety",
      //   title: "보안등",
      //   description: "도로 보안등 시설물",
      //   serviceId: "road-safety",
      //   enabled: true,
      //   category: "안전",
      //   order: 3,
      //   color: "#F59E0B",
      // },
      // {
      //   id: "road-drainage",
      //   title: "자전거도로",
      //   description: "자전거도로",
      //   serviceId: "road-drainage",
      //   enabled: true,
      //   category: "배수",
      //   order: 4,
      //   color: "#10B981",
      // },
    ],
  },
  {
    serviceId: "water",
    title: "상수 관리 시스템",
    facilityTypes: [
      {
        id: "wtl_pipe_lm",
        title: "상수관로",
        description: "상수관로",
        serviceId: "wtl_pipe_lm",
        enabled: true,
        category: "관리 대상 레이어",
        order: 1,
        color: "#3B82F6",
      },
      {
        id: "wtl_sply_ls",
        title: "급수관로",
        description: "급수관로",
        serviceId: "wtl_sply_ls",
        enabled: true,
        category: "관리 대상 레이어",
        order: 2,
        color: "#F59E0B",
      },
      {
        id: "wtl_manh_ps",
        title: "상수맨홀",
        description: "상수맨홀 시설물",
        serviceId: "wtl_manh_ps",
        enabled: true,
        category: "관리 대상 레이어",
        order: 3,
        color: "#10B981",
      },
      {
        id: "wtl_fire_ps",
        title: "소방시설",
        description: "소방시설",
        serviceId: "wtl_fire_ps",
        enabled: true,
        category: "관리 대상 레이어",
        order: 4,
        color: "#EC4899",
      },
      {
        id: "wtl_prga_ps",
        title: "수압계",
        description: "수압계",
        serviceId: "wtl_prga_ps",
        enabled: true,
        category: "관리 대상 레이어",
        order: 5,
        color: "#8B5CF6",
      },
      {
        id: "wtl_stpi_ps",
        title: "스탠드파이프",
        description: "스탠드파이프",
        serviceId: "wtl_stpi_ps",
        enabled: true,
        category: "관리 대상 레이어",
        order: 6,
        color: "#F87171",
      },
      {
        id: "wtl_flow_ps",
        title: "유량계",
        description: "유량계",
        serviceId: "wtl_flow_ps",
        enabled: true,
        category: "관리 대상 레이어",
        order: 7,
        color: "#FB923C",
      },
    ],
  },
];

// Helper functions
export function getServiceFacilities(serviceId: string): FacilityType[] {
  const service = SERVICE_FACILITIES.find((s) => s.serviceId === serviceId);
  return service?.facilityTypes || [];
}

export function getFacilityById(facilityId: string): FacilityType | null {
  for (const service of SERVICE_FACILITIES) {
    const facility = service.facilityTypes.find((f) => f.id === facilityId);
    if (facility) return facility;
  }
  return null;
}

export function getFacilitiesByCategory(
  serviceId: string,
): Record<string, FacilityType[]> {
  const facilities = getServiceFacilities(serviceId);
  return facilities.reduce(
    (groups, facility) => {
      const category = facility.category || "기타";
      if (!groups[category]) groups[category] = [];
      groups[category].push(facility);
      return groups;
    },
    {} as Record<string, FacilityType[]>,
  );
}

export function getFacilitiesByIds(facilityIds: string[]): FacilityType[] {
  const results: FacilityType[] = [];
  for (const id of facilityIds) {
    const facility = getFacilityById(id);
    if (facility) results.push(facility);
  }
  return results;
}
// 서비스 정보 조회
export function getServiceInfo(serviceId: string): ServiceFacilityGroup | null {
  return SERVICE_FACILITIES.find((s) => s.serviceId === serviceId) || null;
}
