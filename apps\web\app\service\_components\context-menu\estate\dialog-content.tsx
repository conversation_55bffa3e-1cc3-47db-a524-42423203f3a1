"use client";

import { createEstateClient } from "@geon-query/model";
import { Button } from "@geon-ui/react/primitives/button";
import {
  DialogClose,
  DialogDescription,
  DialogFooter,
  DialogTitle,
} from "@geon-ui/react/primitives/dialog";
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  Ta<PERSON><PERSON>ist,
  TabsTrigger,
} from "@geon-ui/react/primitives/tabs";
import { ExternalLink } from "lucide-react";
import Link from "next/link";
import { useTranslations } from "next-intl";
import React from "react";

import DraggableDialogContent from "@/components/draggable-dialog/content";
import DraggableDialogHeader from "@/components/draggable-dialog/header";

import LandBasic from "./table/land-basic";
import LandHistory from "./table/land-history";
import LandUsePlan from "./table/land-useplan";
import PriceInd from "./table/price-ind";
import PricePclnd from "./table/price-pclnd";
import RegistryHeadings from "./table/registry-headings";

interface EstateDialogProps {
  pnu: string;
  tabValue: string;
  setTabValue: (value: string) => void;
  juso?: string;
}

export default function EstateDialogContent({
  pnu,
  tabValue,
  setTabValue,
  juso,
}: EstateDialogProps) {
  // message handler
  const t = useTranslations("dialog");
  const client = createEstateClient();

  return (
    <DraggableDialogContent
      className="max-h-[670px] w-full !max-w-[1000px]"
      interactive
    >
      <DraggableDialogHeader>
        <DialogTitle>통합 행정 정보 조회</DialogTitle>
        <DialogDescription>{juso || "Failed to get address"}</DialogDescription>
      </DraggableDialogHeader>
      <Tabs
        value={tabValue}
        className="h-[500px] w-full overflow-hidden overflow-y-auto"
      >
        <TabsList>
          <TabsTrigger
            value="land-basic"
            onClick={() => setTabValue("land-basic")}
          >
            토지 대장
          </TabsTrigger>
          <TabsTrigger
            value="registry-headings"
            onClick={() => setTabValue("registry-headings")}
          >
            건축물 대장
          </TabsTrigger>
          <TabsTrigger
            value="price-ind"
            onClick={() => setTabValue("price-ind")}
          >
            개별 주택 가격
          </TabsTrigger>
          <TabsTrigger
            value="price-pclnd"
            onClick={() => setTabValue("price-pclnd")}
          >
            공시지가
          </TabsTrigger>
          <TabsTrigger
            value="land-useplan"
            onClick={() => setTabValue("land-useplan")}
          >
            토지 이용 계획
          </TabsTrigger>
        </TabsList>

        <TabsContent
          value="land-basic"
          className="flex h-[500px] w-full flex-col gap-2 overflow-hidden overflow-y-auto"
        >
          <span className="font-bold">토지 정보</span>
          <LandBasic pnu={pnu} client={client} pageNo={1} numOfRows={100} />
          <span className="mt-2 font-bold">토지 이동 이력</span>
          <LandHistory pnu={pnu} client={client} pageNo={1} numOfRows={10} />
        </TabsContent>
        <TabsContent
          value="registry-headings"
          className="flex h-[500px] w-full flex-col gap-2 overflow-hidden overflow-y-auto"
        >
          <RegistryHeadings
            pnu={pnu}
            client={client}
            pageNo={1}
            numOfRows={100}
          />
        </TabsContent>
        <TabsContent
          value="price-ind"
          className="flex h-[500px] w-full flex-col gap-2 overflow-hidden overflow-y-auto"
        >
          <PriceInd pnu={pnu} pageNo={1} numOfRows={10} client={client} />
        </TabsContent>
        <TabsContent
          value="price-pclnd"
          className="flex h-[500px] w-full flex-col gap-2 overflow-hidden overflow-y-auto"
        >
          <PricePclnd pnu={pnu} pageNo={1} numOfRows={100} client={client} />
        </TabsContent>
        <TabsContent
          value="land-useplan"
          className="flex h-[500px] w-full flex-col gap-2 overflow-hidden overflow-y-auto"
        >
          <LandUsePlan pnu={pnu} pageNo={1} numOfRows={10} client={client} />
        </TabsContent>
      </Tabs>

      <DialogFooter>
        {tabValue === "land-useplan" && (
          <Button asChild variant="outline">
            <Link
              target="_blank"
              rel="noopener noreferrer"
              href={`https://eum.go.kr/web/ar/lu/luLandDet.jsp?isNoScr=script&mode=search&add=land&pnu=${pnu}`}
            >
              토지 이음
              <ExternalLink />
            </Link>
          </Button>
        )}
        <DialogClose asChild>
          <Button type="button" variant="secondary">
            {t("close")}
          </Button>
        </DialogClose>
      </DialogFooter>
    </DraggableDialogContent>
  );
}
