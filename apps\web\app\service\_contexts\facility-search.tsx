"use client";

import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useMemo,
  useState,
} from "react";

import {
  getFacilitiesByIds,
  getServiceFacilities,
  getServiceInfo,
} from "../_data/facilities";
import type {
  FacilitySearchContextValue,
  SpatialSearchOptions,
} from "../_types/facility";

const FacilitySearchContext = createContext<FacilitySearchContextValue | null>(
  null,
);

export function useFacilitySearch() {
  const context = useContext(FacilitySearchContext);
  if (!context) {
    throw new Error(
      "useFacilitySearch must be used within a FacilitySearchProvider",
    );
  }
  return context;
}

export function FacilitySearchProvider({ children }: { children: ReactNode }) {
  const [selectedServiceId, setSelectedServiceId] = useState<string | null>(
    null,
  );
  const [selectedFacilityIds, setSelectedFacilityIds] = useState<string[]>([]);
  const [spatialSearch, setSpatialSearch] =
    useState<SpatialSearchOptions | null>(null);

  // 현재 적용된 CQL 필터 (검색에서 생성된 필터를 저장)
  const [appliedCQLFilter, setAppliedCQLFilter] = useState<string | null>(null);

  // 현재 서비스의 시설물 유형 목록
  const availableFacilities = useMemo(() => {
    if (!selectedServiceId) return [];
    return getServiceFacilities(selectedServiceId);
  }, [selectedServiceId]);
  const selectedServiceTitle = useMemo(() => {
    if (!selectedServiceId) return "";
    return getServiceInfo(selectedServiceId)?.title || "";
  }, [selectedServiceId]);

  // 선택된 시설물 정보들
  const selectedFacilities = useMemo(() => {
    return getFacilitiesByIds(selectedFacilityIds);
  }, [selectedFacilityIds]);


  // 서비스 선택 함수
  const selectService = useCallback((serviceId: string) => {
    setSelectedServiceId(serviceId);
    // 서비스 변경 시 모든 시설물 선택
    const facilities = getServiceFacilities(serviceId);
    setSelectedFacilityIds(facilities.map((f) => f.id));
    // 공간 검색 초기화
    setSpatialSearch(null);
  }, []);

  // 시설물 유형 선택/해제 함수
  const toggleFacility = useCallback((facilityId: string) => {
    setSelectedFacilityIds((prev) => {
      if (prev.includes(facilityId)) {
        // 이미 선택된 경우 제거
        return prev.filter((id) => id !== facilityId);
      } else {
        // 선택되지 않은 경우 추가
        return [...prev, facilityId];
      }
    });
  }, []);

  // 모든 시설물 선택
  const selectAllFacilities = useCallback(() => {
    const allIds = availableFacilities.map((f) => f.id);
    setSelectedFacilityIds(allIds);
  }, [availableFacilities]);

  // 모든 시설물 선택 해제
  const clearAllFacilities = useCallback(() => {
    setSelectedFacilityIds([]);
  }, []);

  const contextValue = useMemo(
    () => ({
      selectedServiceId,
      selectedServiceTitle,
      selectedFacilityIds,
      availableFacilities,
      spatialSearch,
      selectService,
      toggleFacility,
      selectAllFacilities,
      clearAllFacilities,
      setSpatialSearch,
      selectedFacilities,
      appliedCQLFilter,
      setAppliedCQLFilter,
    }),
    [
      selectedServiceId,
      selectedServiceTitle,
      selectedFacilityIds,
      availableFacilities,
      spatialSearch,
      selectedFacilities,
      appliedCQLFilter,
      selectService,
      toggleFacility,
      selectAllFacilities,
      clearAllFacilities,
    ],
  );

  return (
    <FacilitySearchContext.Provider value={contextValue}>
      {children}
    </FacilitySearchContext.Provider>
  );
}
