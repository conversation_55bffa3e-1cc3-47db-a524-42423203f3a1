"use client";

import { Layer } from "@geon-map/react-odf";
import { TOCNode } from "@geon-map/react-ui/types";
import { crtfckey, WMS_URL } from "@geon-query/model";
import { useCallback, useEffect, useState } from "react";

import { useFacilitySearch } from "@/app/service/_contexts/facility-search";

/**
 * 서비스에 따른 레이어를 자동으로 관리하는 컴포넌트
 *
 * 역할:
 * - 선택된 서비스 ID에 따라 TOC 데이터 로드
 * - 로드된 TOC 데이터에서 레이어만 추출하여 Layer 컴포넌트로 렌더링
 * - CQL 필터 적용
 */
export function ServiceLayerManager() {
  const { selectedServiceId, appliedCQLFilter } = useFacilitySearch();
  const [layerNodes, setLayerNodes] = useState<TOCNode[]>([]);

  console.log("[ServiceLayerManager] Render", {
    selectedServiceId,
    appliedCQLFilter,
    layerNodesCount: layerNodes.length,
  });

  /**
   * 서비스별 TOC 데이터를 로드합니다.
   */
  const loadTOCData = useCallback(async (): Promise<TOCNode[]> => {
    console.log("[ServiceLayerManager] loadTOCData called", {
      selectedServiceId,
    });

    if (!selectedServiceId) {
      console.log(
        "[ServiceLayerManager] No selectedServiceId, returning empty array",
      );
      return [];
    }

    try {
      // 공통 데이터는 항상 로드
      const commonModule = await import(`@/app/service/_data/toc/common`);
      const commonData = commonModule.TOC_DATA;
      console.log("[ServiceLayerManager] Common TOC data loaded", {
        commonDataLength: commonData.length,
      });

      try {
        // 서비스별 데이터 로드 시도
        const serviceModule = await import(
          `@/app/service/_data/toc/${selectedServiceId}`
        );
        const serviceData: TOCNode[] = serviceModule.TOC_DATA;
        console.log("[ServiceLayerManager] Service TOC data loaded", {
          selectedServiceId,
          serviceDataLength: serviceData.length,
        });

        // 서비스 데이터 + 공통 데이터 함께 반환
        const combinedData = [...serviceData, ...commonData];
        console.log("[ServiceLayerManager] Combined TOC data", {
          combinedDataLength: combinedData.length,
        });
        return combinedData;
      } catch (serviceError) {
        // 서비스 데이터 로드 실패 시 공통 데이터만 반환
        console.warn(
          "[ServiceLayerManager] Failed to load service TOC data, using common only",
          {
            selectedServiceId,
            error: serviceError,
          },
        );
        return commonData;
      }
    } catch (error) {
      console.error("[ServiceLayerManager] Failed to load TOC data", {
        selectedServiceId,
        error,
      });
      return [];
    }
  }, [selectedServiceId]);

  // 서비스 변경 시 레이어 추출 및 업데이트
  useEffect(() => {
    const extractLayers = async () => {
      if (!selectedServiceId) {
        setLayerNodes([]);
        return;
      }

      try {
        const tocData = await loadTOCData();
        const extractedLayerNodes: TOCNode[] = [];

        // TOC 데이터에서 레이어 노드만 추출
        const traverse = (nodes: TOCNode[]) => {
          nodes.forEach((node) => {
            if (node.type === "layer") {
              extractedLayerNodes.push(node);
            } else if (node.children) {
              traverse(node.children);
            }
          });
        };

        traverse(tocData);

        console.log("[ServiceLayerManager] Layer nodes extracted", {
          layerNodesCount: extractedLayerNodes.length,
          appliedCQLFilter,
        });

        setLayerNodes(extractedLayerNodes);
      } catch (error) {
        console.error("[ServiceLayerManager] Failed to extract layers", error);
        setLayerNodes([]);
      }
    };

    extractLayers();
  }, [selectedServiceId, loadTOCData]);

  // Layer 컴포넌트들을 렌더링
  return (
    <>
      {layerNodes.map((layerNode) => (
        <Layer
          key={layerNode.id}
          id={layerNode.id}
          config={{
            type: "geoserver",
            server: { url: WMS_URL },
            crtfckey,
            layer: layerNode.id,
            service: "wms",
            method: "post",
            fit: false,
          }}
          visible={layerNode.visible ?? true}
          opacity={layerNode.opacity ?? 1}
          cqlFilter={appliedCQLFilter}
          onLayerReady={(mapLayerId) => {
            console.log("[ServiceLayerManager] Layer ready", {
              originalId: layerNode.id,
              mapLayerId,
            });
          }}
          onLayerError={(error) => {
            console.error("[ServiceLayerManager] Layer error", {
              originalId: layerNode.id,
              error,
            });
          }}
        />
      ))}
    </>
  );
}
