"use client";

import { createGeonMagpClient } from "@geon-query/model";
import { useAppMutation, useAppQueryClient } from "@geon-query/react-query";
import { useRouter } from "next/navigation";
import React from "react";

import AttachmentUploader from "@/app/board/_components/attachment-uploader";
import { usePaginationRouter } from "@/app/board/_components/hooks/use-pagination-router";

import Edit from "./edit";
import { type FormState } from "./view";

const userId = "admin";

export default function Create() {
  const client = createGeonMagpClient();
  const router = useRouter();

  const initialForm: FormState = {
    nttSj: "",
    nttCn: "",
    updusrId: userId,
    registerId: userId,
    upperExpsrAt: false,
    smsSndngAt: false,
    othbcAt: true,
    pstgBeginDt: "",
    linkUrl: "",
    popupAt: false,
    popupBeginDt: "",
    popupEndDt: "",
    popupPortalExpsrAt: false,
    popupInsttExpsrAt: false,
    atchmnflId: null,
  };

  const [form, setForm] = React.useState<FormState>(initialForm);
  const [atchmnflId, setAtchmnflId] = React.useState<string | null>(null);

  const qc = useAppQueryClient();
  const insertMutation = useAppMutation({
    mutationFn: async () =>
      client.dataspace.insert({
        nttSj: form.nttSj,
        nttCn: form.nttCn,
        registerId: form.registerId,
        updusrId: userId,
        atchmnflId,
        popupBeginDt: form.popupBeginDt
          ? new Date(form.popupBeginDt).toISOString()
          : null,
        popupEndDt: form.popupEndDt
          ? new Date(form.popupEndDt).toISOString()
          : null,
      }),
    onSuccess: () => {
      alert("등록이 완료되었습니다.");
      qc.invalidateQueries({ queryKey: ["magp/dataspace"] });
      router.push(`/board/dataspace`);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    insertMutation.mutate();
  };

  const { push } = usePaginationRouter();

  return (
    <div className="mx-auto w-full max-w-5xl space-y-6 p-4">
      <h1 className="text-xl font-semibold">데이터공간 등록</h1>
      <Edit
        form={form}
        setForm={setForm}
        onSubmit={handleSubmit}
        isPending={insertMutation.isPending}
        onCancel={() => push(`/board/dataspace`)}
      >
        <AttachmentUploader
          registerId={userId}
          onUploaded={(id: string | null) => setAtchmnflId(id)}
        />
      </Edit>
    </div>
  );
}
