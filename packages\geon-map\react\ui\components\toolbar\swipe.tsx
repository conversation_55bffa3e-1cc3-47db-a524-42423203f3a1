"use client";

import { cn } from "@geon-ui/react/lib/utils";
import { Popover, PopoverTrigger } from "@geon-ui/react/primitives/popover";
import { UnfoldHorizontal } from "lucide-react";
import * as React from "react";

import { useSwipe } from "../../hooks/use-swipe";
import {
  ToolbarContent,
  ToolbarItem,
  ToolbarTrigger,
} from "./base/toolbar-item";

// Props for compound components - SwipeWidget props를 그대로 사용
export interface ToolbarSwipeProps
  extends Omit<
    React.HTMLAttributes<HTMLDivElement>,
    "value" | "onValueChange"
  > {
  /** 현재 스와이프 값 (0-100) */
  value: number;
  /** 스와이프 값 변경 콜백 */
  onValueChange: (value: number) => void;
  /** 왼쪽 레이어 ID */
  leftLayerId: string;
  /** 오른쪽 레이어 ID */
  rightLayerId: string;
  /** 활성화 상태 */
  enabled?: boolean;
  /** 활성화 상태 변경 콜백 */
  onEnabledChange: (enabled: boolean) => void;
  /** 레이어 변경 콜백 */
  onLeftLayerChange?: (layerId: string) => void;
  onRightLayerChange?: (layerId: string) => void;
}

export interface ToolbarSwipeTriggerProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 툴팁 텍스트 */
  tooltip?: string;
  /** 버튼 크기 */
  size?: "sm" | "lg" | "default" | "icon";
}

export interface ToolbarSwipeContentProps
  extends React.HTMLAttributes<HTMLDivElement> {}

// Swipe Context 생성
interface SwipeContextValue {
  value: number;
  onValueChange: (value: number) => void;
  leftLayerId: string;
  rightLayerId: string;
  enabled: boolean;
  onEnabledChange: (enabled: boolean) => void;
  onLeftLayerChange?: (layerId: string) => void;
  onRightLayerChange?: (layerId: string) => void;
  mapSize: [number, number] | null;
  handleLeftLayerChange: (layerId: string) => void;
  handleRightLayerChange: (layerId: string) => void;
  handleEnabledToggle: () => void;
}

const SwipeContext = React.createContext<SwipeContextValue | null>(null);

export const useSwipeContext = () => {
  const context = React.useContext(SwipeContext);
  if (!context) {
    throw new Error("ToolbarSwipe components must be used within ToolbarSwipe");
  }
  return context;
};

// Main ToolbarSwipe Container - useSwipe hook 사용
export const ToolbarSwipe = React.forwardRef<HTMLDivElement, ToolbarSwipeProps>(
  (
    {
      value,
      onValueChange,
      leftLayerId,
      rightLayerId,
      enabled = false,
      onEnabledChange,
      onLeftLayerChange,
      onRightLayerChange,
      className,
      children,
      ...props
    },
    ref,
  ) => {
    // useSwipe hook 사용
    const swipeResult = useSwipe({
      value,
      onValueChange,
      leftLayerId,
      rightLayerId,
      enabled,
      onEnabledChange,
      onLeftLayerChange,
      onRightLayerChange,
    });

    const contextValue = React.useMemo(
      () => ({
        value,
        onValueChange,
        leftLayerId,
        rightLayerId,
        enabled,
        onEnabledChange,
        onLeftLayerChange,
        onRightLayerChange,
        mapSize: swipeResult.mapSize,
        handleLeftLayerChange: swipeResult.handleLeftLayerChange,
        handleRightLayerChange: swipeResult.handleRightLayerChange,
        handleEnabledToggle: swipeResult.handleEnabledToggle,
      }),
      [
        value,
        onValueChange,
        leftLayerId,
        rightLayerId,
        enabled,
        onEnabledChange,
        onLeftLayerChange,
        onRightLayerChange,
        swipeResult,
      ],
    );

    return (
      <SwipeContext.Provider value={contextValue}>
        <ToolbarItem ref={ref} className={className} {...props}>
          <Popover>
            <PopoverTrigger asChild>
              {React.Children.toArray(children).find(
                (child) =>
                  React.isValidElement(child) &&
                  child.type === ToolbarSwipeTrigger,
              )}
            </PopoverTrigger>
            {React.Children.toArray(children).find(
              (child) =>
                React.isValidElement(child) &&
                child.type === ToolbarSwipeContent,
            )}
          </Popover>
        </ToolbarItem>
      </SwipeContext.Provider>
    );
  },
);

ToolbarSwipe.displayName = "ToolbarSwipe";

// ToolbarSwipeTrigger Component
export const ToolbarSwipeTrigger = React.forwardRef<
  HTMLButtonElement,
  ToolbarSwipeTriggerProps
>(
  (
    {
      tooltip = "스와이프 도구",
      size = "default",
      className,
      children,
      ...props
    },
    ref,
  ) => {
    const { enabled } = useSwipeContext();

    return (
      <ToolbarTrigger
        ref={ref}
        tooltip={tooltip}
        size={size}
        active={enabled}
        className={className}
        {...props}
      >
        {children || <UnfoldHorizontal className="h-4 w-4" />}
      </ToolbarTrigger>
    );
  },
);

ToolbarSwipeTrigger.displayName = "ToolbarSwipeTrigger";

// ToolbarSwipeContent Component
export const ToolbarSwipeContent = React.forwardRef<
  HTMLDivElement,
  ToolbarSwipeContentProps
>(({ className, children, ...props }, ref) => {
  return (
    <ToolbarContent
      ref={ref}
      align="center"
      sideOffset={16}
      className={cn("w-80 flex flex-col gap-4 p-4", className)}
      {...props}
    >
      {children}
    </ToolbarContent>
  );
});

ToolbarSwipeContent.displayName = "ToolbarSwipeContent";
