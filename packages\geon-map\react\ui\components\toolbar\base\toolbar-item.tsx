"use client";

import { cn } from "@geon-ui/react/lib/utils";
import { PopoverContent } from "@geon-ui/react/primitives/popover";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@geon-ui/react/primitives/tooltip";
import * as React from "react";

import { Button } from "./button";

// Position에 따른 tooltip side 자동 결정
const getTooltipSide = (
  position?: string,
): "top" | "bottom" | "left" | "right" => {
  if (!position) return "bottom";

  if (position.includes("right")) return "left";
  if (position.includes("left")) return "right";
  if (position.includes("top")) return "bottom";
  if (position.includes("bottom")) return "top";

  return "bottom";
};

// Position에 따른 popover side 자동 결정
const getPopoverSide = (
  position?: string,
): "top" | "bottom" | "left" | "right" => {
  if (!position) return "bottom";

  if (position.includes("right")) return "left";
  if (position.includes("left")) return "right";
  if (position.includes("top")) return "bottom";
  if (position.includes("bottom")) return "top";

  return "bottom";
};

export interface ToolbarItemProps extends React.HTMLAttributes<HTMLDivElement> {
  /** 트리거 방식 */
  trigger?: "click" | "hover";
  /** 팝오버 비활성화 */
  disablePopover?: boolean;
  /** 툴바 위치 (tooltip 방향 결정용) */
  position?:
    | "top-left"
    | "top-center"
    | "top-right"
    | "center-left"
    | "center-right"
    | "bottom-left"
    | "bottom-center"
    | "bottom-right";
}

export interface ToolbarTriggerProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 툴팁 텍스트 */
  tooltip?: string;
  /** 버튼 크기 */
  size?: "sm" | "lg" | "default" | "icon";
  /** 버튼 스타일 */
  variant?: "default" | "ghost" | "outline" | "destructive" | "secondary";
  /** 활성 상태 */
  active?: boolean;
  /** 아이콘만 표시 */
  iconOnly?: boolean;
}

export interface ToolbarContentProps
  extends React.HTMLAttributes<HTMLDivElement> {
  /** 팝오버 표시 방향 */
  side?: "top" | "bottom" | "left" | "right";
  /** 팝오버 정렬 */
  align?: "start" | "center" | "end";
  /** 팝오버 오프셋 */
  sideOffset?: number;
  /** 최대 너비 */
  maxWidth?: string;
}

// Context for ToolbarItem
interface ToolbarItemContextValue {
  trigger: "click" | "hover";
  disablePopover: boolean;
  position?:
    | "top-left"
    | "top-center"
    | "top-right"
    | "center-left"
    | "center-right"
    | "bottom-left"
    | "bottom-center"
    | "bottom-right";
}

const ToolbarItemContext = React.createContext<ToolbarItemContextValue | null>(
  null,
);

const useToolbarItemContext = () => {
  const context = React.useContext(ToolbarItemContext);
  if (!context) {
    throw new Error(
      "ToolbarTrigger and ToolbarContent must be used within ToolbarItem",
    );
  }
  return context;
};

// ToolbarItem Component
export const ToolbarItem = React.forwardRef<HTMLDivElement, ToolbarItemProps>(
  (
    { trigger = "click", disablePopover = false, position, children, ...props },
    ref,
  ) => {
    const contextValue = React.useMemo(
      () => ({ trigger, disablePopover, position }),
      [trigger, disablePopover, position],
    );

    return (
      <ToolbarItemContext.Provider value={contextValue}>
        <div ref={ref} {...props}>
          {children}
        </div>
      </ToolbarItemContext.Provider>
    );
  },
);

ToolbarItem.displayName = "ToolbarItem";

// ToolbarTrigger Component
export const ToolbarTrigger = React.forwardRef<
  HTMLButtonElement,
  ToolbarTriggerProps
>(
  (
    {
      tooltip,
      size = "default",
      variant = "secondary",
      active = false,
      iconOnly = true,
      className,
      children,
      onClick,
      ...props
    },
    ref,
  ) => {
    const context = useToolbarItemContext();

    // Position 기반 tooltip side 결정
    const tooltipSide = getTooltipSide(context.position);

    // preset.css 변수와 추가 toolbar 스타일 적용
    const toolbarEnhancedClasses = cn(
      // preset.css 변수 기반 스타일
      "cursor-pointer",
      "rounded-[var(--geon-toolbar-radius)]",
      "border-[var(--geon-toolbar-border-width)]",

      // 아이콘 전용 크기 조정
      iconOnly && size === "sm" && "h-8 w-8 p-0",
      iconOnly && size === "default" && "h-10 w-10 p-0",
      iconOnly && size === "lg" && "h-12 w-12 p-0",
      iconOnly && size === "icon" && "h-9 w-9 p-0",

      className,
    );

    const buttonElement = (
      <Button
        ref={ref}
        variant={variant}
        size={size}
        active={active}
        className={toolbarEnhancedClasses}
        onClick={onClick}
        {...props}
      >
        {children}
      </Button>
    );

    // 툴팁이 있는 경우 래핑
    if (tooltip) {
      return (
        <TooltipProvider delayDuration={500}>
          <Tooltip>
            <TooltipTrigger asChild>{buttonElement}</TooltipTrigger>
            <TooltipContent side={tooltipSide}>
              <p>{tooltip}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    return buttonElement;
  },
);

ToolbarTrigger.displayName = "ToolbarTrigger";

// ToolbarContent Component
export const ToolbarContent = React.forwardRef<
  HTMLDivElement,
  ToolbarContentProps
>(
  (
    {
      side = "bottom",
      align = "center",
      sideOffset = 16,
      maxWidth = "320px",
      className,
      children,
      ...props
    },
    ref,
  ) => {
    const context = useToolbarItemContext();

    // Position 기반 popover side 결정 (side prop이 명시적으로 전달되지 않은 경우)
    const autoSide = getPopoverSide(context.position);
    const finalSide = side === "bottom" ? autoSide : side; // 기본값인 경우만 자동 적용

    if (context.disablePopover) {
      return (
        <div ref={ref} className={className} {...props}>
          {children}
        </div>
      );
    }

    return (
      <PopoverContent
        ref={ref}
        side={finalSide}
        align={align}
        sideOffset={sideOffset}
        className={cn(
          "z-50 rounded-lg border border-geon-border bg-geon-popover text-geon-popover-foreground shadow-lg outline-none",
          "backdrop-blur-md bg-geon-popover/95",
          "data-[state=open]:animate-in data-[state=closed]:animate-out",
          "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
          "data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
          "data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2",
          "data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
          "transition-all duration-200 ease-out",
          className,
        )}
        style={{ maxWidth }}
        {...props}
      >
        {children}
      </PopoverContent>
    );
  },
);

ToolbarContent.displayName = "ToolbarContent";
