// 시설물 검색 관련 타입 정의

export type FacilityType = {
  /** 시설물 유형 고유 ID */
  id: string;
  /** 시설물 유형명 */
  title: string;
  /** 시설물 설명 */
  description?: string;
  /** 검색 API 서비스 ID */
  serviceId: string;
  /** 시설물 활성화 여부 */
  enabled?: boolean;
  /** 시설물 카테고리 */
  category?: string;
  /** 아이콘 */
  icon?: string;
  /** 순서 */
  order?: number;
  /** 색상 (UI에서 구분용) */
  color?: string;
};

export type ServiceFacilityGroup = {
  /** 서비스 ID */
  serviceId: string;
  /** 서비스 제목 */
  title: string;
  /** 해당 서비스의 시설물 유형 목록 */
  facilityTypes: FacilityType[];
};

export type SpatialSearchType = "point" | "polygon" | "radius" | "freehand";

export type SpatialSearchOptions = {
  /** 공간 검색 타입 */
  type: SpatialSearchType;
  /** 검색 영역 좌표 */
  coordinates?: any;
  /** 반경 (type이 radius일 때) */
  radius?: number;
  /** 추가 옵션 */
  options?: Record<string, any>;
};

export type FacilitySearchContextValue = {
  /** 현재 선택된 서비스 ID */
  selectedServiceId: string | null;
  /** 현재 선택된 서비스 제목 */
  selectedServiceTitle: string;
  /** 선택된 시설물 유형 ID 목록 */
  selectedFacilityIds: string[];
  /** 현재 서비스에서 사용 가능한 시설물 유형들 */
  availableFacilities: FacilityType[];
  /** 공간 검색 옵션 */
  spatialSearch: SpatialSearchOptions | null;
  /** 서비스 선택 */
  selectService: (serviceId: string) => void;
  /** 시설물 유형 선택/해제 */
  toggleFacility: (facilityId: string) => void;
  /** 모든 시설물 선택 */
  selectAllFacilities: () => void;
  /** 모든 시설물 선택 해제 */
  clearAllFacilities: () => void;
  /** 공간 검색 설정 */
  setSpatialSearch: (options: SpatialSearchOptions | null) => void;
  /** 선택된 시설물 정보들 */
  selectedFacilities: FacilityType[];
  /** 현재 적용된 CQL 필터 (검색에서 생성된 필터) */
  appliedCQLFilter: string | null;
  /** CQL 필터 설정 함수 */
  setAppliedCQLFilter: (filter: string | null) => void;
};
