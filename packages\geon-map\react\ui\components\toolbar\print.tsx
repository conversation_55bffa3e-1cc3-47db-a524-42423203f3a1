"use client";

import type { CaptureResult } from "@geon-map/core";
import { useMap } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@geon-ui/react/primitives/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@geon-ui/react/primitives/select";
import {
  Camera,
  Download,
  Eye,
  Printer,
  Square,
  StickyNote,
} from "lucide-react";
import * as React from "react";

import { type UseDialogReturn, usePrint } from "../../hooks";
import type { CaptureType, PaperSize } from "../../types/print-types";
import { PAPER_SIZE_LABELS } from "../../types/print-types";
import { ToolbarItem, ToolbarTrigger } from "./base/toolbar-item";

// Props for compound components
export interface ToolbarPrintProps
  extends React.HTMLAttributes<HTMLDivElement> {
  /** 캡처 모드 활성화 */
  enableCaptureMode?: boolean;
}

export interface ToolbarPrintTriggerProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 툴팁 텍스트 */
  tooltip?: string;
  /** 버튼 크기 */
  size?: "sm" | "lg" | "default" | "icon";
}

export interface ToolbarPrintContentProps
  extends React.HTMLAttributes<HTMLDivElement> {}

export interface ToolbarPrintCaptureOptionProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 캡처 타입 */
  captureType: CaptureType;
  /** 아이콘 */
  icon?: React.ReactNode;
  /** 라벨 */
  label?: string;
}

// Print Context 생성
interface PrintContextValue {
  enableCaptureMode: boolean;

  // 상태
  currentPaperSize: PaperSize;
  captureResult?: CaptureResult | null;
  isCapturing: boolean;

  // 다이얼로그 컨트롤
  dialogs: {
    capture: UseDialogReturn;
    paper: UseDialogReturn;
    original: UseDialogReturn;
  };

  getPaperSizeInPixels: (
    paperSize: PaperSize,
    dpi?: number,
    includeMargin?: boolean,
    marginMm?: number,
  ) => {
    width: number;
    height: number;
    fullWidth: number;
    fullHeight: number;
    marginPx: number;
    dpi: number;
    paperSize: PaperSize;
  };

  // 액션
  changePaperSize: (size: PaperSize) => void;
  startArea: () => void;
  captureCurrentView: () => void;
  startPaperCapture: () => void;
  saveCapturedPng: () => Promise<boolean>;
  saveCanvasPng: (canvas: HTMLCanvasElement) => Promise<boolean>;
  saveCapturedPdf: () => Promise<boolean>;
  saveCanvasPdf: (canvas: HTMLCanvasElement) => Promise<boolean>;
  printCanvas: (canvas: HTMLCanvasElement) => Promise<boolean>;
  printCaptured: () => Promise<boolean>;
  handlePrintClick: () => void;
  openOriginalPreview: () => void;
}

const PrintContext = React.createContext<PrintContextValue | null>(null);

export const usePrintContext = () => {
  const context = React.useContext(PrintContext);
  if (!context) {
    throw new Error("ToolbarPrint components must be used within ToolbarPrint");
  }
  return context;
};

// Main ToolbarPrint Container
export const ToolbarPrint = React.forwardRef<HTMLDivElement, ToolbarPrintProps>(
  ({ enableCaptureMode = false, className, children, ...props }, ref) => {
    const {
      // 다이얼로그 컨트롤
      dialogs,

      // 상태
      currentPaperSize,
      isCapturing,
      captureResult,

      getPaperSizeInPixels,

      // 액션
      changePaperSize,
      startArea,
      captureCurrentView,
      startPaperCapture,
      saveCapturedPng,
      saveCanvasPng,
      saveCapturedPdf,
      saveCanvasPdf,
      printCanvas,
      printCaptured,
      handlePrintClick,
      openOriginalPreview,
    } = usePrint({ enableCaptureMode });

    const contextValue = React.useMemo(
      () => ({
        enableCaptureMode,

        dialogs,
        currentPaperSize,
        captureResult,
        isCapturing,

        getPaperSizeInPixels,

        changePaperSize,
        startArea,
        captureCurrentView,
        startPaperCapture,
        saveCapturedPng,
        saveCanvasPng,
        saveCapturedPdf,
        saveCanvasPdf,
        printCanvas,
        printCaptured,
        handlePrintClick,
        openOriginalPreview,
      }),
      [
        enableCaptureMode,
        dialogs,
        currentPaperSize,
        captureResult,
        isCapturing,
        getPaperSizeInPixels,
        changePaperSize,
        startArea,
        captureCurrentView,
        startPaperCapture,
        saveCapturedPng,
        saveCanvasPng,
        saveCapturedPdf,
        saveCanvasPdf,
        printCanvas,
        printCaptured,
        handlePrintClick,
        openOriginalPreview,
      ],
    );

    return (
      <PrintContext.Provider value={contextValue}>
        <ToolbarItem ref={ref} className={className} {...props}>
          {children}
        </ToolbarItem>
      </PrintContext.Provider>
    );
  },
);

ToolbarPrint.displayName = "ToolbarPrint";

// ToolbarPrintTrigger Component
export const ToolbarPrintTrigger = React.forwardRef<
  HTMLButtonElement,
  ToolbarPrintTriggerProps
>(
  (
    {
      tooltip = "인쇄",
      size = "default",
      className,
      children,
      onClick,
      ...props
    },
    ref,
  ) => {
    const { dialogs } = usePrintContext();

    const handleClick = React.useCallback(
      (e: React.MouseEvent<HTMLButtonElement>) => {
        dialogs.capture.openDialog();
        onClick?.(e);
      },
      [dialogs.capture, onClick],
    );

    return (
      <ToolbarTrigger
        ref={ref}
        tooltip={tooltip}
        size={size}
        className={className}
        onClick={handleClick}
        {...props}
      >
        {children || <Printer className="h-4 w-4" />}
      </ToolbarTrigger>
    );
  },
);

ToolbarPrintTrigger.displayName = "ToolbarPrintTrigger";

// ToolbarPrintContent Component (Capture Dialog)
export const ToolbarPrintContent = React.forwardRef<
  HTMLDivElement,
  ToolbarPrintContentProps
>(({ className, children, ...props }, ref) => {
  const {
    dialogs: { capture },
  } = usePrintContext();

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      capture.closeDialog();
    } else {
      capture.openDialog();
    }
  };

  return (
    <Dialog open={capture.open} onOpenChange={handleOpenChange}>
      <DialogContent
        ref={ref}
        className={cn(
          "max-w-md z-[99999999] bg-geon-background border-geon-border",
          className,
        )}
        {...props}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-geon-foreground">
            <Printer className="w-5 h-5 text-geon-primary" />
            인쇄 및 캡처
          </DialogTitle>
          <DialogDescription className="text-geon-muted-foreground">
            인쇄 및 캡처 옵션을 선택하세요.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 캡처 옵션 */}
          <ToolbarPrintCaptureOptions />

          {/* 미리보기 */}
          <ToolbarPrintCapturePreview />

          {/* 액션 버튼들 */}
          <ToolbarPrintCaptureActions />
        </div>

        {children}
      </DialogContent>
    </Dialog>
  );
});

ToolbarPrintContent.displayName = "ToolbarPrintContent";

// ToolbarPrintCaptureOptions Component
export const ToolbarPrintCaptureOptions = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn("grid grid-cols-3 gap-3", className)}
      {...props}
    >
      <ToolbarPrintCaptureOption captureType="area" />
      <ToolbarPrintCaptureOption captureType="current-view" />
      <ToolbarPrintCaptureOption captureType="paper" />
      {children}
    </div>
  );
});

ToolbarPrintCaptureOptions.displayName = "ToolbarPrintCaptureOptions";

// ToolbarPrintCaptureOption Component
export const ToolbarPrintCaptureOption = React.forwardRef<
  HTMLButtonElement,
  ToolbarPrintCaptureOptionProps
>(({ captureType, icon, label, className, onClick, ...props }, ref) => {
  const { startArea, captureCurrentView, startPaperCapture } =
    usePrintContext();

  const defaultIcons = {
    area: <Square className="w-6 h-6 text-geon-primary" />,
    "current-view": <Camera className="w-6 h-6 text-geon-primary" />,
    paper: <StickyNote className="w-6 h-6 text-geon-primary" />,
  };

  const defaultLabels = {
    area: "영역 캡처",
    "current-view": "화면 캡처",
    paper: "용지별 캡처",
  };

  const defaultHandlers = React.useMemo(
    () => ({
      area: startArea,
      "current-view": captureCurrentView,
      paper: startPaperCapture,
    }),
    [startArea, captureCurrentView, startPaperCapture],
  );

  const handleClick = React.useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      defaultHandlers[captureType]();
      onClick?.(e);
    },
    [captureType, onClick, defaultHandlers],
  );

  return (
    <Button
      ref={ref}
      variant="outline"
      className={cn(
        "h-20 flex flex-col items-center gap-2",
        "bg-geon-background text-geon-foreground",
        "border-geon-border hover:bg-geon-hover",
        className,
      )}
      onClick={handleClick}
      {...props}
    >
      {icon || defaultIcons[captureType]}
      <span className="text-sm">{label || defaultLabels[captureType]}</span>
    </Button>
  );
});

ToolbarPrintCaptureOption.displayName = "ToolbarPrintCaptureOption";

// ToolbarPrintCapturePreview Component
export const ToolbarPrintCapturePreview = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => {
  const { captureResult } = usePrintContext();

  const renderPreview = () => {
    if (captureResult) {
      return (
        <img
          src={captureResult.canvas.toDataURL()}
          alt="캡처된 이미지"
          className="w-full h-auto max-h-48 object-contain rounded"
        />
      );
    }

    return (
      <p className="text-center text-geon-muted-foreground text-sm">
        캡처할 영역을 선택하세요
      </p>
    );
  };

  return (
    <div
      ref={ref}
      className={cn(
        "border rounded-lg p-4 min-h-32 flex items-center justify-center",
        "bg-geon-muted border-geon-border",
        className,
      )}
      {...props}
    >
      {renderPreview()}
      {children}
    </div>
  );
});

ToolbarPrintCapturePreview.displayName = "ToolbarPrintCapturePreview";

// ToolbarPrintCaptureActions Component
export const ToolbarPrintCaptureActions = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => {
  const {
    saveCapturedPng,
    saveCapturedPdf,
    printCaptured,
    captureResult,
    openOriginalPreview,
  } = usePrintContext();

  return (
    <div ref={ref} className={cn("space-y-3", className)} {...props}>
      {/* 원본 보기 버튼 */}
      {captureResult && (
        <Button
          className={cn(
            "w-full",
            "bg-geon-background text-geon-foreground",
            "border-geon-border hover:bg-geon-hover",
          )}
          variant="outline"
          onClick={openOriginalPreview}
        >
          <Eye className="w-4 h-4 mr-2 text-geon-primary" />
          원본크기로 보기
        </Button>
      )}

      {/* 저장 버튼들 */}
      <div className="flex gap-2">
        <Button
          className={cn(
            "flex-1",
            "bg-geon-background text-geon-foreground",
            "border-geon-border hover:bg-geon-hover",
          )}
          variant="outline"
          onClick={saveCapturedPng}
          disabled={!captureResult}
        >
          <Download className="w-4 h-4 mr-2 text-geon-primary" />
          PNG 저장
        </Button>
        <Button
          className={cn(
            "flex-1",
            "bg-geon-background text-geon-foreground",
            "border-geon-border hover:bg-geon-hover",
          )}
          variant="outline"
          onClick={saveCapturedPdf}
          disabled={!captureResult}
        >
          <Download className="w-4 h-4 mr-2 text-geon-primary" />
          PDF 저장
        </Button>
      </div>

      {/* 인쇄 버튼 */}
      <Button
        className={cn(
          "w-full",
          "bg-geon-primary text-geon-primary-foreground",
          "hover:bg-geon-primary/90",
        )}
        onClick={printCaptured}
        disabled={!captureResult}
      >
        <Printer className="w-4 h-4 mr-2" />
        인쇄하기
      </Button>

      {children}
    </div>
  );
});

ToolbarPrintCaptureActions.displayName = "ToolbarPrintCaptureActions";

// ToolbarPrintActions Component (Legacy - 호환성용)
const ToolbarPrintActions = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  // Legacy 호환성을 위해 ToolbarPrintCaptureActions를 래핑
  return (
    <ToolbarPrintCaptureActions ref={ref} className={className} {...props} />
  );
});

ToolbarPrintActions.displayName = "ToolbarPrintActions";

// ToolbarPrintOriginalDialog Component
export const ToolbarPrintOriginalDialog = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ ...props }, ref) => {
  const {
    captureResult,
    dialogs: { original },
  } = usePrintContext();

  if (!captureResult) return null;

  const { canvas } = captureResult;
  const imageWidth = canvas.width;
  const imageHeight = canvas.height;

  // 최대 크기 제한만 설정
  const maxDialogWidth =
    typeof window !== "undefined"
      ? Math.min(window.innerWidth * 0.9, 1200)
      : 1200;
  const maxDialogHeight =
    typeof window !== "undefined"
      ? Math.min(window.innerHeight * 0.9, 800)
      : 800;

  return (
    <Dialog open={original.open} onOpenChange={original.onOpenChange}>
      <DialogContent
        className={cn(
          "z-[99999999] p-0 flex flex-col w-auto h-auto overflow-hidden",
          "bg-geon-background border-geon-border",
        )}
        style={{
          maxWidth: maxDialogWidth,
          maxHeight: maxDialogHeight,
        }}
        ref={ref}
        {...props}
      >
        {/* 고정 헤더 */}
        <DialogHeader className="p-6 pb-4 flex-shrink-0 border-b border-geon-border">
          <DialogTitle className="flex items-center justify-between text-geon-foreground">
            <span>캡처 이미지 - 원본크기</span>
            <div className="flex flex-col items-end text-sm font-normal text-geon-muted-foreground">
              <span>
                {imageWidth} × {imageHeight}px
              </span>
              <span className="text-xs text-geon-primary">100% 원본크기</span>
            </div>
          </DialogTitle>
          <DialogDescription className="text-geon-muted-foreground">
            캡처된 이미지를 원본크기로 확인할 수 있습니다.
          </DialogDescription>
        </DialogHeader>

        {/* 스크롤 가능한 이미지 영역 */}
        <div
          className="flex-1 overflow-auto p-6"
          style={{ minWidth: 0, minHeight: 0 }}
        >
          <div
            className="flex items-center justify-center"
            style={{
              minWidth: imageWidth + 48,
              minHeight: imageHeight + 48,
            }}
          >
            <div className="border rounded bg-geon-background shadow-sm border-geon-border">
              <img
                src={canvas.toDataURL()}
                alt="캡처된 이미지 원본"
                className="block rounded"
                style={{
                  width: imageWidth,
                  height: imageHeight,
                }}
              />
            </div>
          </div>
        </div>

        {/* 고정 푸터 */}
        <div className="px-6 pb-4 flex-shrink-0 border-t bg-geon-muted border-geon-border">
          <p className="text-xs text-geon-muted-foreground text-center py-2">
            이미지를 원본 크기로 표시합니다. 스크롤하여 전체를 확인하세요.
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
});

ToolbarPrintOriginalDialog.displayName = "ToolbarPrintOriginalDialog";

// ToolbarPrintPaperSizeSelect Component
interface ToolbarPrintPaperSizeSelectProps {
  className?: string;
  value?: string;
  defaultValue?: string;
  onValueChange?: (value: string) => void;
}

export const ToolbarPrintPaperSizeSelect = React.forwardRef<
  HTMLButtonElement,
  ToolbarPrintPaperSizeSelectProps
>(({ className, ...props }, ref) => {
  const { currentPaperSize, changePaperSize } = usePrintContext();

  return (
    <Select value={currentPaperSize} onValueChange={changePaperSize} {...props}>
      <SelectTrigger
        ref={ref}
        className={cn(
          "w-full bg-geon-background text-geon-foreground border-geon-border",
          className,
        )}
      >
        <SelectValue placeholder="용지를 선택해주세요." />
      </SelectTrigger>
      <SelectContent className="z-[99999999] bg-geon-background border-geon-border">
        {Object.entries(PAPER_SIZE_LABELS).map(([key, label]) => (
          <SelectItem
            key={key}
            value={key}
            className="text-geon-foreground hover:bg-geon-hover"
          >
            {label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
});

ToolbarPrintPaperSizeSelect.displayName = "ToolbarPrintPaperSizeSelect";

// ToolbarPrintPaperDialog Component
export const ToolbarPrintPaperDialog = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ ...props }, ref) => {
  const {
    dialogs: { paper },
    getPaperSizeInPixels,
    currentPaperSize,
    saveCanvasPng,
    saveCanvasPdf,
    printCanvas,
  } = usePrintContext();

  const { odf } = useMap();
  const [map, setMap] = React.useState<any | null>(null);

  React.useEffect(() => {
    if (map) {
      const pageDpi = {
        A0: 110,
        A1: 150,
        A2: 180,
        A3: 200,
        A4: 240,
        "A0-L": 110,
        "A1-L": 150,
        "A2-L": 180,
        "A3-L": 200,
        "A4-L": 240,
      };
      const paperPixels = getPaperSizeInPixels(
        currentPaperSize,
        pageDpi[currentPaperSize],
        false,
        0,
      );

      // 1. DOM 컨테이너 크기 설정
      const mapContainer = map.getTarget();
      if (mapContainer) {
        mapContainer.style.width = `${paperPixels.width}px`;
        mapContainer.style.height = `${paperPixels.height}px`;
      }
      // 2. 지도 크기 업데이트
      map.updateSize();
    }
  }, [currentPaperSize, getPaperSizeInPixels, map]);

  React.useEffect(() => {
    // Dialog가 열려있고 ODF가 준비되었을 때만 실행
    if (!paper.open || !odf) return;
    // 임시소스
    setTimeout(() => {
      const mapContainer = document.getElementById("print-widget-map");
      const coord = new odf.Coordinate(199312.9996, 551784.6924);
      const mapOption = {
        center: coord,
        zoom: 11,
        projection: "EPSG:5186",
        baroEMapURL: "https://geon-gateway.geon.kr/map/api/map/baroemap",
        baroEMapAirURL: "https://geon-gateway.geon.kr/map/api/map/ngisair",
        basemap: {
          baroEMap: ["eMapAIR", "eMapColor", "eMapWhite"],
        },
        pixelRatio: 1,
        optimization: true,
      };
      const map = new odf.Map(mapContainer, mapOption);
      setMap(map);
    }, 1000);
  }, [odf, paper.open]);

  const printMap = async () => {
    if (map) {
      const mapCanvas = document.querySelector(
        "#print-widget-map canvas",
      ) as HTMLCanvasElement;
      if (mapCanvas) {
        await printCanvas(mapCanvas);
      }
    }
  };

  const downloadPng = async () => {
    if (map) {
      const mapCanvas = document.querySelector(
        "#print-widget-map canvas",
      ) as HTMLCanvasElement;
      if (mapCanvas) {
        await saveCanvasPng(mapCanvas);
      }
    }
  };

  const downloadPdf = async () => {
    if (map) {
      const mapCanvas = document.querySelector(
        "#print-widget-map canvas",
      ) as HTMLCanvasElement;
      if (mapCanvas) {
        await saveCanvasPdf(mapCanvas);
      }
    }
  };

  return (
    <Dialog open={paper.open} onOpenChange={paper.onOpenChange}>
      <DialogContent
        className={cn(
          "z-[99999999] p-0 flex flex-col",
          "bg-geon-background border-geon-border",
        )}
        ref={ref}
        style={{
          width: "min(95vw, 1000px)",
          height: "min(95vh, 800px)",
          maxWidth: "none",
          maxHeight: "none",
        }}
        {...props}
      >
        {/* 고정 헤더 */}
        <DialogHeader className="p-6 pb-4 flex-shrink-0 border-b border-geon-border">
          <DialogTitle className="flex items-center justify-between text-geon-foreground">
            <span>용지별 캡쳐(테스트용)</span>
          </DialogTitle>
          <DialogDescription className="text-geon-muted-foreground">
            용지를 선택하고, 지도를 이동해보세요.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-auto p-6">
          <div className="space-y-4">
            {/* 용지 선택 */}
            <div className="flex items-center justify-center gap-3">
              <ToolbarPrintPaperSizeSelect />
              <div className="flex space-x-2">
                <Button
                  onClick={printMap}
                  variant="outline"
                  className={cn(
                    "flex items-center gap-2 rounded-md",
                    "bg-geon-background text-geon-foreground",
                    "border-geon-border hover:bg-geon-hover",
                  )}
                >
                  <Printer className="h-4 w-4 text-geon-primary" />
                  인쇄
                </Button>

                <Button
                  onClick={downloadPng}
                  variant="outline"
                  className={cn(
                    "flex items-center gap-2 rounded-md",
                    "bg-geon-background text-geon-foreground",
                    "border-geon-border hover:bg-geon-hover",
                  )}
                >
                  <Download className="h-4 w-4 text-geon-primary" />
                  PNG 저장
                </Button>

                <Button
                  onClick={downloadPdf}
                  variant="outline"
                  className={cn(
                    "flex items-center gap-2 rounded-md",
                    "bg-geon-background text-geon-foreground",
                    "border-geon-border hover:bg-geon-hover",
                  )}
                >
                  <Download className="h-4 w-4 text-geon-primary" />
                  PDF 저장
                </Button>
              </div>
            </div>
            {/* 지도 미리보기 */}
            <div className="w-auto border rounded border-geon-border">
              <div
                id="print-widget-map"
                className="max-h-screen h-screen max-w-screen w-screen"
              ></div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
});

ToolbarPrintPaperDialog.displayName = "ToolbarPrintPaperDialog";
