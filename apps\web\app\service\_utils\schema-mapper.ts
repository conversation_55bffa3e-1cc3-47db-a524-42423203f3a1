import type { TableSchema } from "../_types/dynamic-table";
import type { FacilityDetailData } from "../_types/facility-detail";

/**
 * 데이터베이스 컬럼명과 시설물 데이터 필드 매핑 규칙
 */
export const COLUMN_MAPPING: Record<string, keyof FacilityDetailData> = {
  ftrCde: "featureCode",
  ftrIdn: "managementNumber",
  hjdCde: "adminDistrict",
  shtNum: "mapSheetNumber",
  mngCde: "managementAgency",
  istYmd: "installStartDate",
  bjdCde: "adminDistrict", // 법정동도 행정구역으로 매핑
  cntNum: "facilityId", // 공사번호를 시설물ID로 매핑
};

/**
 * 시설물 데이터를 데이터베이스 스키마에 맞게 변환
 */
export function mapFacilityDataToSchema(
  data: FacilityDetailData,
  schema: TableSchema,
): Record<string, any> {
  const mappedData: Record<string, any> = {};

  schema.columns.forEach((column) => {
    const fieldName = COLUMN_MAPPING[column.columnName];
    if (fieldName && data[fieldName] !== undefined) {
      mappedData[column.columnName] = data[fieldName];
    } else {
      // 매핑되지 않은 컬럼은 기본값 설정
      mappedData[column.columnName] = getDefaultValue(column.columnType);
    }
  });

  return mappedData;
}

/**
 * 컬럼 타입에 따른 기본값 반환
 */
function getDefaultValue(columnType: string): any {
  const upperType = columnType.toUpperCase();

  if (
    upperType.includes("INTEGER") ||
    upperType.includes("NUMERIC") ||
    upperType.includes("BIGINT")
  ) {
    return null;
  }

  if (upperType.includes("TIMESTAMP") || upperType.includes("DATE")) {
    return null;
  }

  if (upperType === "USER_DEFINED" || upperType.includes("GEOM")) {
    return null;
  }

  return "";
}

/**
 * 컬럼명 한글 표시명 매핑
 */
export const COLUMN_DISPLAY_NAMES: Record<string, string> = {
  ftrCde: "시설물 코드",
  ftrIdn: "시설물 관리번호",
  hjdCde: "행정동 코드",
  shtNum: "도엽번호",
  mngCde: "관리기관 코드",
  istYmd: "설치일자",
  cntNum: "공사번호",
  sysChk: "시스템 여부",
  bjdCde: "법정동 코드",
  sourceTable: "원본 테이블명",
};

/**
 * 특정 시설물 타입에 대한 컬럼 제외 규칙
 */
export const FACILITY_COLUMN_EXCLUSIONS: Record<string, string[]> = {
  road: ["gid", "geom", "sourceTable"],
  water: ["gid", "geom", "sourceTable"],
  building: ["gid", "geom", "sourceTable"],
};
