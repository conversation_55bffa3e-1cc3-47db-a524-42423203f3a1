"use client";

import { But<PERSON> } from "@geon-map/react-ui/components";
import type {
  AdminClient,
  APIRequestType,
  APIResponseType,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Badge } from "@geon-ui/react/primitives/badge";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import { createColumnHelper } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import React from "react";

import { AdminDialog } from "@/app/admin/_components/admin-dialog";
import { AdminPopover } from "@/app/admin/_components/admin-popover";
import { Pagination, ViewTable } from "@/components/table";

export const menuList: { name: string }[] = [
  { name: "도로관리" },
  { name: "상수관리" },
  { name: "하수관리" },
  { name: "인허가" },
  { name: "해양정보" },
  { name: "용도분석" },
];

export default function AdminTable({
  client,
  ...props
}: APIRequestType<AdminClient["author"]["business"]> & {
  client: AdminClient;
}) {
  const t = useTranslations("admin.author.business");

  const [pageSize, setPageSize] = React.useState<number>(props.pageSize);
  const [pageIndex, setPageIndex] = React.useState<number>(props.pageIndex);

  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<AdminClient["author"]["business"]>
  >({
    queryKey: [
      "author/business",
      { ...props, authorType: "business", pageSize, pageIndex },
    ],
    queryFn: () =>
      client.author.business({
        ...props,
        authorType: "business",
        pageSize,
        pageIndex,
      }),
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError || !data || typeof data.result === "string")
    return (
      <div className="text-destructive flex justify-center align-middle">
        Error loading parcel data: {error as string}
        {data && `, ${data?.result as unknown as string}`}
      </div>
    );

  const helper = createColumnHelper<(typeof data.result.resultList)[0]>();
  const columns = [
    helper.accessor("userNm", {
      cell: (info) => info.getValue(),
      header: t("userNm"),
    }),
    helper.accessor("deptNm", {
      cell: (info) => info.getValue(),
      header: t("deptNm"),
    }),
    helper.accessor("userId", {
      cell: (info) => info.getValue(),
      header: t("userId"),
    }),
    helper.accessor("mbtlnumEncpt", {
      cell: (info) => info.getValue(),
      header: t("mbtlnumEncpt"),
    }),
    helper.accessor("emailaddrEncpt", {
      cell: (info) => info.getValue(),
      header: t("emailaddrEncpt"),
    }),
    helper.accessor("menuAuthorList", {
      cell: (info) => {
        const list = info.getValue() as { menuNm: string }[];
        if (!list || list.length === 0) return "-";
        return (
          <div className="flex flex-col">
            {list.map((item, idx) => (
              <Badge
                variant="secondary"
                className="m-0.5 bg-blue-500 text-white dark:bg-blue-600"
                key={idx}
              >
                {item.menuNm}
              </Badge>
            ))}
          </div>
        );
      },
      header: t("menuAuthorList"),
    }),
    helper.display({
      id: "edit",
      header: () => "",
      cell: (info) => {
        const row = info.row.original;
        const initialGrants =
          (row.menuAuthorList as { authorCodeNm: string }[] | undefined)?.map(
            (m) => m.authorCodeNm,
          ) ?? [];

        const dialogRows: { label: string; value: React.ReactNode }[] = [
          { label: t("userNm"), value: row.userNm },
          { label: t("deptNm"), value: row.deptNm },
          { label: t("userId"), value: row.userId },
          { label: t("mbtlnumEncpt"), value: row.mbtlnumEncpt },
          { label: t("emailaddrEncpt"), value: row.emailaddrEncpt },
          {
            label: t("menuAuthorList"),
            value: (
              <div className="flex items-center justify-between gap-4">
                <span className="truncate">
                  {initialGrants.map((item, idx) => (
                    <Badge
                      variant="secondary"
                      className="m-0.5 bg-blue-500 text-white dark:bg-blue-600"
                      key={idx}
                    >
                      {item}
                    </Badge>
                  ))}
                </span>
                <AdminPopover
                  trigger={
                    <button className="rounded border px-2 py-0.5">+</button>
                  }
                  items={menuList}
                  defaultSelected={initialGrants}
                  onAdd={(selected) => {
                    console.log("selected for", row.userId, selected);
                  }}
                  side="right"
                  align="end"
                  sideOffset={140}
                  alignOffset={-40}
                  title="추가 권한 목록"
                />
              </div>
            ),
          },
        ];

        return (
          <AdminDialog
            title="권한 정보"
            rows={dialogRows}
            onSubmit={() => {
              console.log("submit for", row.userId);
            }}
            trigger={<Button variant="outline">정보 수정</Button>}
            buttonTitle="권한 부여"
          />
        );
      },
    }),
  ];

  return (
    <div className="flex w-full max-w-[1500px] flex-col overflow-hidden overflow-y-auto">
      <div className="mt-5">
        <ViewTable data={data.result.resultList} columns={columns} pinHeader />
      </div>
      {data.result.pageInfo && (
        <Pagination
          type="server"
          pageInfo={data.result.pageInfo}
          onPageNoChange={setPageIndex}
          onNumOfRowsChange={(newPageSize) => {
            setPageSize(newPageSize);
            setPageIndex(1);
          }}
          isLoading={isLoading}
        />
      )}
    </div>
  );
}
