import { BaseLayerInfo, LayerService } from "@geon-map/react-odf";
import {
  BasemapItem,
  BasemapListRequest,
  BasemapListResponse,
  defaultGeonSmtClient,
} from "@geon-query/model";

// 🔄 레이어 타입 판별 함수 (UI 분류용)
function determineLayerType(
  lyrStleCodeNm: string,
): "base" | "overlay" | "hybrid" {
  const styleName = lyrStleCodeNm?.toLowerCase() || "";

  if (styleName === "hybrid") {
    return "hybrid";
  }

  // 추후 다른 오버레이 타입들 추가 가능
  // if (styleName.includes('overlay') || styleName.includes('label')) {
  //   return 'overlay';
  // }

  return "base";
}

// 🔧 ODF 서비스 타입 결정 함수 (기술적 설정용)
function determineServiceType(
  lyrStleCode: string,
  lyrStleCodeNm: string,
  layerParams: any,
): LayerService {
  // 1. layerParams에 service가 명시되어 있으면 우선 사용
  if (layerParams?.service) {
    return layerParams.service;
  }

  // 2. lyrStleCode 기반 매핑 (코드 우선)
  const code = lyrStleCode?.toUpperCase() || "";
  if (code === "W") return "wms";
  if (code === "F") return "wfs";
  if (code === "T") return "wmts";

  // 3. lyrStleCodeNm 기반 매핑 (이름 기반)
  const codeName = lyrStleCodeNm?.toLowerCase() || "";
  if (codeName.includes("wms")) return "wms";
  if (codeName.includes("wfs")) return "wfs";
  if (codeName.includes("wmts")) return "wmts";

  // 4. 특수 케이스: Hybrid는 UI 분류용이지만 기술적으로는 wmts 사용
  if (codeName === "hybrid") return "wmts";

  // 5. 기본값 (대부분의 배경지도는 wmts)
  return "wmts";
}

// 🔄 API 데이터를 BaseLayerInfo로 변환하는 함수 (앱 레벨에서 구현)
function convertApiToBaseLayerInfo(apiBasemap: BasemapItem): BaseLayerInfo {
  try {
    // mapUrlparamtr JSON 파싱
    const layerParams = apiBasemap.mapUrlparamtr
      ? JSON.parse(apiBasemap.mapUrlparamtr)
      : {};

    // 🎯 레이어 타입 판별
    const layerType = determineLayerType(apiBasemap.lyrStleCodeNm);

    return {
      id: apiBasemap.bcrnMapId, // bcrnMapId를 고유 ID로 사용
      name: apiBasemap.bcrnMapNm,
      category: apiBasemap.bcrnMapClCodeNm || "사용자정의",
      thumbnail: apiBasemap.base64, // base64 이미지 (없으면 undefined)

      // 🆕 새로운 필드들
      layerType,

      layerParams: {
        ...layerParams,
        service: determineServiceType(
          apiBasemap.lyrStleCode,
          apiBasemap.lyrStleCodeNm,
          layerParams,
        ),
        server: apiBasemap.mapUrl,
        layer: layerParams.layer,
      },
    };
  } catch (parseError) {
    console.warn(
      `Failed to parse layer params for ${apiBasemap.bcrnMapNm}:`,
      parseError,
    );

    // 파싱 실패 시 기본 구조로 생성
    const layerType = determineLayerType(apiBasemap.lyrStleCodeNm);

    return {
      id: apiBasemap.bcrnMapId,
      name: apiBasemap.bcrnMapNm,
      category: apiBasemap.bcrnMapClCodeNm || "사용자정의",
      thumbnail: apiBasemap.base64,

      // 🆕 새로운 필드들
      layerType,

      layerParams: {
        service: determineServiceType(
          apiBasemap.lyrStleCode,
          apiBasemap.lyrStleCodeNm,
          {},
        ),
        server: apiBasemap.mapUrl,
        layer: apiBasemap.bcrnMapNm,
      },
    };
  }
}

// 📋 API 응답 배열을 BaseLayerInfo 배열로 변환 (앱 레벨에서 구현)
function convertApiArrayToBaseLayers(
  apiBasemapList: BasemapItem[],
): BaseLayerInfo[] {
  return apiBasemapList
    .filter((item) => {
      // 사용 중인 배경지도만 + 필수 필드 검증
      return (
        item.bcrnMapUseAt === "Y" &&
        item.bcrnMapId &&
        item.bcrnMapNm &&
        item.mapUrl
      );
    })
    .map((apiBasemap) => {
      try {
        return convertApiToBaseLayerInfo(apiBasemap);
      } catch (convertError) {
        console.warn(
          `⚠️ Failed to convert basemap ${apiBasemap.bcrnMapNm}:`,
          convertError,
        );
        return null;
      }
    })
    .filter((layer): layer is BaseLayerInfo => layer !== null);
}

// 🔗 제네릭 배열 병합 유틸리티
export function mergeArrays<T>(arr1: T[], arr2: T[]): T[] {
  return [...arr1, ...arr2];
}
// 🆕 서버에서 API 데이터 페칭 (앱에서 직접 제어)
export async function getBaseLayers(): Promise<BaseLayerInfo[]> {
  try {
    // 🎯 클라이언트와 파라미터를 앱에서 직접 제어
    const data: BasemapListResponse = await defaultGeonSmtClient.basemap.list({
      pageIndex: 1,
      pageSize: 100,
      bcrnMapUseAt: "Y",
      imageAt: "N",
    } as BasemapListRequest);

    // API 응답 검증
    if (!data || data.code !== 200) {
      console.warn("⚠️ API response error:", data?.message || "Unknown error");
      return [];
    }

    if (!data?.result?.list || !Array.isArray(data.result.list)) {
      console.warn("⚠️ No base layers found in API response");
      return [];
    }

    const apiBaseLayers = convertApiArrayToBaseLayers(data.result.list);

    return apiBaseLayers;
  } catch (error) {
    console.error("❌ Failed to fetch API base layers:", error);
    console.warn("⚠️ Using only default base layers");
    return [];
  }
}
