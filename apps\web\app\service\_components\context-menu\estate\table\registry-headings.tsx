"use client";

import type {
  APIRequestType,
  APIResponseType,
  EstateClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Label } from "@geon-ui/react/primitives/label";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import { useFormatter, useTranslations } from "next-intl";
import React from "react";

export default function RegistryHeadings({
  client,
  ...props
}: APIRequestType<EstateClient["registry"]["headings"]> & {
  client: EstateClient;
}) {
  // message handler
  const t = useTranslations("estate.registry.headings");
  const f = useFormatter();

  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<EstateClient["registry"]["headings"]>
  >({
    queryKey: ["registry/headings", { ...props }],
    queryFn: () => client.registry.headings({ ...props }),
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError || !data || typeof data.result === "string")
    return (
      <div className="text-destructive flex size-full items-center justify-center">
        Error loading parcel data: {error as string}
        {data && `, ${data?.result as unknown as string}`}
      </div>
    );
  if (!data.result.resultList.length)
    return (
      <div className="flex size-full items-center justify-center">
        등록된 건축물 대장이 없습니다.
      </div>
    );

  return (
    <div className="size-full overflow-hidden overflow-y-auto">
      <div className="grid grid-cols-4">
        <div className="grid border p-1">
          <Label className="text-muted-foreground text-xs">건물 ID</Label>-
        </div>
        <div className="grid border border-l-0 p-1">
          <Label className="text-muted-foreground text-xs">고유번호</Label>
          {`${props.pnu.slice(0, 10)}-${data.result.resultList[0].regstrGbCd}-${props.pnu.slice(11, -1)}`}
        </div>
        <div className="grid border border-l-0 p-1">
          <Label className="text-muted-foreground text-xs">{t("bldNm")}</Label>
          {(data.result.resultList[0].bldNm as string).length > 0
            ? data.result.resultList[0].bldNm
            : "-"}
        </div>
        <div className="grid border border-l-0 p-1">
          <Label className="text-muted-foreground text-xs">{`${t("hoCnt")}/${t("fmlyCnt")}/${t("hhldCnt")}`}</Label>
          {`${data.result.resultList[0].hoCnt}호/${data.result.resultList[0].fmlyCnt}가구/${data.result.resultList[0].hhldCnt}세대`}
        </div>
      </div>
      <div className="grid grid-cols-5">
        <div className="col-span-2 grid border border-t-0 p-1">
          <Label className="text-muted-foreground text-xs">
            {t("platPlc")}
          </Label>
          {data.result.resultList[0].platPlc}
        </div>
        <div className="grid border border-l-0 border-t-0 p-1">
          <Label className="text-muted-foreground text-xs">지번</Label>
          {`${data.result.resultList[0].bun}-${data.result.resultList[0].ji}`}
        </div>
        <div className="col-span-2 grid border border-l-0 border-t-0 p-1">
          <Label className="text-muted-foreground text-xs">
            {t("newPlatPlc")}
          </Label>
          {data.result.resultList[0].newPlatPlc}
        </div>
      </div>
      <div className="grid grid-cols-5">
        <div className="grid border border-t-0 p-1">
          <Label className="text-muted-foreground text-xs">
            {t("platArea")}
          </Label>
          {f.number(parseFloat(data.result.resultList[0].platArea))}
        </div>
        <div className="grid border border-l-0 border-t-0 p-1">
          <Label className="text-muted-foreground text-xs">
            {t("totArea")}
          </Label>
          {f.number(parseFloat(data.result.resultList[0].totArea))}
        </div>
        <div className="grid border border-l-0 border-t-0 p-1">
          <Label className="text-muted-foreground text-xs">지역</Label>-
        </div>
        <div className="grid border border-l-0 border-t-0 p-1">
          <Label className="text-muted-foreground text-xs">지구</Label>-
        </div>
        <div className="grid border border-l-0 border-t-0 p-1">
          <Label className="text-muted-foreground text-xs">구역</Label>-
        </div>
      </div>
      <div className="grid grid-cols-5">
        <div className="grid border border-t-0 p-1">
          <Label className="text-muted-foreground text-xs">
            {t("archArea")}
          </Label>
          {f.number(parseFloat(data.result.resultList[0].archArea))}
        </div>
        <div className="grid border border-l-0 border-t-0 p-1">
          <Label className="text-muted-foreground text-xs">
            {t("vlRatEstmTotArea")}
          </Label>
          {f.number(parseFloat(data.result.resultList[0].vlRatEstmTotArea))}
        </div>
        <div className="grid border border-l-0 border-t-0 p-1">
          <Label className="text-muted-foreground text-xs">
            {t("strctCdNm")}
          </Label>
          {data.result.resultList[0].strctCdNm}
        </div>
        <div className="grid border border-l-0 border-t-0 p-1">
          <Label className="text-muted-foreground text-xs">
            {t("mainPurpsCdNm")}
          </Label>
          {data.result.resultList[0].mainPurpsCdNm}
        </div>
        <div className="grid border border-l-0 border-t-0 p-1">
          <Label className="text-muted-foreground text-xs">층수</Label>
          {`지하: ${f.number(parseInt(data.result.resultList[0].ugrndFlrCnt))} 층, 지상: ${f.number(parseInt(data.result.resultList[0].grndFlrCnt))} 층`}
        </div>
      </div>
      <div className="grid grid-cols-5">
        <div className="grid border border-t-0 p-1">
          <Label className="text-muted-foreground text-xs">{t("bcRat")}</Label>
          {f.number(parseFloat(data.result.resultList[0].bcRat))}
        </div>
        <div className="grid border border-l-0 border-t-0 p-1">
          <Label className="text-muted-foreground text-xs">{t("vlRat")}</Label>
          {f.number(parseFloat(data.result.resultList[0].vlRat))}
        </div>
        <div className="grid border border-l-0 border-t-0 p-1">
          <Label className="text-muted-foreground text-xs">{t("heit")}</Label>
          {f.number(parseInt(data.result.resultList[0].heit))}
        </div>
        <div className="col-span-2 grid grid-cols-3">
          <div className="grid border border-l-0 border-t-0 p-1">
            <Label className="text-muted-foreground text-xs">
              {t("roofCdNm")}
            </Label>
            {data.result.resultList[0].roofCdNm}
          </div>
          <div className="col-span-2 grid border border-l-0 border-t-0 p-1 text-right">
            <Label className="text-muted-foreground text-xs">부속건축물</Label>
            {`${f.number(parseInt(data.result.resultList[0].atchBldCnt))} 동 ${f.number(parseFloat(data.result.resultList[0].atchBldArea))} ㎡`}
          </div>
        </div>
      </div>
    </div>
  );
}
