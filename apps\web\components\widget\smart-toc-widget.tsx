"use client";

import { useLayer } from "@geon-map/react-odf";
import { TOCWidget } from "@geon-map/react-ui/components";
import { TOCNode, TOCWidgetProps } from "@geon-map/react-ui/types";
import { cn } from "@geon-ui/react/utils";
import { useCallback, useMemo } from "react";

interface SmartTOCWidgetProps
  extends Omit<TOCWidgetProps, "data" | "onLayerVisibilityChange"> {
  className?: string;
  /** 필터링할 레이어 타입들 (기본적으로 draw, measure, clear 제외) */
  excludeLayerTypes?: string[];
  /** 추가 필터링 조건 */
  layerFilter?: (layer: any) => boolean;
}

/**
 * useLayer 상태를 직접 구독하는 스마트 TOC Widget
 *
 * 특징:
 * - Layer 컴포넌트로 마운트된 레이어들을 자동으로 표시
 * - TOC 데이터를 수동으로 전달할 필요 없음
 * - useLayer의 레이어 상태와 실시간 동기화
 */
export function SmartTOCWidget({
  className = "absolute left-5 top-20 flex max-h-[700px] min-h-[300px] w-[400px] flex-col",
  excludeLayerTypes = ["draw", "measure", "clear"],
  layerFilter,
  ...tocOptions
}: SmartTOCWidgetProps) {
  const { layers, setVisible, setOpacity } = useLayer();

  console.log("[SmartTOCWidget] Render", {
    layersCount: layers.length,
    excludeLayerTypes,
  });

  // useLayer 상태를 TOCNode 형식으로 변환
  const tocNodes = useMemo((): TOCNode[] => {
    if (!layers || layers.length === 0) return [];

    let filteredLayers = layers;

    // 기본 타입 필터링
    if (excludeLayerTypes.length > 0) {
      filteredLayers = filteredLayers.filter(
        (layer) => !excludeLayerTypes.includes(layer.type || ""),
      );
    }

    // 커스텀 필터 적용
    if (layerFilter) {
      filteredLayers = filteredLayers.filter(layerFilter);
    }

    // Layer를 TOCNode로 변환
    const tocNodes: TOCNode[] = filteredLayers.map((layer) => ({
      id: layer.id,
      name: layer.name || layer.id,
      type: "layer" as const,
      visible: layer.visible ?? true,
      opacity: layer.opacity ?? 1,
      layerId: layer.id, // ODF 레이어 ID
    }));

    console.log("[SmartTOCWidget] TOC nodes generated", {
      originalLayersCount: layers.length,
      filteredLayersCount: filteredLayers.length,
      tocNodesCount: tocNodes.length,
    });

    return tocNodes;
  }, [layers, excludeLayerTypes, layerFilter]);

  // 레이어 가시성 변경 처리
  const handleLayerVisibilityChange = useCallback(
    (layerId: string, visible: boolean) => {
      console.log("[SmartTOCWidget] Layer visibility changed", {
        layerId,
        visible,
      });
      setVisible(layerId, visible);
    },
    [setVisible],
  );

  // 레이어 투명도 변경 처리 (필요시 구현)
  const handleLayerOpacityChange = useCallback(
    (layerId: string, opacity: number) => {
      console.log("[SmartTOCWidget] Layer opacity changed", {
        layerId,
        opacity,
      });
      setOpacity(layerId, opacity);
    },
    [setOpacity],
  );

  return (
    <TOCWidget
      className={cn(className)}
      data={tocNodes}
      showHeader={false}
      isLayerOpacityEnabled={true}
      onLayerVisibilityChange={handleLayerVisibilityChange}
      {...tocOptions}
    />
  );
}
