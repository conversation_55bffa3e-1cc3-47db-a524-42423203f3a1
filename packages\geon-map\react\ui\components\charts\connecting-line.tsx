interface ConnectingLineProps {
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  xAxisMap: any;
  yAxisMap: any;
  text?: string;
}
// 선택지점 연결선
export default function ConnectingLineComponent({
  startX,
  startY,
  endX,
  endY,
  xAxisMap,
  yAxisMap,
  text,
}: ConnectingLineProps) {
  const LineComponent = () => {
    if (!xAxisMap || !yAxisMap) return null;
    const xAxis = Object.values(xAxisMap)[0] as any;
    const yAxis = Object.values(yAxisMap)[0] as any;
    const xScale = xAxis.scale;
    const yScale = yAxis.scale;

    const x1 = xScale(startX);
    const y1 = yScale(startY);
    const x2 = xScale(endX);
    const y2 = yScale(endY);

    const midX = (x1 + x2) / 2;
    const midY = (y1 + y2) / 2;

    return (
      <>
        <line
          x1={x1}
          y1={y1}
          x2={x2}
          y2={y2}
          stroke="#a268aaff"
          strokeWidth={1}
        />
        {text && (
          <text
            x={midX}
            y={midY + 30}
            textAnchor="middle"
            fontSize={16}
            fontWeight="bold"
          >
            {text}
          </text>
        )}
      </>
    );
  };

  return <LineComponent />;
}
