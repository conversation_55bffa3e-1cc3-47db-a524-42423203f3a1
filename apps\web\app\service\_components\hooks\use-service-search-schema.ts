﻿"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";

import {
  type ApiSchemaResponse,
  buildSearchSchema,
  type ClientSearchSchema,
  fetchApiSchema,
  getAvailableFields,
  getClientSchema,
  type ServiceSearchSchema,
} from "@/app/service/_components/dynamic-search";
import {
  type ActiveField,
  type AvailableField,
} from "@/app/service/_components/dynamic-search/dynamic-filter-manager";

type UseServiceSearchSchemaResult = {
  schema: ServiceSearchSchema | null;
  schemaLoading: boolean;
  schemaError: string | null;
  availableFields: AvailableField[];
  activeFieldsForUI: ActiveField[];
  handleAddField: (fieldId: string) => Promise<void>;
  handleRemoveField: (fieldId: string) => Promise<void>;
};

export function useServiceSearchSchema(
  serviceId?: string | null,
): UseServiceSearchSchemaResult {
  const [schema, setSchema] = useState<ServiceSearchSchema | null>(null);
  const [schemaLoading, setSchemaLoading] = useState(false);
  const [schemaError, setSchemaError] = useState<string | null>(null);

  const [activeFieldIds, setActiveFieldIds] = useState<string[]>([]);
  const [availableFields, setAvailableFields] = useState<AvailableField[]>([]);
  const [apiSchemaData, setApiSchemaData] = useState<ApiSchemaResponse | null>(
    null,
  );
  const [clientSchemaData, setClientSchemaData] =
    useState<ClientSearchSchema | null>(null);

  useEffect(() => {
    let isCancelled = false;

    async function loadSchema() {
      if (!serviceId) {
        setSchema(null);
        setSchemaError(null);
        setActiveFieldIds([]);
        setAvailableFields([]);
        setApiSchemaData(null);
        setClientSchemaData(null);
        setSchemaLoading(false);
        return;
      }

      setSchemaLoading(true);
      setSchemaError(null);

      try {
        const apiSchema = await fetchApiSchema(serviceId);
        if (isCancelled) return;

        const clientSchema = getClientSchema(serviceId);
        if (isCancelled) return;

        setApiSchemaData(apiSchema);
        setClientSchemaData(clientSchema);

        const defaultFields = [
          ...apiSchema.searchField,
          ...clientSchema.defaultFields,
        ];
        setActiveFieldIds(defaultFields);

        const finalSchema = await buildSearchSchema(
          apiSchema,
          clientSchema,
          defaultFields,
        );
        if (!isCancelled) {
          setSchema(finalSchema);
        }

        const availableFieldsList = await getAvailableFields(
          apiSchema,
          clientSchema,
          defaultFields,
        );
        if (!isCancelled) {
          setAvailableFields(availableFieldsList);
        }
      } catch (error) {
        if (!isCancelled) {
          const errorMessage =
            error instanceof Error ? error.message : "Schema loading failed";
          setSchemaError(errorMessage);
          console.error("Schema loading error:", error);
          setSchema(null);
          setAvailableFields([]);
          setActiveFieldIds([]);
        }
      } finally {
        if (!isCancelled) {
          setSchemaLoading(false);
        }
      }
    }

    loadSchema();

    return () => {
      isCancelled = true;
    };
  }, [serviceId]);

  const handleAddField = useCallback(
    async (fieldId: string) => {
      if (!apiSchemaData || !clientSchemaData) return;

      const newActiveFieldIds = [...activeFieldIds, fieldId];
      setActiveFieldIds(newActiveFieldIds);

      const updatedSchema = await buildSearchSchema(
        apiSchemaData,
        clientSchemaData,
        newActiveFieldIds,
      );
      setSchema(updatedSchema);

      const updatedAvailableFields = await getAvailableFields(
        apiSchemaData,
        clientSchemaData,
        newActiveFieldIds,
      );
      setAvailableFields(updatedAvailableFields);
    },
    [activeFieldIds, apiSchemaData, clientSchemaData],
  );

  const handleRemoveField = useCallback(
    async (fieldId: string) => {
      if (!apiSchemaData || !clientSchemaData) return;

      const newActiveFieldIds = activeFieldIds.filter((id) => id !== fieldId);
      setActiveFieldIds(newActiveFieldIds);

      const updatedSchema = await buildSearchSchema(
        apiSchemaData,
        clientSchemaData,
        newActiveFieldIds,
      );
      setSchema(updatedSchema);

      const updatedAvailableFields = await getAvailableFields(
        apiSchemaData,
        clientSchemaData,
        newActiveFieldIds,
      );
      setAvailableFields(updatedAvailableFields);
    },
    [activeFieldIds, apiSchemaData, clientSchemaData],
  );

  const activeFieldsForUI = useMemo<ActiveField[]>(() => {
    if (!schema || !clientSchemaData) return [];

    return schema.fields.map((field) => ({
      id: field.id,
      label: field.label,
      removable: !clientSchemaData.defaultFields.includes(field.id),
    }));
  }, [schema, clientSchemaData]);

  return {
    schema,
    schemaLoading,
    schemaError,
    availableFields,
    activeFieldsForUI,
    handleAddField,
    handleRemoveField,
  };
}
