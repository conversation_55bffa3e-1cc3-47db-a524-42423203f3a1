import React from "react";

import { formatDate } from "@/app/board/_utils";

export type FormState = {
  nttSj: string;
  nttCn: string;
  updusrId: string;
  registerId: string;
  upperExpsrAt: boolean;
  smsSndngAt: boolean;
  othbcAt: boolean;
  pstgBeginDt: string;
  linkUrl: string;
  popupAt: string | boolean;
  popupBeginDt: string;
  popupEndDt: string;
  popupPortalExpsrAt: boolean;
  popupInsttExpsrAt: boolean;
};

export default function View({ form }: { form: FormState }) {
  const detailFields = [
    { label: "제목", value: form.nttSj },
    { label: "등록자", value: form.registerId },
    { label: "팝업 사용", value: form.popupAt ? "Y" : "N" },
    {
      label: "팝업 시작",
      value: form.popupBeginDt ? formatDate(form.popupBeginDt) : "-",
    },
    {
      label: "팝업 종료",
      value: form.popupEndDt ? formatDate(form.popupEndDt) : "-",
    },
  ];

  return (
    <div className="space-y-4">
      <div className="rounded-md border bg-white p-4">
        <div className="grid grid-cols-2 gap-3 text-sm">
          {detailFields.map((f) => (
            <div key={f.label}>
              <span className="mr-2 font-medium">{f.label}</span>
              {f.value}
            </div>
          ))}
        </div>
      </div>

      <div className="rounded-md border bg-white p-4">
        <h2 className="mb-3 text-base font-semibold">내용</h2>
        <div className="prose max-w-none whitespace-pre-wrap">{form.nttCn}</div>
      </div>
    </div>
  );
}
