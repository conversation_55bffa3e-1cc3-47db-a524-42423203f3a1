"use client";

import { <PERSON><PERSON> } from "@geon-ui/react/primitives/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@geon-ui/react/primitives/form";
import { Input } from "@geon-ui/react/primitives/input";
import { Search } from "lucide-react";
import React from "react";
import { useForm } from "react-hook-form";

export type SearchOption = {
  label: string;
  value: string;
};

interface DynamicSearchFormProps<T extends Record<string, any>> {
  options: SearchOption[];
  params: T;
  setParams: React.Dispatch<React.SetStateAction<T>>;
  defaultField?: string;
}

export default function SearchForm<T extends Record<string, any>>({
  options,
  params,
  setParams,
}: DynamicSearchFormProps<T>) {
  const [selectedField, setSelectedField] = React.useState<string>("");

  const form = useForm<{ keyword: string }>({
    defaultValues: { keyword: "" },
  });

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) => {
          const newParams = Object.keys(params).reduce(
            (acc, key) => {
              acc[key] = "";
              return acc;
            },
            {} as Record<string, any>,
          );

          setParams({
            ...newParams,
            [selectedField]: data.keyword,
          } as T);
        })}
        className="flex items-end gap-4 rounded-lg bg-white p-4 shadow"
      >
        <FormItem className="w-[200px]">
          <FormControl>
            <select
              value={selectedField}
              onChange={(e) => setSelectedField(e.target.value)}
              className="w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="" disabled>
                분류
              </option>
              {options.map((opt) => (
                <option key={opt.value} value={opt.value}>
                  {opt.label}
                </option>
              ))}
            </select>
          </FormControl>
        </FormItem>
        <FormField
          control={form.control}
          name="keyword"
          render={({ field }) => (
            <FormItem className="max-w-[500px] flex-1">
              <FormControl>
                <Input
                  {...field}
                  placeholder={`검색어를 입력하세요`}
                  className="w-full"
                />
              </FormControl>
            </FormItem>
          )}
        />
        <Button
          type="submit"
          className="rounded-md bg-blue-600 px-6 py-2 text-white hover:bg-blue-700"
        >
          <Search className="mr-2 h-4 w-4" />
          검색
        </Button>
      </form>
    </Form>
  );
}
