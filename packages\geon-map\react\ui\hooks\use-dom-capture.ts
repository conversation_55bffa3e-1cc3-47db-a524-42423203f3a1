import { toPng } from "html-to-image";
import { RefObject } from "react";

export function useDomCapture(targetRef: RefObject<HTMLDivElement | null>) {
  // 다운로드
  const download = async (fileName: string) => {
    if (targetRef.current === null) return;

    const dataUrl = await toPng(targetRef.current, {
      backgroundColor: "#fff",
      style: { paddingBottom: "40px", overflow: "visible" },
      cacheBust: true,
    });
    const link = document.createElement("a");
    link.href = dataUrl;
    link.download = fileName;
    link.click();
  };

  // 인쇄
  const print = () => {
    if (!targetRef.current) return;

    const printWindow = window.open(
      "",
      "_blank",
      "width=800,height=600,left=100,top=100,toolbar=no,scrollbars=yes,resizable=yes",
    );
    if (printWindow) {
      printWindow.document.write(targetRef.current.outerHTML);
      printWindow.document.close();
      printWindow.onafterprint = () => {
        printWindow.close();
      };
      printWindow.print();
    }
  };

  return { download, print };
}
